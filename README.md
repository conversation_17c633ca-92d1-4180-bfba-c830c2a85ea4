# DataScope 前端项目

本项目是DataScope数据管理平台的Web前端，使用Vue 3和TypeScript开发。

## 项目简介
DataScope 是一个用于数据库查询和分析的工具，本项目是其前端部分，基于Vue 3和TypeScript开发。

## 技术栈

## 项目结构

```
- src/
  - assets/        # 静态资源
  - components/    # 公共组件
  - composables/   # 可组合函数
  - config/        # 配置文件
  - layouts/       # 布局组件
    - main/        # 主布局系统组件
  - mock/          # Mock服务
  - router/        # 路由配置
  - services/      # API服务
  - stores/        # 状态管理
  - types/         # TypeScript类型
  - utils/         # 工具函数
  - views/         # 页面视图
- config/          # 项目配置目录
  - env/           # 环境配置文件
  - typescript/    # TypeScript配置文件
- tests/           # 测试目录
  - e2e/           # 端到端测试
  - unit/          # 单元测试
- docs/            # 项目文档
- public/          # 静态资源目录
```

## 项目优化

最近，项目经过了以下优化：

### 1. 文件结构优化
- 创建了独立的`config`目录，集中管理项目配置
  - `config/env/`: 环境配置文件和示例
  - `config/typescript/`: TypeScript配置文件
- 移除了冗余的备份文件和临时文件
- 统一了测试目录结构，所有测试文件都在`tests`目录下

### 2. 环境配置优化
- 提供了标准的环境配置示例和模板
- 统一了环境变量命名和管理方式
- 添加了详细的配置文档和说明

### 3. 代码模块化
- 将`QueryEditor.vue`组件拆分为多个更小的组件：
  - `QueryBasicInfo.vue` - 管理查询基本信息
  - `QueryEditorHeader.vue` - 头部导航和操作按钮
  - `QueryEditorTabs.vue` - 查询类型标签页管理

### 4. 功能抽象
- 创建了以下可组合函数（Composables）：
  - `useQueryVersion.ts` - 管理查询版本相关逻辑
  - `useQueryExecution.ts` - 执行查询的逻辑
  - `useQuerySave.ts` - 保存查询的逻辑

### 5. 类型系统增强
- 创建了类型定义文件：
  - `types/queryEditor.ts` - 查询编辑器相关类型
  - `types/version.ts` - 版本管理相关类型

### 6. 问题修复
- 解决了重复定义`handleSaveQuery`函数的问题
- 修复了`selectedDataSource`可能为null导致的类型错误
- 改进了状态管理，使用computed属性管理派生状态

### 7. API层优化
- 创建了版本管理API模块`api/version.ts`
- 创建了数据源API模块`api/dataSource.ts`

这些改进大幅提升了代码可维护性，使代码结构更清晰，减少了重复代码，并强化了类型安全。

## 新增功能：可组合函数(Composables)

为了提高代码复用性和模块化，项目新增了`src/composables/`目录，存放所有可组合函数。目前实现的可组合函数包括：

- `useQueryEditorState`: 管理查询编辑器的状态和计算属性
- `useQuerySave`: 处理查询保存相关的逻辑
- `useQueryExecution`: 封装查询执行的功能
- `useQueryVersion`: 管理查询版本相关的功能，包括草稿保存、版本发布和版本加载

这些可组合函数可以被多个组件使用，大大提高了代码的复用性和可维护性。

## 新增功能：查询组件模块化

为了提高代码的可维护性和复用性，查询编辑器已进行模块化改造，主要新增组件包括：

- `QueryBasicInfo`: 管理查询基本信息的组件，包括查询名称和数据源选择
- `QueryEditorHeader`: 查询编辑器头部组件，包含返回、保存和发布按钮
- `QueryEditorTabs`: 查询编辑器标签页组件，管理SQL和自然语言查询切换

这些组件可以被复用于其他页面，大大提高了代码的模块化程度和开发效率。

## 类型系统增强

为了提高代码的类型安全性，项目新增了以下类型定义文件：

- `src/types/queryEditor.ts`: 专门定义查询编辑器相关的类型
- `src/types/version.ts`: 定义查询版本管理相关的类型

这些类型定义使得代码更加健壮，减少了运行时错误的可能性。

## 主要功能模块

### 查询执行与版本管理

- 查询版本管理系统
  - 支持查询版本的创建、发布和废弃
  - 自动保存查询草稿
  - 支持版本切换和比较
  - 确保版本ID在查询执行中始终正确传递
- 查询执行引擎优化
  - 改进错误处理和异常管理
  - 支持将查询结果导出为多种格式
  - 查询执行状态实时更新

### 查询参数管理

- 支持丰富的参数类型：字符串、数字、日期、选择器等
- 参数条件拖拽排序
- 参数配置（默认值、验证规则、占位符等）
- 参数条件验证（必填、长度限制、数值范围、正则表达式、日期范围）

### 表格数据展示

- 表格列配置（可见性、宽度、排序、过滤）
- 表格列配置（自定义格式化、样式设置）
- 表格行操作按钮配置
  - 支持按钮图标、样式自定义
  - 支持确认提示配置
  - 支持条件显示（始终、表达式、权限）
  - 完整的编辑界面，含所有配置选项
- 表格批量操作按钮配置
  - 支持最小选择数量限制
  - 支持按钮图标、样式自定义
  - 支持确认提示配置
  - 完整的编辑界面，含所有配置选项
- 表格数据导出配置

### 图表数据可视化

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

### 生产环境构建

```bash
npm run build
```

### 配置环境变量

1. 复制`.env.example`文件并重命名为`.env.development`（开发环境）或`.env.production`（生产环境）
2. 根据需要修改配置项
3. 启动项目或构建时会自动应用对应环境的配置

## 功能特性

- 基于 Vue 3 和 TypeScript 的现代 Web 应用
- 数据源管理：支持多种数据库类型的连接和管理
- 查询管理：编写、测试和保存查询
- 集成管理：将查询结果可视化为表格或图表
  - 新增一键发布到低代码平台功能，实现自动生成低代码页面
  - 支持将集成转换为低代码格式并直接发布到平台
  - 提供页面创建后的一键预览功能，方便用户查看效果
- 权限管理：基于角色的权限控制
- 主题定制：支持亮/暗主题和品牌色定制

## 最新改进

- **项目结构优化**：
  - 新增配置目录，集中管理环境和TypeScript配置
  - 优化文件组织，清理冗余文件，提高可维护性
  - 统一测试目录结构，提供更清晰的测试组织方式

- **低代码平台认证优化**：
  - 优化低代码平台认证机制，将固定token改为从localStorage动态获取
  - 新增getLowCodeAuthToken函数统一管理token获取逻辑，提高代码可维护性
  - 增强代理服务配置，支持动态token验证与错误处理
  - 完善日志记录，便于问题排查与监控
  - 解决token过期导致的低代码平台操作失败问题

- **数据源和表单交互体验优化**：
  - 修复数据源表单验证交互问题，将实时验证改为提交时验证
  - 优化发布流程，增加超时时间确保验证流程有足够时间完成
  - 修复数据源下拉列表不显示问题和数据预览翻页不生效问题
  - 修复系统集列表页面select默认值不显示问题
  - 优化按钮状态显示，增强视觉反馈
  - 禁用图表模式的展示，修复选项卡文字与枚举不对应问题

- **低代码平台集成增强**：
  - 完善发布到低代码平台功能，显示页面CODE便于追踪
  - 修复发布前显示导出JSON与预览功能按钮的问题
  - 确保页面发布和验证流程更加稳定可靠

- **类型系统修复**：
  - 修复TypeScript类型错误，增强代码健壮性
  - 修正Vue API导入路径，统一使用项目内vue-types-fix模块
  - 为回调函数参数添加显式类型注解，提高类型安全性

- **子系统功能迁移与主系统整合**：
  - 将页面服务系统`(page-service-system)`的核心功能迁移到主系统
  - 原`page-service-system/src/pages/editor/PageEditor.vue`文件迁移到主系统中的`src/views/pages/PageEdit.vue`
  - 统一操作按钮布局，将表单操作按钮均放置在页面顶部，提供一致的用户体验
  - 改进编辑页面的UI布局，与系统集成页面保持统一风格
  - 简化页面服务系统和主系统的通信方式，减少跨系统调用

- **系统页面列表功能增强**：
  - 优化系统页面列表操作按钮，完善功能和交互体验
  - 添加嵌入功能，支持页面外部嵌入和代码复制
  - 新增操作下拉菜单，整合更多功能并优化界面布局
  - 改进视觉反馈，增加悬停效果和交互提示
  - 统一操作按钮风格，保持一致的用户体验

- **选项列表枚举管理**：
  - 新增选项列表保存为系统枚举的功能，提高选项复用效率
  - 支持从系统枚举加载选项列表，避免重复录入相同选项
  - 增强枚举选项搜索功能，支持按项目代码和关键词过滤
  - 优化选项编辑体验，支持自动生成枚举名称和编码
  - 完整的枚举管理API服务，支持创建、更新、查询和删除操作

- **预览功能优化**：
  - 移除原有自定义预览代码，简化预览流程
  - 重构预览按钮功能，统一跳转到低代码平台
  - 实现发布成功后的自动预览链接保存
  - 支持集成列表页和编辑页的预览按钮直接打开低代码平台

- **集成低代码发布功能增强**：
  - 优化低代码格式转换流程，增强数据验证和错误处理
  - 新增页面存在性检查机制，支持更新已存在的低代码页面
  - 解决重复发布同一集成导致的"页面标识不可重复"错误
  - 改进参数编码处理，确保复杂内容正确传递到低代码平台
  - 完善日志记录，便于跟踪问题和调试

- **集成JSON导出功能增强**：
  - 编辑页面导出的JSON现在使用标准配置格式，包含完整的filter和list字段
  - 列表页面下载标准JSON功能确保包含完整的查询条件和结果数据
  - 改进JSON编码方式，解决特殊字符显示异常问题
  - 导出文件名含"standard"标识，便于区分

- **表单验证优化**：
  - 提升表单验证和错误处理流程
  - 优化错误提示，帮助用户快速定位问题
  - 更详细的日志输出，便于开发人员排查问题

- **前端数据流优化**：
  - 增强本地数据持久化机制，解决后端数据不完整的临时方案
  - 改进集成编辑页面的加载和保存逻辑，提供更好的恢复机制
  - 优化错误处理，提供更详细的错误信息和日志记录
  - 列表页面下载功能增加数据完整性检查和验证

## 依赖及版本

- Vue: v3.3.x
- TypeScript: v5.x
- Vue Router: v4.x
- Pinia: v2.x
- Axios: v1.x
- Element Plus: v2.x
