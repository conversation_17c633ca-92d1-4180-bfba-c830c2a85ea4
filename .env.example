# 环境配置示例
# 将此文件复制为.env、.env.development、.env.test或.env.production并填写适当的值

# API相关配置
VITE_API_BASE_URL=                # API基础URL，例如：/data-scope
VITE_USE_MOCK_API=false           # 是否使用模拟API，开发时可设为true

# 低代码平台域名配置
VITE_LOWCODE_DOMAIN=              # 低代码平台域名，例如：qaboss.yeepay.com（测试）或 boss.yeepay.com（生产）
                                  # 如果不设置，则根据构建环境自动判断

# 页面服务系统配置
VITE_PAGE_SERVICE_PORT=3002       # 页面服务系统端口（仅开发环境需要）
VITE_PAGE_SERVICE_PATH=           # 页面服务系统路径，例如：/page-service-system

# 日志级别
VITE_LOG_LEVEL=info               # 日志级别：debug, info, warn, error