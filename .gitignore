# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Cache and temp files
.temp/
.temp/**/*
temp-error-info.txt
temp-*.txt

# Test files
test-results/
/tests/e2e/screenshots/

# Vite specific
vite.config.ts.timestamp-*
*.timestamp-*
*.timestamp-*.mjs
changes.txt

# Environment files - only ignore backups and local overrides
.env.development.backup
.env.backup
.env.*.local

# Keep these files
!.env
!.env.development
!.env.production

# Custom scripts and temp files
push.sh
push_output.txt
**/**.vue1
**/**.bak
