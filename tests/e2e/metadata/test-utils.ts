import type { Page } from '@playwright/test';
import { MetadataTestConfig } from './metadata.config';

/**
 * 元数据测试工具函数
 */
export class MetadataTestUtils {
  /**
   * 获取可用的数据源ID
   * @param page Playwright页面
   * @returns 数据源ID或undefined
   */
  static async getAvailableDataSourceId(page: Page): Promise<string | undefined> {
    try {
      const url = `${MetadataTestConfig.environment.baseUrl}/datasource`;
      console.log(`访问数据源列表页面: ${url}`);
      
      // 访问数据源列表
      await page.goto(url);
      console.log('页面加载完成，等待加载指示器消失');
      
      await this.waitForLoadingToDisappear(page);
      console.log('加载指示器已消失');
      
      // 检查是否有数据源
      const dataSourceList = page.locator(MetadataTestConfig.selectors.dataSourceList);
      const dataSourceCount = await dataSourceList.count();
      console.log(`找到 ${dataSourceCount} 个数据源`);
      
      if (dataSourceCount > 0) {
        console.log('尝试点击第一个数据源');
        // 点击第一个数据源
        await dataSourceList.first().click();
        await this.waitForLoadingToDisappear(page);
        
        // 从URL获取数据源ID
        const currentUrl = page.url();
        console.log(`当前URL: ${currentUrl}`);
        
        const match = currentUrl.match(/\/datasource\/([^\/]+)/);
        const dataSourceId = match ? match[1] : undefined;
        console.log(`提取的数据源ID: ${dataSourceId}`);
        
        return dataSourceId;
      } else {
        console.log('未找到任何数据源');
      }
    } catch (e) {
      console.error('获取数据源ID失败:', e);
    }
    
    return undefined;
  }
  
  /**
   * 等待加载指示器消失
   * @param page Playwright页面
   * @param timeout 超时时间（毫秒）
   */
  static async waitForLoadingToDisappear(page: Page, timeout = MetadataTestConfig.timeouts.element): Promise<void> {
    const loadingSelector = MetadataTestConfig.selectors.loadingIndicator;
    await page.waitForSelector(loadingSelector, { state: 'detached', timeout }).catch(() => {
      console.log(`等待加载指示器消失超时: ${loadingSelector}`);
    });
    
    // 确保页面稳定
    await page.waitForTimeout(MetadataTestConfig.timeouts.debounce);
  }
  
  /**
   * 截取测试截图
   * @param page Playwright页面
   * @param name 截图名称
   */
  static async takeScreenshot(page: Page, name: string): Promise<void> {
    await page.screenshot({
      path: `tests/e2e/screenshots/metadata-${name}-${Date.now()}.png`
    });
  }
  
  /**
   * 清理测试数据
   * 注意：此处是一个占位函数，实际实现需要根据项目情况调整
   */
  static async cleanUpTestData(page: Page, dataSourceId: string): Promise<void> {
    console.log(`清理测试数据: ${dataSourceId}`);
    // 实际清理逻辑
  }
} 