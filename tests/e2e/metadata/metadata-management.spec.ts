import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';
import { faker } from '@faker-js/faker/locale/zh_CN';
import { MetadataTestUtils } from './test-utils';
import { MetadataTestConfig } from './metadata.config';

interface BehaviorResult {
  success: boolean;
  reason?: string;
  resultCount?: number;
  hasResults?: boolean;
}

class MetadataTestError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'MetadataTestError';
  }
}

// 用户行为序列
const userBehaviors = {
  // 数据分析师查看元数据流程
  dataAnalystViewMetadata: async (page: Page, dataSourceId: string): Promise<BehaviorResult> => {
    console.log('执行数据分析师查看元数据流程');
    
    await page.goto(`/datasource/${dataSourceId}`);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    await userBehaviors.switchToMetadataTab(page);
    
    const tableList = page.locator(MetadataTestConfig.selectors.tableList);
    const count = await tableList.count();
    
    if (count > 0) {
      const viewCount = Math.min(count, Math.floor(Math.random() * 2) + 2);
      
      for (let i = 0; i < viewCount; i++) {
        const randomIndex = Math.floor(Math.random() * count);
        await tableList.nth(randomIndex).click();
        await MetadataTestUtils.waitForLoadingToDisappear(page);
        await page.waitForTimeout(MetadataTestConfig.timeouts.interaction);
      }
    }
    
    return { success: count > 0 };
  },
  
  // 数据管理员同步元数据流程
  dataAdminSyncMetadata: async (page: Page, dataSourceId: string): Promise<BehaviorResult> => {
    console.log('执行数据管理员同步元数据流程');
    
    await page.goto(`/datasource/${dataSourceId}`);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    const syncButton = page.locator(MetadataTestConfig.selectors.syncButton);
    if (await syncButton.count() === 0) {
      throw new MetadataTestError('未找到同步按钮');
    }
    
    await syncButton.click();
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    try {
      await page.waitForSelector(MetadataTestConfig.selectors.notification, { 
        timeout: MetadataTestConfig.timeouts.element 
      });
      
      await userBehaviors.switchToMetadataTab(page);
      
      return { success: true };
    } catch (e) {
      throw new MetadataTestError('未收到同步通知');
    }
  },
  
  // 用户搜索特定表格流程
  userSearchForTable: async (page: Page, dataSourceId: string, searchTerm: string): Promise<BehaviorResult> => {
    console.log(`执行用户搜索表格流程，搜索词: ${searchTerm}`);
    
    await page.goto(`/datasource/${dataSourceId}`);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    await userBehaviors.switchToMetadataTab(page);
    
    const searchInput = page.locator(MetadataTestConfig.selectors.searchInput);
    if (await searchInput.count() === 0) {
      throw new MetadataTestError('未找到搜索框');
    }
    
    await searchInput.fill(searchTerm);
    await page.waitForTimeout(MetadataTestConfig.timeouts.debounce);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    const resultList = page.locator(MetadataTestConfig.selectors.tableList);
    const resultCount = await resultList.count();
    
    if (resultCount > 0) {
      await resultList.first().click();
      await MetadataTestUtils.waitForLoadingToDisappear(page);
    }
    
    return { 
      success: true, 
      resultCount,
      hasResults: resultCount > 0
    };
  },

  /**
   * 模拟用户查看表的详细元数据
   * @param page Playwright Page 对象
   * @param dataSourceId 数据源ID
   */
  userViewTableDetails: async (page: Page, dataSourceId: string): Promise<void> => {
    console.log('开始执行: 用户查看表详细元数据');
    
    await page.goto(`${MetadataTestConfig.environment.baseUrl}/datasource/${dataSourceId}`);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    await userBehaviors.switchToMetadataTab(page);
    
    await page.waitForSelector(MetadataTestConfig.selectors.tableList);
    
    const firstTable = await page.locator(MetadataTestConfig.selectors.tableRow).first();
    await firstTable.click();
    
    await page.waitForSelector(MetadataTestConfig.selectors.tableDetails);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    console.log('完成执行: 用户查看表详细元数据');
  },

  switchToMetadataTab: async (page: Page): Promise<void> => {
    await page.locator(MetadataTestConfig.selectors.metadataTab).first().click();
    await MetadataTestUtils.waitForLoadingToDisappear(page);
  }
};

// 测试套件
test.describe('元数据管理模块 - 用户行为测试', () => {
  let dataSourceId: string = '';
  
  // 测试上下文设置
  test.beforeAll(async ({ browser }) => {
    console.log('开始测试前准备工作');
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      const id = await MetadataTestUtils.getAvailableDataSourceId(page);
      if (id) {
        dataSourceId = id;
        console.log(`测试将使用数据源ID: ${dataSourceId}`);
      }
    } finally {
      await context.close();
    }
  });
  
  // 每个测试运行后清理
  test.afterEach(async ({ page }) => {
    await MetadataTestUtils.takeScreenshot(page, 'test-end');
  });
  
  // 测试: 数据管理员同步元数据流程
  test('数据管理员应能成功同步元数据', async ({ page }) => {
    test.skip(!dataSourceId, '没有可用的数据源');
    
    const result = await userBehaviors.dataAdminSyncMetadata(page, dataSourceId);
    expect(result.success).toBeTruthy();
    
    const hasFeedback = await page.isVisible(MetadataTestConfig.selectors.notification);
    expect(hasFeedback).toBeTruthy();
    
    // 等待同步完成
    await page.waitForTimeout(MetadataTestConfig.timeouts.operation);
  });
  
  // 测试: 数据分析师查看元数据流程
  test('数据分析师应能浏览和查看元数据', async ({ page }) => {
    test.skip(!dataSourceId, '没有可用的数据源');
    
    const result = await userBehaviors.dataAnalystViewMetadata(page, dataSourceId);
    expect(result.success).toBeTruthy();
    
    const hasMetadataContent = await page.isVisible(MetadataTestConfig.selectors.columnList);
    expect(hasMetadataContent).toBeTruthy();
  });
  
  // 测试: 用户搜索表格流程 - 有结果场景
  test('用户应能搜索并找到存在的表', async ({ page }) => {
    test.skip(!dataSourceId, '没有可用的数据源');
    
    // 使用可能存在的表名进行搜索
    const searchTerm = MetadataTestConfig.testData.searchTerms.likely[0];
    const result = await userBehaviors.userSearchForTable(page, dataSourceId, searchTerm);
    
    expect(result.success).toBeTruthy();
    expect(result.hasResults).toBeTruthy();
    expect(result.resultCount).toBeGreaterThan(0);
  });
  
  // 测试: 用户搜索表格流程 - 无结果场景
  test('用户搜索不存在的表应能看到无结果提示', async ({ page }) => {
    test.skip(!dataSourceId, '没有可用的数据源');
    
    const randomSearchTerm = `NoSuchTable${faker.string.alphanumeric(8)}`;
    const result = await userBehaviors.userSearchForTable(page, dataSourceId, randomSearchTerm);
    
    expect(result.success).toBeTruthy();
    expect(result.hasResults).toBeFalsy();
    
    const hasNoResultUI = await page.isVisible(MetadataTestConfig.selectors.noResultMessage);
    expect(hasNoResultUI).toBeTruthy();
  });
  
  // 测试: 用户查看表详情流程
  test('用户应能查看表的详细元数据', async ({ page }) => {
    test.skip(!dataSourceId, '没有可用的数据源');
    
    await userBehaviors.userViewTableDetails(page, dataSourceId);
    
    // 验证详情面板中的关键信息是否存在
    const detailsPanel = await page.locator(MetadataTestConfig.selectors.tableDetails);
    await expect(detailsPanel).toBeVisible();
    
    // 验证是否显示列信息
    const columnsSection = await page.locator(MetadataTestConfig.selectors.columnsList);
    await expect(columnsSection).toBeVisible();
    
    // 验证至少有一个列信息
    const columnCount = await page.locator(MetadataTestConfig.selectors.columnRow).count();
    expect(columnCount).toBeGreaterThan(0);
  });
  
  // 测试: 表格加载性能
  test('元数据表格应在2秒内加载完成', async ({ page }) => {
    test.skip(!dataSourceId, '没有可用的数据源');
    
    await page.goto(`/datasource/${dataSourceId}`);
    await MetadataTestUtils.waitForLoadingToDisappear(page);
    
    const startTime = Date.now();
    
    await userBehaviors.switchToMetadataTab(page);
    await page.waitForSelector(MetadataTestConfig.selectors.tableList, { 
      timeout: MetadataTestConfig.timeouts.navigation 
    });
    
    const endTime = Date.now();
    const loadTime = endTime - startTime;
    
    console.log(`元数据表格加载时间: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(MetadataTestConfig.timeouts.operation);
  });
}); 