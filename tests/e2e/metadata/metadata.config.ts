/**
 * 元数据测试配置
 */
export const MetadataTestConfig = {
  // 测试环境
  environment: {
    baseUrl: 'http://localhost:3000',
    apiUrl: 'http://localhost:8080/data-scope',
    retryCount: 3,
    retryDelay: 1000,
  },
  
  // 测试超时设置
  timeouts: {
    navigation: 60000,    // 页面导航超时
    element: 30000,       // 元素等待超时
    operation: 20000,     // 操作执行超时
    interaction: 5000,    // 用户交互间隔
    debounce: 2000,       // 防抖等待时间
  },
  
  // 测试选择器
  selectors: {
    metadataTab: '[role="tab"]:has-text("元数据"), [role="tab"]:has-text("表")',
    tableList: '.table-list .table-item, .tree-node, [role="treeitem"]',
    columnList: '.column-list, .table-detail, .column-item',
    searchInput: 'input[placeholder*="搜索"], input[placeholder*="查找"], .search-input',
    syncButton: 'button:has-text("同步元数据"), button:has-text("同步"), [title="同步元数据"]',
    loadingIndicator: '.ant-spin, .loading, .ant-spin-spinning, .ant-spin-container',
    notification: '.ant-message, .notification, [role="alert"]',
    tableRow: '.table-list .table-item, .tree-node-content, [role="treeitem"]',
    tableDetails: '.table-detail-panel, .metadata-detail, .detail-content',
    columnsList: '.column-list, .metadata-columns, .columns-container',
    columnRow: '.column-item, .column-row, [role="row"]',
    noResultMessage: ':has-text("无结果"), :has-text("未找到"), .empty-result, .no-data',
    dataSourceList: '.ant-table-row, .datasource-list .datasource-item, table tbody tr',
  },
  
  // 测试数据
  testData: {
    searchTerms: {
      likely: ['user', 'order', 'product', 'customer', 'test'],
      unlikely: ['NoSuchTable', 'ZXCVBNMasdf', '@#$%^']
    }
  }
}; 