import { test, expect } from '@playwright/test';
import { 
  waitForLoadingToDisappear, 
  randomWait
} from '../utils/test-helpers';

/**
 * 数据源选择器组件测试套件
 * 
 * 这里我们创建一个模拟场景，以测试DataSourceSelector组件
 * 实际应用中，该组件可能在查询创建、集成配置等页面使用
 */
test.describe('数据源选择器组件', () => {
  
  /**
   * 准备一个测试页面，其中可能包含数据源选择器
   */
  const setupTestPage = async (page) => {
    // 访问查询创建页面或其他包含数据源选择器的页面
    // 这里我们使用查询页面作为示例
    await page.goto('/query/create');
    await waitForLoadingToDisappear(page);
    
    // 查找数据源选择器
    const selector = page.locator('.data-source-selector, [data-test="datasource-selector"], select[name="dataSourceId"]');
    const hasSelectorOnPage = await selector.count() > 0;
    
    return { hasSelectorOnPage, selector };
  };
  
  /**
   * 基本功能测试 - 加载数据源选择器
   */
  test('页面应加载数据源选择器组件', async ({ page }) => {
    // 设置测试页面
    const { hasSelectorOnPage, selector } = await setupTestPage(page);
    
    if (!hasSelectorOnPage) {
      console.log('跳过测试 - 页面上没有找到数据源选择器');
      test.skip();
      return;
    }
    
    // 验证选择器已加载
    await expect(selector).toBeVisible();
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-selector.png' });
  });
  
  /**
   * 交互测试 - 点击选择器打开下拉菜单
   */
  test('点击选择器应打开下拉菜单', async ({ page }) => {
    // 设置测试页面
    const { hasSelectorOnPage, selector } = await setupTestPage(page);
    
    if (!hasSelectorOnPage) {
      console.log('跳过测试 - 页面上没有找到数据源选择器');
      test.skip();
      return;
    }
    
    // 点击选择器打开下拉菜单
    await selector.click();
    await randomWait(500, 1000);
    
    // 验证下拉菜单已打开
    const dropdown = page.locator('.data-source-selector .dropdown-menu, .el-select-dropdown, .ant-select-dropdown');
    const isDropdownVisible = await dropdown.count() > 0;
    
    if (isDropdownVisible) {
      // 如果找到下拉菜单，验证其可见性
      await expect(dropdown).toBeVisible();
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-selector-dropdown.png' });
    } else {
      // 可能使用不同的UI库，尝试查找下拉项
      const options = page.locator('.data-source-selector li, .el-select-dropdown__item, .ant-select-item');
      const hasOptions = await options.count() > 0;
      
      if (hasOptions) {
        await expect(options.first()).toBeVisible();
        console.log('下拉菜单项已显示');
      } else {
        console.log('未找到下拉菜单或下拉项，可能使用了不同的UI实现');
      }
    }
  });
  
  /**
   * 交互测试 - 选择数据源
   */
  test('应能选择一个数据源', async ({ page }) => {
    // 设置测试页面
    const { hasSelectorOnPage, selector } = await setupTestPage(page);
    
    if (!hasSelectorOnPage) {
      console.log('跳过测试 - 页面上没有找到数据源选择器');
      test.skip();
      return;
    }
    
    // 点击选择器打开下拉菜单
    await selector.click();
    await randomWait(500, 1000);
    
    // 查找下拉项
    const dropdownItems = page.locator('.data-source-selector li, .el-select-dropdown__item, .ant-select-item, option');
    const itemCount = await dropdownItems.count();
    
    if (itemCount > 0) {
      // 选择第一个数据源
      await dropdownItems.first().click();
      await randomWait(500, 1000);
      
      // 验证选择器已更新值
      const selectedValue = await selector.inputValue() || await selector.textContent() || '';
      
      console.log(`选中的数据源: ${selectedValue}`);
      expect(selectedValue.trim().length).toBeGreaterThan(0);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-selector-selected.png' });
    } else {
      console.log('跳过选择测试 - 没有可选的数据源项');
    }
  });
  
  /**
   * 功能测试 - 刷新数据源列表
   */
  test('应支持刷新数据源列表', async ({ page }) => {
    // 设置测试页面
    const { hasSelectorOnPage, selector } = await setupTestPage(page);
    
    if (!hasSelectorOnPage) {
      console.log('跳过测试 - 页面上没有找到数据源选择器');
      test.skip();
      return;
    }
    
    // 查找刷新按钮
    const refreshButton = page.locator('.data-source-selector button[title="刷新数据源列表"], .data-source-selector .fa-sync-alt, .data-source-selector .refresh-button');
    const hasRefreshButton = await refreshButton.count() > 0;
    
    if (hasRefreshButton) {
      // 点击刷新按钮
      await refreshButton.click();
      await waitForLoadingToDisappear(page);
      
      // 验证组件仍然可见
      await expect(selector).toBeVisible();
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-selector-refresh.png' });
    } else {
      console.log('跳过刷新测试 - 没有找到刷新按钮');
    }
  });
  
  /**
   * 功能测试 - 数据源过滤
   * 如果选择器支持按类型过滤
   */
  test('按类型过滤数据源', async ({ page }) => {
    // 访问一个可能有类型过滤功能的页面
    await page.goto('/datasource');
    await waitForLoadingToDisappear(page);
    
    // 查找类型过滤器
    const typeFilter = page.locator('select[name="type"], [data-test="type-filter"], .type-filter');
    const hasTypeFilter = await typeFilter.count() > 0;
    
    if (hasTypeFilter) {
      // 获取原始数据源列表数量
      const originalRowCount = await page.locator('table tbody tr').count();
      
      // 选择一个特定类型
      await typeFilter.click();
      await page.click('option[value="mysql"], li:has-text("MySQL")');
      await waitForLoadingToDisappear(page);
      
      // 获取过滤后的数量
      const filteredRowCount = await page.locator('table tbody tr').count();
      
      // 记录结果
      console.log(`原始数据源数量: ${originalRowCount}, 过滤后数量: ${filteredRowCount}`);
      
      // 截图
      await page.screenshot({ path: 'tests/e2e/screenshots/datasource-type-filter.png' });
      
      // 清除过滤器
      await typeFilter.click();
      await page.click('option[value=""], li:has-text("全部")');
      await waitForLoadingToDisappear(page);
      
      // 验证恢复到原始状态
      const resetRowCount = await page.locator('table tbody tr').count();
      expect(resetRowCount).toBe(originalRowCount);
    } else {
      console.log('跳过类型过滤测试 - 没有找到类型过滤器');
    }
  });
  
  /**
   * 边界情况测试 - 空数据源列表处理
   * 注意：这个测试需要模拟空数据源列表的情况，在实际环境中可能难以测试
   */
  test.skip('应优雅处理空数据源列表', async ({ page }) => {
    // 此测试需要模拟后端API返回空数据源列表的情况
    // 实际情况下，可能需要使用模拟服务器或拦截API请求
    
    // 假设我们有一个空数据源的测试页面
    await page.goto('/empty-datasource-test');
    await waitForLoadingToDisappear(page);
    
    // 找到选择器
    const selector = page.locator('.data-source-selector');
    
    // 点击选择器
    await selector.click();
    await randomWait(500, 1000);
    
    // 验证显示"暂无数据源"或类似的空状态消息
    const emptyMessage = page.locator('text="暂无数据源", text="No data sources"');
    await expect(emptyMessage).toBeVisible();
    
    // 截图
    await page.screenshot({ path: 'tests/e2e/screenshots/datasource-selector-empty.png' });
  });
}); 