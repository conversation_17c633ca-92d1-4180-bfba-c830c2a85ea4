import { test, expect } from '@playwright/test';

test.describe('解密配置功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到数据源详情页面
    await page.goto('/datasource/detail/test-datasource-id');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('应该能够打开解密配置弹窗', async ({ page }) => {
    // 查找第一个解密配置按钮并点击
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await expect(decryptConfigButton).toBeVisible();

    await decryptConfigButton.click();

    // 验证解密配置弹窗是否打开
    await expect(page.locator('text=列字段解密配置')).toBeVisible();
  });

  test('应该能够配置AES解密', async ({ page }) => {
    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 启用解密功能
    await page.locator('#isDecryptEnabled').check();

    // 选择AES算法
    await page.locator('#aes').check();

    // 输入AES密钥
    await page.locator('#aesKey').fill('test-aes-key-123456');

    // 保存配置
    await page.locator('button:has-text("保存")').click();

    // 验证保存成功
    await expect(page.locator('text=解密配置保存成功')).toBeVisible();
  });

  test('应该能够配置国密整体解密', async ({ page }) => {
    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 启用解密功能
    await page.locator('#isDecryptEnabled').check();

    // 选择国密算法（默认已选中）
    await page.locator('#gm').check();

    // 选择整体解密模式（默认已选中）
    await page.locator('#gmFull').check();

    // 保存配置
    await page.locator('button:has-text("保存")').click();

    // 验证保存成功
    await expect(page.locator('text=解密配置保存成功')).toBeVisible();
  });

  test('应该能够配置国密部分字段解密', async ({ page }) => {
    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 启用解密功能
    await page.locator('#isDecryptEnabled').check();

    // 选择国密算法
    await page.locator('#gm').check();

    // 选择部分字段解密模式
    await page.locator('#gmPartial').check();

    // 添加需要解密的字段
    await page.locator('input[placeholder="输入字段名，如：name, age"]').fill('name');
    await page.locator('button:has-text("添加")').click();

    await page.locator('input[placeholder="输入字段名，如：name, age"]').fill('age');
    await page.locator('button:has-text("添加")').click();

    // 验证字段已添加
    await expect(page.locator('text=name')).toBeVisible();
    await expect(page.locator('text=age')).toBeVisible();

    // 保存配置
    await page.locator('button:has-text("保存")').click();

    // 验证保存成功
    await expect(page.locator('text=解密配置保存成功')).toBeVisible();
  });

  test('应该能够删除部分字段解密中的字段', async ({ page }) => {
    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 启用解密功能并配置部分字段解密
    await page.locator('#isDecryptEnabled').check();
    await page.locator('#gm').check();
    await page.locator('#gmPartial').check();

    // 添加字段
    await page.locator('input[placeholder="输入字段名，如：name, age"]').fill('name');
    await page.locator('button:has-text("添加")').click();

    await page.locator('input[placeholder="输入字段名，如：name, age"]').fill('age');
    await page.locator('button:has-text("添加")').click();

    // 删除第一个字段
    await page.locator('button:has-text("删除")').first().click();

    // 验证字段已删除
    await expect(page.locator('text=name')).not.toBeVisible();
    await expect(page.locator('text=age')).toBeVisible();
  });

  test('应该能够取消解密配置', async ({ page }) => {
    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 启用解密功能
    await page.locator('#isDecryptEnabled').check();

    // 点击取消按钮
    await page.locator('button:has-text("取消")').click();

    // 验证弹窗已关闭
    await expect(page.locator('text=列字段解密配置')).not.toBeVisible();
  });

  test('解密配置应该独立于授权配置', async ({ page }) => {
    // 打开授权配置弹窗
    const authConfigButton = page.locator('button:has-text("授权配置")').first();
    await authConfigButton.click();

    // 验证授权配置弹窗中没有解密相关选项
    await expect(page.locator('text=是否需要解密')).not.toBeVisible();
    await expect(page.locator('text=解密算法')).not.toBeVisible();

    // 关闭授权配置弹窗
    await page.locator('button:has-text("取消")').click();

    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 验证解密配置弹窗中没有授权相关选项（除了解密功能本身）
    await expect(page.locator('text=是否需要授权')).not.toBeVisible();

    // 验证解密配置弹窗有正确的内容
    await expect(page.locator('text=启用解密功能')).toBeVisible();
    await expect(page.locator('text=解密算法')).toBeVisible();
  });

  test('已启用解密配置的弹窗应该正确初始化数据', async ({ page }) => {
    // 模拟已有解密配置的列
    await page.evaluate(() => {
      // 模拟列数据，包含已配置的解密信息
      window.testColumnData = {
        id: 'test-column-id',
        name: 'test_column',
        encryConfig: JSON.stringify({
          aes: {
            key: 'test-aes-key-123456'
          }
        }),
        isEncrypted: true
      };
    });

    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 等待弹窗完全加载
    await page.waitForSelector('text=列字段解密配置');

    // 验证解密功能复选框已勾选
    await expect(page.locator('#isDecryptEnabled')).toBeChecked();

    // 验证AES算法已选中
    await expect(page.locator('#aes')).toBeChecked();

    // 验证AES密钥已填入
    await expect(page.locator('#aesKey')).toHaveValue('test-aes-key-123456');
  });

  test('已启用国密部分解密配置的弹窗应该正确初始化数据', async ({ page }) => {
    // 模拟已有国密部分解密配置的列
    await page.evaluate(() => {
      window.testColumnData = {
        id: 'test-column-id-2',
        name: 'test_column_2',
        encryConfig: JSON.stringify({
          gm: {
            encrypt_json_key: ['name', 'age', 'phone']
          }
        }),
        isEncrypted: true
      };
    });

    // 打开解密配置弹窗
    const decryptConfigButton = page.locator('button:has-text("解密配置")').first();
    await decryptConfigButton.click();

    // 等待弹窗完全加载
    await page.waitForSelector('text=列字段解密配置');

    // 验证解密功能复选框已勾选
    await expect(page.locator('#isDecryptEnabled')).toBeChecked();

    // 验证国密算法已选中
    await expect(page.locator('#gm')).toBeChecked();

    // 验证部分字段解密模式已选中
    await expect(page.locator('#gmPartial')).toBeChecked();

    // 验证JSON字段已正确显示
    await expect(page.locator('text=name')).toBeVisible();
    await expect(page.locator('text=age')).toBeVisible();
    await expect(page.locator('text=phone')).toBeVisible();
  });
});
