/**
 * 数据源测试配置
 */
export const DataSourceTestConfig = {
  // 测试环境
  environment: {
    baseUrl: 'http://localhost:3000',
    apiUrl: 'http://localhost:8080/data-scope',
    retryCount: 3,
    retryDelay: 1000,
  },
  
  // 测试超时设置
  timeouts: {
    navigation: 60000,    // 页面导航超时
    element: 30000,       // 元素等待超时
    operation: 20000,     // 操作执行超时
    interaction: 5000,    // 用户交互间隔
    debounce: 2000,       // 防抖等待时间
  },
  
  // 测试选择器
  selectors: {
    addButton: 'button:has-text("添加数据源")',
    form: {
      name: 'input[type="text"][placeholder="请输入数据源名称"]',
      type: 'select[disabled][title="本期仅支持MySQL数据源"]',
      host: 'input[type="text"][placeholder="请输入主机地址"]',
      port: 'input[type="number"][placeholder="请输入端口号"]',
      database: 'input[type="text"][placeholder="请输入数据库名称"]',
      username: 'input[type="text"][placeholder="请输入用户名"]',
      password: 'input[type="password"][placeholder="请输入密码"]',
      testButton: 'button[type="button"]:has-text("测试连接")',
      submitButton: 'button[type="button"]:has-text("保存修改")',
    },
    notification: 'div[role="alert"]',
    loadingIndicator: 'div[role="progressbar"]',
    dataSourceList: 'table[role="grid"] tr',
  },
  
  // 测试数据
  testData: {
    defaultConfig: {
      name: '测试数据源',
      type: 'MySQL',
      host: 'localhost',
      port: 3306,
      database: 'test_db',
      username: 'test_user',
      password: 'test_password'
    }
  }
}; 