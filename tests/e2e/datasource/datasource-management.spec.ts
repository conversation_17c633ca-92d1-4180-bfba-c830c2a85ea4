import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';
import { faker } from '@faker-js/faker/locale/zh_CN';
import { DataSourceTestConfig } from './datasource.config';

interface DataSourceConfig {
  name: string;
  type: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

/**
 * 生成测试用的数据源配置
 */
function generateDataSourceConfig(): DataSourceConfig {
  return {
    ...DataSourceTestConfig.testData.defaultConfig,
    name: `测试数据源_${faker.string.alphanumeric(6)}`
  };
}

/**
 * 数据源管理测试工具类
 */
class DataSourceTestUtils {
  /**
   * 等待加载指示器消失
   */
  static async waitForLoadingToDisappear(page: Page): Promise<void> {
    const loadingSelector = DataSourceTestConfig.selectors.loadingIndicator;
    await page.waitForSelector(loadingSelector, { state: 'detached', timeout: DataSourceTestConfig.timeouts.element })
      .catch(() => console.log('加载指示器未出现或已消失'));
    await page.waitForTimeout(DataSourceTestConfig.timeouts.debounce);
  }

  /**
   * 添加数据源
   */
  static async addDataSource(page: Page, config: DataSourceConfig): Promise<string | undefined> {
    try {
      console.log('开始添加数据源...');
      
      // 访问数据源列表页面
      await page.goto(DataSourceTestConfig.environment.baseUrl + '/datasource');
      await this.waitForLoadingToDisappear(page);
      
      console.log('点击添加数据源按钮');
      await page.click(DataSourceTestConfig.selectors.addButton);
      await this.waitForLoadingToDisappear(page);
      
      console.log('填写表单');
      const form = DataSourceTestConfig.selectors.form;
      
      // 填写表单
      await page.fill(form.name, config.name);
      // await page.click(form.type); // TODO 本期将数据源类型标记为了 disabled
      await page.keyboard.type(config.type);
      await page.keyboard.press('Enter');
      await page.fill(form.host, config.host);
      await page.fill(form.port, config.port.toString());
      await page.fill(form.database, config.database);
      await page.fill(form.username, config.username);
      await page.fill(form.password, config.password);
      
      console.log('点击测试连接按钮');
      await page.click(form.testButton);
      
      // 等待测试连接结果
      await page.waitForSelector(DataSourceTestConfig.selectors.notification, { 
        timeout: DataSourceTestConfig.timeouts.operation 
      });
      await this.waitForLoadingToDisappear(page);
      
      console.log('点击确定按钮');
      await page.click(form.submitButton);
      
      // 等待页面跳转
      await page.waitForURL(/\/datasource\/[\w-]+$/, { 
        timeout: DataSourceTestConfig.timeouts.navigation 
      });
      
      // 从URL中提取数据源ID
      const url = page.url();
      const match = url.match(/\/datasource\/([\w-]+)$/);
      const dataSourceId = match ? match[1] : undefined;
      
      console.log(`数据源添加完成，ID: ${dataSourceId}`);
      return dataSourceId;
      
    } catch (e) {
      console.error('添加数据源失败:', e);
      // 截图记录错误状态
      await page.screenshot({ path: `error-add-datasource-${Date.now()}.png` });
      return undefined;
    }
  }
}

// 测试套件
test.describe('数据源管理模块', () => {
  test('应能成功添加新的数据源', async ({ page }) => {
    const config = generateDataSourceConfig();
    console.log('使用配置:', config);
    
    const dataSourceId = await DataSourceTestUtils.addDataSource(page, config);
    expect(dataSourceId).toBeDefined();
    expect(typeof dataSourceId).toBe('string');
    
    // 验证数据源是否出现在列表中
    await page.goto(DataSourceTestConfig.environment.baseUrl + '/datasource');
    await DataSourceTestUtils.waitForLoadingToDisappear(page);
    
    const dataSourceElement = page.locator(DataSourceTestConfig.selectors.dataSourceList)
      .filter({ hasText: config.name });
    
    const isVisible = await dataSourceElement.isVisible();
    expect(isVisible).toBeTruthy();
  });
}); 