import { FullConfig } from '@playwright/test';
import path from 'path';
import { writeFileSync } from 'fs';

/**
 * 全局测试拆卸
 * 在所有测试结束后执行一次
 */
async function globalTeardown(config: FullConfig): Promise<void> {
  console.log('⚙️ 全局拆卸 - 开始');

  // 清理测试产生的临时数据
  try {
    // 记录测试完成时间
    const testEndTime = new Date().toISOString();
    const logInfo = {
      testEndTime,
      message: '测试套件执行完成',
      config: {
        baseURL: (config.projects[0].use as any).baseURL,
        workers: config.workers
      }
    };

    // 写入测试完成日志
    writeFileSync(
      path.join(process.cwd(), 'tests/e2e/storage/test-completion.json'),
      JSON.stringify(logInfo, null, 2)
    );

    // 这里可以添加其他清理操作，如：
    // - 重置测试数据
    // - 清理临时文件
    // - 关闭测试服务
    // - 发送测试报告通知

    console.log('✅ 测试完成日志已保存');
  } catch (error) {
    console.error('❌ 全局拆卸失败:', error);
  }

  console.log('⚙️ 全局拆卸 - 完成');
}

export default globalTeardown; 