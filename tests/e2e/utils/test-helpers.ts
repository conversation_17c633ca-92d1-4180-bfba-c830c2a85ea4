import { expect } from '@playwright/test';
import type { Page } from '@playwright/test';
import { faker } from '@faker-js/faker/locale/zh_CN';

/**
 * 等待加载状态消失
 * @param page Playwright页面对象
 * @param timeout 等待超时时间（毫秒）
 */
export async function waitForLoadingToDisappear(page: Page, timeout = 30000): Promise<void> {
  // 适配不同的加载指示器选择器
  const loadingSelectors = [
    '.loading-spinner',
    '.ant-spin',
    '.el-loading',
    '[data-test="loading-indicator"]',
    '.loading-container',
    '.loading',
    // 添加其他可能的加载指示器选择器
  ];
  
  for (const selector of loadingSelectors) {
    try {
      // 检查该选择器是否存在
      const exists = await page.locator(selector).count() > 0;
      if (exists) {
        // 等待该加载指示器消失
        await page.waitForSelector(`${selector}:not(:visible)`, { timeout });
        console.log(`等待加载指示器消失: ${selector}`);
        return;
      }
    } catch (e) {
      // 忽略错误，继续检查下一个选择器
    }
  }
  
  // 如果没有找到加载指示器，等待一个安全的时间
  await page.waitForTimeout(1000);
}

/**
 * 等待通知消息出现
 * @param page Playwright页面对象
 * @param timeout 等待超时时间（毫秒）
 */
export async function waitForNotification(page: Page, timeout = 5000): Promise<void> {
  // 适配不同UI库的通知选择器
  const notificationSelectors = [
    '.el-notification',
    '.ant-message .ant-message-notice',
    '.toast',
    '.notification',
    '.alert',
    '[data-test="notification"]',
    // 添加其他可能的通知选择器
  ];
  
  for (const selector of notificationSelectors) {
    try {
      // 检查该选择器是否存在
      await page.waitForSelector(selector, { timeout: 500 });
      console.log(`检测到通知消息: ${selector}`);
      return;
    } catch (e) {
      // 忽略错误，继续检查下一个选择器
    }
  }
  
  // 如果没有找到通知，抛出错误
  throw new Error('未能检测到任何通知消息');
}

/**
 * 验证页面标题
 * @param page Playwright页面对象
 * @param expectedTitle 期望的标题（可以是部分文本）
 */
export async function expectPageTitle(page: Page, expectedTitle: string): Promise<void> {
  // 适配不同的标题选择器
  const titleSelectors = [
    'h1',
    'h2',
    '.page-title',
    '.title',
    '[data-test="page-title"]',
    // 添加其他可能的标题选择器
  ];
  
  for (const selector of titleSelectors) {
    try {
      const titleElements = page.locator(selector);
      const count = await titleElements.count();
      
      for (let i = 0; i < count; i++) {
        const titleText = await titleElements.nth(i).textContent();
        if (titleText && titleText.includes(expectedTitle)) {
          console.log(`找到匹配标题: "${titleText}" 在选择器 ${selector}`);
          return;
        }
      }
    } catch (e) {
      // 忽略错误，继续检查下一个选择器
    }
  }
  
  // 如果没有找到匹配的标题，抛出错误
  throw new Error(`未能找到包含 "${expectedTitle}" 的页面标题`);
}

/**
 * 生成随机数据源名称
 * @returns 随机数据源名称
 */
export function generateRandomDataSourceName(): string {
  return `测试数据源-${faker.company.name()}-${faker.string.alphanumeric(5)}`;
}

/**
 * 生成测试数据源信息
 * @param type 数据源类型
 * @returns 数据源基本信息
 */
export function generateTestDataSource(type: 'mysql' | 'postgresql' | 'oracle' | 'sqlserver' = 'mysql') {
  const typeConfig = {
    mysql: { port: 3306, database: 'test_db' },
    postgresql: { port: 5432, database: 'postgres' },
    oracle: { port: 1521, database: 'ORCL' },
    sqlserver: { port: 1433, database: 'master' }
  };

  return {
    name: generateRandomDataSourceName(),
    description: faker.lorem.sentence(),
    type: type,
    host: faker.internet.ip(),
    port: typeConfig[type].port,
    databaseName: typeConfig[type].database,
    username: 'testuser',
    password: 'Test@123456',
    syncFrequency: 'manual'
  };
}

/**
 * 检查元素是否在视口中可见
 * @param page Playwright页面对象
 * @param selector 元素选择器
 */
export async function expectElementVisible(page: Page, selector: string): Promise<void> {
  await expect(page.locator(selector)).toBeVisible();
  console.log(`验证元素可见: ${selector}`);
}

/**
 * 检查表格是否包含预期的行
 * @param page Playwright页面对象
 * @param expectedText 预期表格行中的文本
 */
export async function expectTableContainsRow(page: Page, expectedText: string): Promise<void> {
  await expect(page.locator(`tr:has-text("${expectedText}")`)).toBeVisible();
  console.log(`验证表格包含行: ${expectedText}`);
}

/**
 * 等待随机时间
 * @param min 最小等待时间（毫秒）
 * @param max 最大等待时间（毫秒）
 */
export async function randomWait(min = 300, max = 1000): Promise<void> {
  const waitTime = Math.floor(Math.random() * (max - min + 1)) + min;
  await new Promise(resolve => setTimeout(resolve, waitTime));
}

/**
 * 获取表格某一列的数据
 * @param page Playwright页面对象
 * @param columnIndex 列索引（从0开始）
 */
export async function getTableColumnData(page: Page, columnIndex: number): Promise<string[]> {
  // 适配不同UI库的表格
  const tableRowSelectors = [
    'table tr', 
    '.ant-table-row',
    '.el-table__row',
    '[data-test="table-row"]',
    // 添加其他可能的行选择器
  ];
  
  for (const rowSelector of tableRowSelectors) {
    try {
      const rows = page.locator(rowSelector);
      const rowCount = await rows.count();
      
      if (rowCount === 0) {
        continue;
      }
      
      const columnData: string[] = [];
      
      for (let i = 0; i < rowCount; i++) {
        const row = rows.nth(i);
        // 适配不同的单元格选择器
        const cellSelectors = ['td', '.cell', '[data-test="table-cell"]'];
        
        for (const cellSelector of cellSelectors) {
          try {
            const cells = row.locator(cellSelector);
            const cellCount = await cells.count();
            
            if (cellCount > columnIndex) {
              const cellText = await cells.nth(columnIndex).textContent() || '';
              columnData.push(cellText.trim());
              break;
            }
          } catch (e) {
            // 忽略错误，继续尝试下一个选择器
          }
        }
      }
      
      if (columnData.length > 0) {
        return columnData;
      }
    } catch (e) {
      // 忽略错误，继续尝试下一个选择器
    }
  }
  
  // 如果没有找到数据，返回空数组
  return [];
}

/**
 * 单击表格中的指定行
 * @param page Playwright页面对象
 * @param rowIndex 行索引（从0开始）
 */
export async function clickTableRow(page: Page, rowIndex: number): Promise<void> {
  // 适配不同UI库的表格行
  const tableRowSelectors = [
    'table tr', 
    '.ant-table-row',
    '.el-table__row',
    '[data-test="table-row"]',
    // 添加其他可能的行选择器
  ];
  
  for (const rowSelector of tableRowSelectors) {
    try {
      const rows = page.locator(rowSelector).filter({ hasText: '' }); // 过滤掉空行
      const rowCount = await rows.count();
      
      if (rowCount > rowIndex) {
        await rows.nth(rowIndex).click();
        console.log(`点击了表格行: ${rowIndex}`);
        return;
      }
    } catch (e) {
      // 忽略错误，继续尝试下一个选择器
    }
  }
  
  // 如果没有找到行，抛出错误
  throw new Error(`未能找到表格行: ${rowIndex}`);
}

/**
 * 检查元素是否可见
 * @param page Playwright页面对象
 * @param selector CSS选择器
 * @param timeoutMs 超时时间（毫秒）
 * @returns 元素是否可见
 */
export async function isElementVisible(page: Page, selector: string, timeoutMs = 5000): Promise<boolean> {
  try {
    await page.waitForSelector(selector, { state: 'visible', timeout: timeoutMs });
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 填写表单字段
 * @param page Playwright页面对象
 * @param fieldName 字段名称
 * @param value 值
 * @param optional 是否是可选字段
 */
export async function fillFormField(page: Page, fieldName: string, value: string, optional = false): Promise<void> {
  try {
    // 尝试多种可能的输入选择器
    const selectors = [
      `input[name="${fieldName}"]`,
      `textarea[name="${fieldName}"]`,
      `[data-test="${fieldName}"]`,
      `[formcontrolname="${fieldName}"]`,
      `#${fieldName}`
    ];
    
    for (const selector of selectors) {
      try {
        const element = page.locator(selector);
        const count = await element.count();
        
        if (count > 0) {
          await element.fill(value);
          console.log(`填写字段 ${fieldName} = ${value}`);
          return;
        }
      } catch (e) {
        // 忽略错误，继续尝试下一个选择器
      }
    }
    
    if (!optional) {
      throw new Error(`未能找到字段: ${fieldName}`);
    } else {
      console.log(`跳过可选字段: ${fieldName}`);
    }
  } catch (e) {
    if (!optional) {
      throw e;
    } else {
      console.log(`跳过可选字段: ${fieldName} (错误: ${e})`);
    }
  }
}

/**
 * 选择下拉框选项
 * @param page Playwright页面对象
 * @param selectName 下拉框名称
 * @param optionValue 选项值
 */
export async function selectDropdownOption(page: Page, selectName: string, optionValue: string): Promise<void> {
  try {
    // 尝试处理各种不同UI框架的下拉框
    
    // 1. 标准HTML选择框
    try {
      await page.selectOption(`select[name="${selectName}"]`, optionValue);
      console.log(`已选择标准下拉选项: ${optionValue}`);
      return;
    } catch (e) {
      // 不是标准选择框，继续尝试
    }
    
    // 2. Element UI选择框
    try {
      const elSelect = page.locator(`.el-select[name="${selectName}"], [data-test="${selectName}"]`);
      if (await elSelect.count() > 0) {
        await elSelect.click();
        await page.locator(`.el-select-dropdown__item:has-text("${optionValue}")`).click();
        console.log(`已选择Element UI下拉选项: ${optionValue}`);
        return;
      }
    } catch (e) {
      // 不是Element选择框，继续尝试
    }
    
    // 3. Ant Design选择框
    try {
      const antSelect = page.locator(`.ant-select[name="${selectName}"], [data-test="${selectName}"]`);
      if (await antSelect.count() > 0) {
        await antSelect.click();
        await page.locator(`.ant-select-item-option:has-text("${optionValue}")`).click();
        console.log(`已选择Ant Design下拉选项: ${optionValue}`);
        return;
      }
    } catch (e) {
      // 不是Ant Design选择框，继续尝试
    }
    
    // 4. 通用方法 - 尝试点击任何可能是下拉框的元素，然后选择选项
    try {
      const selectors = [
        `[name="${selectName}"]`,
        `[data-test="${selectName}"]`,
        `#${selectName}`,
        `.${selectName}`
      ];
      
      for (const selector of selectors) {
        const dropdown = page.locator(selector);
        if (await dropdown.count() > 0) {
          await dropdown.click();
          
          // 尝试多种可能的选项选择器
          const optionSelectors = [
            `li:has-text("${optionValue}")`,
            `.dropdown-item:has-text("${optionValue}")`,
            `.select-option:has-text("${optionValue}")`,
            `[data-value="${optionValue}"]`
          ];
          
          for (const optionSelector of optionSelectors) {
            try {
              await page.locator(optionSelector).click();
              console.log(`已选择通用下拉选项: ${optionValue}`);
              return;
            } catch (e) {
              // 继续尝试下一个选项选择器
            }
          }
        }
      }
    } catch (e) {
      // 所有通用方法都失败
    }
    
    throw new Error(`未能选择下拉选项: ${selectName} = ${optionValue}`);
  } catch (e) {
    console.error(`选择下拉选项失败: ${e}`);
    throw e;
  }
}

/**
 * 截图并保存
 * @param page Playwright页面对象
 * @param name 截图名称
 */
export async function takeScreenshot(page: Page, name: string): Promise<void> {
  await page.screenshot({ path: `./tests/e2e/screenshots/${name}.png`, fullPage: true });
  console.log(`截图已保存: ${name}.png`);
} 