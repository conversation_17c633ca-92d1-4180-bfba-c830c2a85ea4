import { test, expect } from '@playwright/test'

test.describe('数据源类型修改功能', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到数据源管理页面
    await page.goto('/datasource')
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="datasource-list"]', { timeout: 10000 })
  })

  test('应该能够成功修改数据源类型', async ({ page }) => {
    // 查找第一个数据源并点击编辑按钮
    const firstEditButton = page.locator('[data-testid="edit-datasource-btn"]').first()
    await expect(firstEditButton).toBeVisible()
    await firstEditButton.click()

    // 等待编辑表单加载
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    // 获取当前数据源类型
    const typeSelect = page.locator('select[data-testid="datasource-type"]')
    await expect(typeSelect).toBeVisible()
    const currentType = await typeSelect.inputValue()

    // 选择不同的数据源类型
    const newType = currentType === 'mysql' ? 'db2' : 'mysql'
    await typeSelect.selectOption(newType)

    // 验证类型已更改
    expect(await typeSelect.inputValue()).toBe(newType)

    // 点击保存按钮
    const saveButton = page.locator('[data-testid="save-datasource-btn"]')
    await expect(saveButton).toBeEnabled()
    await saveButton.click()

    // 等待保存成功提示
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('.ant-message-success')).toContainText('数据源更新成功')

    // 验证返回到列表页面
    await page.waitForSelector('[data-testid="datasource-list"]', { timeout: 5000 })

    // 重新进入编辑页面验证类型已保存
    await firstEditButton.click()
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })
    
    const updatedTypeSelect = page.locator('select[data-testid="datasource-type"]')
    expect(await updatedTypeSelect.inputValue()).toBe(newType)
  })

  test('应该能够只修改数据源类型而不影响其他字段', async ({ page }) => {
    // 查找第一个数据源并点击编辑按钮
    const firstEditButton = page.locator('[data-testid="edit-datasource-btn"]').first()
    await firstEditButton.click()

    // 等待编辑表单加载
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    // 记录当前所有字段的值
    const nameInput = page.locator('input[data-testid="datasource-name"]')
    const descriptionInput = page.locator('textarea[data-testid="datasource-description"]')
    const hostInput = page.locator('input[data-testid="datasource-host"]')
    const portInput = page.locator('input[data-testid="datasource-port"]')
    const databaseInput = page.locator('input[data-testid="datasource-database"]')
    const usernameInput = page.locator('input[data-testid="datasource-username"]')
    const typeSelect = page.locator('select[data-testid="datasource-type"]')

    const originalValues = {
      name: await nameInput.inputValue(),
      description: await descriptionInput.inputValue(),
      host: await hostInput.inputValue(),
      port: await portInput.inputValue(),
      database: await databaseInput.inputValue(),
      username: await usernameInput.inputValue(),
      type: await typeSelect.inputValue()
    }

    // 只修改数据源类型
    const newType = originalValues.type === 'mysql' ? 'db2' : 'mysql'
    await typeSelect.selectOption(newType)

    // 点击保存按钮
    const saveButton = page.locator('[data-testid="save-datasource-btn"]')
    await saveButton.click()

    // 等待保存成功提示
    await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 5000 })

    // 重新进入编辑页面验证
    await page.waitForSelector('[data-testid="datasource-list"]', { timeout: 5000 })
    await firstEditButton.click()
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    // 验证只有类型字段被修改，其他字段保持不变
    expect(await nameInput.inputValue()).toBe(originalValues.name)
    expect(await descriptionInput.inputValue()).toBe(originalValues.description)
    expect(await hostInput.inputValue()).toBe(originalValues.host)
    expect(await portInput.inputValue()).toBe(originalValues.port)
    expect(await databaseInput.inputValue()).toBe(originalValues.database)
    expect(await usernameInput.inputValue()).toBe(originalValues.username)
    expect(await typeSelect.inputValue()).toBe(newType)  // 只有类型被修改
  })

  test('应该在修改类型时自动更新默认端口', async ({ page }) => {
    // 查找第一个数据源并点击编辑按钮
    const firstEditButton = page.locator('[data-testid="edit-datasource-btn"]').first()
    await firstEditButton.click()

    // 等待编辑表单加载
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    const typeSelect = page.locator('select[data-testid="datasource-type"]')
    const portInput = page.locator('input[data-testid="datasource-port"]')

    // 选择MySQL类型
    await typeSelect.selectOption('mysql')
    await page.waitForTimeout(500)  // 等待端口更新
    expect(await portInput.inputValue()).toBe('3306')

    // 选择DB2类型
    await typeSelect.selectOption('db2')
    await page.waitForTimeout(500)  // 等待端口更新
    expect(await portInput.inputValue()).toBe('50000')
  })

  test('应该显示适当的错误信息当保存失败时', async ({ page }) => {
    // 模拟网络错误或服务器错误
    await page.route('**/api/datasources/*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: '服务器内部错误'
        })
      })
    })

    // 查找第一个数据源并点击编辑按钮
    const firstEditButton = page.locator('[data-testid="edit-datasource-btn"]').first()
    await firstEditButton.click()

    // 等待编辑表单加载
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    // 修改数据源类型
    const typeSelect = page.locator('select[data-testid="datasource-type"]')
    const currentType = await typeSelect.inputValue()
    const newType = currentType === 'mysql' ? 'db2' : 'mysql'
    await typeSelect.selectOption(newType)

    // 点击保存按钮
    const saveButton = page.locator('[data-testid="save-datasource-btn"]')
    await saveButton.click()

    // 验证显示错误信息
    await expect(page.locator('.ant-message-error')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('.ant-message-error')).toContainText('更新数据源失败')
  })

  test('应该在表单验证失败时禁用保存按钮', async ({ page }) => {
    // 查找第一个数据源并点击编辑按钮
    const firstEditButton = page.locator('[data-testid="edit-datasource-btn"]').first()
    await firstEditButton.click()

    // 等待编辑表单加载
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    // 清空必填字段
    const nameInput = page.locator('input[data-testid="datasource-name"]')
    await nameInput.clear()

    // 验证保存按钮被禁用
    const saveButton = page.locator('[data-testid="save-datasource-btn"]')
    await expect(saveButton).toBeDisabled()

    // 恢复必填字段
    await nameInput.fill('测试数据源')

    // 验证保存按钮重新启用
    await expect(saveButton).toBeEnabled()
  })

  test('应该能够取消编辑并返回列表页面', async ({ page }) => {
    // 查找第一个数据源并点击编辑按钮
    const firstEditButton = page.locator('[data-testid="edit-datasource-btn"]').first()
    await firstEditButton.click()

    // 等待编辑表单加载
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })

    // 修改数据源类型
    const typeSelect = page.locator('select[data-testid="datasource-type"]')
    const currentType = await typeSelect.inputValue()
    const newType = currentType === 'mysql' ? 'db2' : 'mysql'
    await typeSelect.selectOption(newType)

    // 点击取消按钮
    const cancelButton = page.locator('[data-testid="cancel-datasource-btn"]')
    await cancelButton.click()

    // 验证返回到列表页面
    await page.waitForSelector('[data-testid="datasource-list"]', { timeout: 5000 })

    // 重新进入编辑页面验证修改未保存
    await firstEditButton.click()
    await page.waitForSelector('[data-testid="datasource-form"]', { timeout: 5000 })
    
    const typeSelectAfterCancel = page.locator('select[data-testid="datasource-type"]')
    expect(await typeSelectAfterCancel.inputValue()).toBe(currentType)  // 应该恢复到原来的类型
  })
})
