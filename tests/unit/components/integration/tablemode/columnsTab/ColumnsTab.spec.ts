import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import ColumnsTab from '@/components/integration/tablemode/columnsTab/ColumnsTab.vue'
import ColumnsList from '@/components/integration/tablemode/columnsTab/ColumnsList.vue'
import ColumnForm from '@/components/integration/tablemode/columnsTab/ColumnForm.vue'
import { ColumnAlign } from '@/types/integration'
import { nextTick } from 'vue'

// 模拟消息服务
vi.mock('@/services/message', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// 模拟枚举服务配置
vi.mock('@/utils/config', () => ({
  enumServiceConfig: {
    projectCode: 'test-project'
  }
}))

// 模拟配置同步函数
vi.mock('@/utils/columnConfigSync', () => ({
  syncConfigToTopLevel: vi.fn(),
  syncTopLevelToConfig: vi.fn()
}))

describe('ColumnsTab组件', () => {
  // 默认props
  const defaultProps = {
    integrationId: 'test-integration',
    dataSource: 'test-datasource',
    tableConfig: {
      columns: [
        {
          id: 'col_1',
          field: 'id',
          label: 'ID',
          displayType: 'text',
          visible: true,
          align: ColumnAlign.LEFT,
          width: 150,
          sortable: true,
          filterable: true
        },
        {
          id: 'col_2',
          field: 'name',
          label: '名称',
          displayType: 'text',
          visible: true,
          align: ColumnAlign.LEFT,
          width: 200,
          sortable: true,
          filterable: true
        }
      ]
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染列表视图', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })

    // 等待加载完成
    await nextTick()
    
    // 应该渲染ColumnsList组件
    expect(wrapper.findComponent(ColumnsList).exists()).toBe(true)
    
    // 不应该渲染ColumnForm组件
    expect(wrapper.findComponent(ColumnForm).exists()).toBe(false)
    
    // 检查传递给ColumnsList的props
    const columnsList = wrapper.findComponent(ColumnsList)
    expect(columnsList.props('columns')).toHaveLength(2)
    expect(columnsList.props('columns')[0].field).toBe('id')
    expect(columnsList.props('columns')[1].field).toBe('name')
  })

  it('添加列按钮点击后应该显示表单', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 模拟点击添加按钮 - 通过触发ColumnsList的add事件
    await wrapper.findComponent(ColumnsList).vm.$emit('add')
    
    // 应该显示ColumnForm组件
    expect(wrapper.findComponent(ColumnForm).exists()).toBe(true)
    
    // 检查传递给ColumnForm的props
    const columnForm = wrapper.findComponent(ColumnForm)
    expect(columnForm.props('column')).toBeUndefined() // 添加模式下没有当前列
    expect(columnForm.props('existingFields')).toEqual(['id', 'name'])
    expect(columnForm.props('saving')).toBe(false)
  })

  it('编辑列时应该显示表单并加载列数据', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 要编辑的列
    const columnToEdit = defaultProps.tableConfig.columns[0]
    
    // 模拟点击编辑按钮 - 通过触发ColumnsList的edit事件
    await wrapper.findComponent(ColumnsList).vm.$emit('edit', columnToEdit)
    
    // 应该显示ColumnForm组件
    expect(wrapper.findComponent(ColumnForm).exists()).toBe(true)
    
    // 检查传递给ColumnForm的props
    const columnForm = wrapper.findComponent(ColumnForm)
    expect(columnForm.props('column')).toMatchObject({
      id: 'col_1',
      field: 'id',
      label: 'ID'
    })
  })

  it('取消表单编辑时应该返回列表视图', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 先进入编辑模式
    await wrapper.findComponent(ColumnsList).vm.$emit('add')
    expect(wrapper.findComponent(ColumnForm).exists()).toBe(true)
    
    // 取消编辑
    await wrapper.findComponent(ColumnForm).vm.$emit('cancel')
    
    // 应该返回列表视图
    expect(wrapper.findComponent(ColumnsList).exists()).toBe(true)
    expect(wrapper.findComponent(ColumnForm).exists()).toBe(false)
  })

  it('保存列时应该更新列数据并返回列表视图', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 模拟添加新列
    await wrapper.findComponent(ColumnsList).vm.$emit('add')
    
    // 定义新列数据
    const newColumn = {
      id: 'col_new',
      field: 'email',
      label: '邮箱',
      displayType: 'text',
      visible: true,
      align: ColumnAlign.LEFT,
      width: 200,
      sortable: true,
      filterable: true
    }
    
    // 保存新列
    await wrapper.findComponent(ColumnForm).vm.$emit('save', newColumn)
    
    // 验证组件发出的事件
    expect(wrapper.emitted('update:columns')).toBeTruthy()
    expect(wrapper.emitted('save')).toBeTruthy()
    
    // 列表现在应该有三列
    const emittedColumns = wrapper.emitted('update:columns')?.[0][0]
    expect(emittedColumns).toHaveLength(3)
    expect(emittedColumns.some((col: any) => col.field === 'email')).toBe(true)
  })

  it('删除列时应该从列表中移除', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 要删除的列
    const columnToDelete = defaultProps.tableConfig.columns[0]
    
    // 模拟删除操作
    await wrapper.findComponent(ColumnsList).vm.$emit('delete', columnToDelete)
    
    // 验证组件发出的事件
    expect(wrapper.emitted('update:columns')).toBeTruthy()
    expect(wrapper.emitted('save')).toBeTruthy()
    
    // 列表现在应该只有一列
    const emittedColumns = wrapper.emitted('update:columns')?.[0][0]
    expect(emittedColumns).toHaveLength(1)
    expect(emittedColumns[0].field).toBe('name')
  })

  it('切换列可见性时应该更新列属性', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 要更新的列
    const columnToUpdate = defaultProps.tableConfig.columns[0]
    
    // 模拟切换可见性操作 - 从可见变为不可见
    await wrapper.findComponent(ColumnsList).vm.$emit('toggle-visibility', columnToUpdate, false)
    
    // 验证组件发出的事件
    expect(wrapper.emitted('update:columns')).toBeTruthy()
    expect(wrapper.emitted('save')).toBeTruthy()
    
    // 列的可见性应该已更新
    const emittedColumns = wrapper.emitted('update:columns')?.[0][0]
    expect(emittedColumns[0].visible).toBe(false)
  })

  it('重新排序列时应该更新列顺序', async () => {
    const wrapper = mount(ColumnsTab, {
      props: defaultProps,
      global: {
        stubs: {
          EnumSelector: true,
          ParamAdvancedConfig: true
        }
      }
    })
    
    await nextTick()
    
    // 重新排序的列 - 交换顺序
    const reorderedColumns = [
      defaultProps.tableConfig.columns[1],
      defaultProps.tableConfig.columns[0]
    ]
    
    // 模拟重排序操作
    await wrapper.findComponent(ColumnsList).vm.$emit('reorder', reorderedColumns)
    
    // 验证组件发出的事件
    expect(wrapper.emitted('update:columns')).toBeTruthy()
    expect(wrapper.emitted('save')).toBeTruthy()
    
    // 列顺序应该已更新
    const emittedColumns = wrapper.emitted('update:columns')?.[0][0]
    expect(emittedColumns[0].field).toBe('name')
    expect(emittedColumns[1].field).toBe('id')
    
    // displayOrder属性应该已更新
    expect(emittedColumns[0].displayOrder).toBe(0)
    expect(emittedColumns[1].displayOrder).toBe(1)
  })
})