import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import DecryptConfigModal from '@/components/datasource/DecryptConfigModal.vue';

// Mock axios
vi.mock('@/utils/axios', () => ({
  default: {
    put: vi.fn().mockResolvedValue({ data: { success: true } })
  }
}));

// Mock message service
vi.mock('@/services/message', () => ({
  useMessageService: () => ({
    success: vi.fn(),
    error: vi.fn()
  })
}));

// Mock response handler
vi.mock('@/utils/api', () => ({
  useResponseHandler: () => ({
    handleResponse: vi.fn()
  })
}));

describe('解密配置数据结构测试', () => {
  it('应该正确生成AES解密配置', () => {
    const aesConfig = {
      aes: {
        key: 'test-aes-key-123456'
      }
    };

    expect(aesConfig).toEqual({
      aes: {
        key: 'test-aes-key-123456'
      }
    });

    expect(aesConfig.aes.key).toBe('test-aes-key-123456');
  });

  it('应该正确生成国密整体解密配置', () => {
    const gmFullConfig = {
      gm: {}
    };

    expect(gmFullConfig).toEqual({
      gm: {}
    });

    expect(gmFullConfig.gm).toBeDefined();
    expect(Object.keys(gmFullConfig.gm)).toHaveLength(0);
  });

  it('应该正确生成国密部分字段解密配置', () => {
    const gmPartialConfig = {
      gm: {
        encrypt_json_key: ['name', 'age']
      }
    };

    expect(gmPartialConfig).toEqual({
      gm: {
        encrypt_json_key: ['name', 'age']
      }
    });

    expect(gmPartialConfig.gm.encrypt_json_key).toEqual(['name', 'age']);
    expect(gmPartialConfig.gm.encrypt_json_key).toHaveLength(2);
  });

  it('应该正确验证配置格式', () => {
    // 测试有效的AES配置
    const validAesConfig = {
      aes: {
        key: 'valid-key'
      }
    };
    expect(validAesConfig.aes).toBeDefined();
    expect(validAesConfig.aes.key).toBeTruthy();

    // 测试有效的国密整体解密配置
    const validGmFullConfig = {
      gm: {}
    };
    expect(validGmFullConfig.gm).toBeDefined();

    // 测试有效的国密部分解密配置
    const validGmPartialConfig = {
      gm: {
        encrypt_json_key: ['field1', 'field2']
      }
    };
    expect(validGmPartialConfig.gm).toBeDefined();
    expect(Array.isArray(validGmPartialConfig.gm.encrypt_json_key)).toBe(true);
    expect(validGmPartialConfig.gm.encrypt_json_key.length).toBeGreaterThan(0);
  });

  it('应该正确判断是否启用了解密', () => {
    // 空配置 - 未启用解密
    const emptyConfig = {};
    const isEncryptedEmpty = !!(emptyConfig.aes || emptyConfig.gm);
    expect(isEncryptedEmpty).toBe(false);

    // AES配置 - 启用解密
    const aesConfig = {
      aes: {
        key: 'test-key'
      }
    };
    const isEncryptedAes = !!(aesConfig.aes || aesConfig.gm);
    expect(isEncryptedAes).toBe(true);

    // 国密配置 - 启用解密
    const gmConfig = {
      gm: {
        encrypt_json_key: ['name']
      }
    };
    const isEncryptedGm = !!(gmConfig.aes || gmConfig.gm);
    expect(isEncryptedGm).toBe(true);
  });

  it('应该正确处理配置的序列化和反序列化', () => {
    const originalConfig = {
      gm: {
        encrypt_json_key: ['name', 'age', 'email']
      }
    };

    // 序列化
    const serialized = JSON.stringify(originalConfig);
    expect(typeof serialized).toBe('string');

    // 反序列化
    const deserialized = JSON.parse(serialized);
    expect(deserialized).toEqual(originalConfig);
    expect(deserialized.gm.encrypt_json_key).toEqual(['name', 'age', 'email']);
  });

  it('应该正确处理边界情况', () => {
    // 空的encrypt_json_key数组
    const emptyArrayConfig = {
      gm: {
        encrypt_json_key: []
      }
    };
    expect(emptyArrayConfig.gm.encrypt_json_key).toHaveLength(0);

    // 单个字段的encrypt_json_key
    const singleFieldConfig = {
      gm: {
        encrypt_json_key: ['onlyField']
      }
    };
    expect(singleFieldConfig.gm.encrypt_json_key).toHaveLength(1);
    expect(singleFieldConfig.gm.encrypt_json_key[0]).toBe('onlyField');

    // 空字符串密钥（应该被视为无效）
    const emptyKeyConfig = {
      aes: {
        key: ''
      }
    };
    expect(emptyKeyConfig.aes.key).toBe('');
    expect(emptyKeyConfig.aes.key.trim()).toBe('');
  });
});

describe('DecryptConfigModal 组件测试', () => {
  let wrapper: any;

  beforeEach(() => {
    // 清理之前的实例
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('应该正确初始化空配置', async () => {
    wrapper = mount(DecryptConfigModal, {
      props: {
        visible: true,
        columnId: 'test-column-id',
        columnName: 'test_column',
        initialConfig: null
      }
    });

    await nextTick();

    // 验证初始状态
    const enabledCheckbox = wrapper.find('#isDecryptEnabled');
    expect(enabledCheckbox.element.checked).toBe(false);
  });

  it('应该正确初始化AES配置', async () => {
    const aesConfig = {
      aes: {
        key: 'test-aes-key-123456'
      }
    };

    wrapper = mount(DecryptConfigModal, {
      props: {
        visible: true,
        columnId: 'test-column-id',
        columnName: 'test_column',
        initialConfig: aesConfig
      }
    });

    await nextTick();

    // 验证解密功能已启用
    const enabledCheckbox = wrapper.find('#isDecryptEnabled');
    expect(enabledCheckbox.element.checked).toBe(true);

    // 验证AES算法已选中
    const aesRadio = wrapper.find('#aes');
    expect(aesRadio.element.checked).toBe(true);

    // 验证AES密钥已填入
    const aesKeyInput = wrapper.find('#aesKey');
    expect(aesKeyInput.element.value).toBe('test-aes-key-123456');
  });

  it('应该正确初始化国密部分解密配置', async () => {
    const gmPartialConfig = {
      gm: {
        encrypt_json_key: ['name', 'age', 'phone']
      }
    };

    wrapper = mount(DecryptConfigModal, {
      props: {
        visible: true,
        columnId: 'test-column-id',
        columnName: 'test_column',
        initialConfig: gmPartialConfig
      }
    });

    await nextTick();

    // 验证解密功能已启用
    const enabledCheckbox = wrapper.find('#isDecryptEnabled');
    expect(enabledCheckbox.element.checked).toBe(true);

    // 验证国密算法已选中
    const gmRadio = wrapper.find('#gm');
    expect(gmRadio.element.checked).toBe(true);

    // 验证部分解密模式已选中
    const gmPartialRadio = wrapper.find('#gmPartial');
    expect(gmPartialRadio.element.checked).toBe(true);

    // 验证JSON字段已正确设置
    const component = wrapper.vm;
    expect(component.gmJsonKeys).toEqual(['name', 'age', 'phone']);
  });

  it('应该在弹窗重新打开时重新初始化数据', async () => {
    // 首次打开弹窗
    wrapper = mount(DecryptConfigModal, {
      props: {
        visible: true,
        columnId: 'test-column-id',
        columnName: 'test_column',
        initialConfig: null
      }
    });

    await nextTick();

    // 验证初始状态
    let enabledCheckbox = wrapper.find('#isDecryptEnabled');
    expect(enabledCheckbox.element.checked).toBe(false);

    // 关闭弹窗
    await wrapper.setProps({ visible: false });
    await nextTick();

    // 重新打开弹窗，带有配置
    await wrapper.setProps({
      visible: true,
      initialConfig: {
        aes: {
          key: 'new-key'
        }
      }
    });
    await nextTick();

    // 验证配置已正确初始化
    enabledCheckbox = wrapper.find('#isDecryptEnabled');
    expect(enabledCheckbox.element.checked).toBe(true);

    const aesKeyInput = wrapper.find('#aesKey');
    expect(aesKeyInput.element.value).toBe('new-key');
  });
});
