# Vite 环境变量机制详解

## 1. 内置环境变量

```typescript
// 在任何 .vue 或 .ts 文件中都可以使用
console.log('当前模式:', import.meta.env.MODE)        // 'development' | 'production'
console.log('是否开发环境:', import.meta.env.DEV)      // true | false  
console.log('是否生产环境:', import.meta.env.PROD)     // true | false

// 我们的域名配置就是基于这个
const domain = import.meta.env.PROD ? 'boss.yeepay.com' : 'qaboss.yeepay.com'
```

## 2. 命令与环境的对应关系

| 命令 | NODE_ENV | mode | import.meta.env.PROD | 加载的环境文件 |
|------|----------|------|---------------------|---------------|
| `npm run dev` | development | development | false | .env.development |
| `npm run build` | production | production | true | .env.production |
| `npm run preview` | production | production | true | .env.production |

## 3. 环境文件优先级

```
.env.production.local   # 生产环境本地配置（最高优先级，不提交到git）
.env.production         # 生产环境配置
.env.local             # 本地配置（不提交到git）
.env                   # 通用配置（最低优先级）
```

## 4. 自定义环境变量

```bash
# .env.development
VITE_API_BASE_URL=
VITE_USE_MOCK_API=true
VITE_LOG_LEVEL=debug

# .env.production  
VITE_API_BASE_URL=/data-scope
VITE_USE_MOCK_API=false
VITE_LOG_LEVEL=error
```

```typescript
// 在代码中使用
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
const useMock = import.meta.env.VITE_USE_MOCK_API === 'true'
const logLevel = import.meta.env.VITE_LOG_LEVEL
```

## 5. 构建时替换

Vite 在构建时会将这些环境变量**静态替换**到代码中：

```typescript
// 开发环境构建后的代码
const domain = false ? 'boss.yeepay.com' : 'qaboss.yeepay.com'  // → 'qaboss.yeepay.com'

// 生产环境构建后的代码  
const domain = true ? 'boss.yeepay.com' : 'qaboss.yeepay.com'   // → 'boss.yeepay.com'
```

## 6. 部署环境的确定

```typescript
// 我们的配置逻辑
export const lowCodeConfig = {
  // 构建时就确定了域名，不是运行时判断
  baseUrl: import.meta.env.PROD ? 'https://boss.yeepay.com' : 'https://qaboss.yeepay.com'
}
```

**关键点**：
- 环境判断发生在**构建时**，不是运行时
- 生产环境的代码包中，`import.meta.env.PROD` 已经被替换为 `true`
- 开发环境的代码中，`import.meta.env.PROD` 被替换为 `false`
- 这样确保了不同环境使用正确的域名配置
