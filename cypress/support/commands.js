// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// 自定义命令：等待元素可见并可点击
Cypress.Commands.add('waitAndClick', (selector, options = {}) => {
  cy.get(selector, options)
    .should('be.visible')
    .should('not.be.disabled')
    .click()
})

// 自定义命令：等待并选择下拉选项
Cypress.Commands.add('selectDropdownOption', (selector, optionText) => {
  cy.get(selector).click()
  cy.get('.ant-select-dropdown').should('be.visible')
  cy.get('.ant-select-dropdown').contains(optionText).click()
})

// 自定义命令：等待API响应
Cypress.Commands.add('waitForApi', (alias, timeout = 10000) => {
  cy.wait(alias, { timeout })
})

// 自定义命令：检查元素是否包含文本
Cypress.Commands.add('shouldContainText', { prevSubject: 'element' }, (subject, text) => {
  cy.wrap(subject).should('contain', text)
})

// 自定义命令：安全点击（等待元素存在且可点击）
Cypress.Commands.add('safeClick', (selector) => {
  cy.get(selector)
    .should('exist')
    .should('be.visible')
    .should('not.be.disabled')
    .click({ force: true })
})
