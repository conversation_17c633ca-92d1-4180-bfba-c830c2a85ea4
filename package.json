{"name": "boss-data-scope2", "private": true, "version": "0.0.1", "description": "全新数据查询与可视化平台", "type": "module", "scripts": {"dev:mock": "node scripts/kill-port.cjs 8082 && cross-env VITE_USE_MOCK_API=true vite --port 8082 --strictPort --clearScreen=false", "dev": "node scripts/kill-port.cjs 8082 && cross-env VITE_USE_MOCK_API=false vite --port 8082 --strictPort --clearScreen=false", "dev:fixed": "node scripts/kill-port.cjs 3000 && cross-env VITE_USE_MOCK_API=true vite --port 3000 --strictPort --clearScreen=false", "dev:network": "node scripts/kill-port.cjs 3000 && cross-env VITE_USE_MOCK_API=true vite --host 0.0.0.0 --port 3000 --strictPort --clearScreen=false", "dev:api:network": "node scripts/kill-port.cjs 8082 && cross-env VITE_USE_MOCK_API=false vite --host 0.0.0.0 --port 8082 --strictPort --clearScreen=false", "dev:no-hmr": "node scripts/kill-port.cjs 3000 && cross-env VITE_DISABLE_HMR=true cross-env VITE_USE_MOCK_API=true vite --port 3000 --strictPort --clearScreen=false", "dev:stable": "node scripts/kill-port.cjs 3000 && cross-env VITE_USE_MOCK_API=true vite --port 3000 --strictPort --clearScreen=false --debug --no-sourcemap", "dev:final": "npm run clean:all && node scripts/kill-port.cjs 3000 && cross-env NODE_ENV=development cross-env VITE_USE_MOCK_API=true vite --port 3000 --config vite.dev.config.js", "dev:simple": "npm run clean:all && node scripts/kill-port.cjs 3000 && cross-env VITE_USE_MOCK_API=true vite --config vite.simple.js --port 3000", "dev:preview": "npm run clean:all && cross-env VITE_USE_MOCK_API=true vite build && node scripts/kill-port.cjs 3000 && cross-env VITE_USE_MOCK_API=true vite preview --port 3000", "build:stable": "cross-env VITE_USE_MOCK_API=false NODE_ENV=production vite build --debug", "build:test": "cross-env VITE_USE_MOCK_API=false NODE_ENV=production vite build --mode test", "preview:stable": "npm run clean:all && npm run build:stable && node scripts/kill-port.cjs 3000 && vite preview --port 3000", "preview:test": "npm run clean:all && npm run build:test && node scripts/kill-port.cjs 3000 && vite preview --port 3000", "clean:ports": "node scripts/kill-port.cjs 3000", "clean:cache": "rm -rf node_modules/.vite* node_modules/.vite_* .vite* dist tmp .temp", "clean:all": "npm run clean:ports && npm run clean:cache", "dev:clean": "npm run clean:all && npm run dev", "build": "tsc && vite build", "preview": "vite preview", "preview:dev": "node scripts/kill-port.cjs 3000 && vite preview --port 3000 --strictPort", "check-types": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:update-snapshots": "playwright test --update-snapshots", "test:e2e:coverage": "playwright test --coverage", "test:metadata": "playwright test tests/e2e/metadata/metadata-management.spec.ts", "test:metadata:headed": "playwright test tests/e2e/metadata/metadata-management.spec.ts --headed", "test:metadata:debug": "playwright test tests/e2e/metadata/metadata-management.spec.ts --debug", "test:metadata:ui": "playwright test tests/e2e/metadata/metadata-management.spec.ts --ui"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@fortawesome/fontawesome-free": "^6.4.2", "@iconify/vue": "^4.3.0", "@vue/reactivity": "^3.5.13", "@vueuse/core": "^10.9.0", "ant-design-vue": "^4.0.3", "axios": "^1.6.7", "dayjs": "^1.11.13", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "naive-ui": "^2.41.0", "pinia": "^2.1.7", "terser": "^5.39.0", "uuid": "^11.1.0", "vue": "^3.4.21", "vue-echarts": "^7.0.3", "vue-router": "^4.3.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@esbuild/linux-x64": "0.19.12", "@faker-js/faker": "^9.6.0", "@fortawesome/fontawesome-free": "^6.0.0", "@playwright/test": "^1.51.1", "@stagewise/toolbar": "^0.2.1", "@types/axios": "^0.9.36", "@types/node": "^22.14.0", "@types/node-fetch": "^2.6.12", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "esbuild": "0.19.12", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.23.0", "express": "^4.18.2", "node-fetch": "^3.3.2", "postcss": "^8.4.35", "prettier": "^3.2.5", "sass-embedded": "^1.86.3", "tailwindcss": "^2.2.19", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^6.2.5", "vitest": "^3.0.9", "vue-tsc": "^2.2.8"}, "optionalDependencies": {"@esbuild/linux-x64": "0.19.12"}}