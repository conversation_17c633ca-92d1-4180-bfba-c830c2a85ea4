# 集成服务页面系统设计方案

**文档状态**: 已更新  
**创建日期**: 2024-07-02  
**最后更新**: 2024-07-15  
**作者**: 架构设计团队  

## 目录

- [1. 背景与需求](#1-背景与需求)
- [2. 系统架构概述](#2-系统架构概述)
- [3. 核心组件设计](#3-核心组件设计)
- [4. 数据流设计](#4-数据流设计)
- [5. 技术选型](#5-技术选型)
- [6. 系统功能模块](#6-系统功能模块)
- [7. 部署架构](#7-部署架构)
- [8. 外部系统集成方案](#8-外部系统集成方案)
- [9. 实施路径](#9-实施路径)
- [10. 系统优势](#10-系统优势)
- [11. 风险与应对](#11-风险与应对)
- [12. 前后端API规范](#12-前后端API规范)
- [13. 附录](#13-附录)
- [14. 最终实施方案](#14-最终实施方案)
  - [14.1 系统定位与架构](#141-系统定位与架构)
  - [14.2 系统功能边界](#142-系统功能边界)
  - [14.3 技术实现方案](#143-技术实现方案)
  - [14.4 实施路径与时间表](#144-实施路径与时间表)
  - [14.5 关键技术实现细节](#145-关键技术实现细节)
  - [14.6 总结](#146-总结)
  - [14.7 JSON配置接口定义](#147-json配置接口定义)
  - [14.8 代码目录结构设计](#148-代码目录结构设计)
- [15. 外围框架与微前端方案](#15-外围框架与微前端方案)
  - [15.1 外围框架设计](#151-外围框架设计)
  - [15.2 微前端架构支持](#152-微前端架构支持)
  - [15.3 实施路径](#153-实施路径)
  - [15.4 优势与挑战](#154-优势与挑战)
  - [15.5 总结](#155-总结)

## 1. 背景与需求

### 1.1 当前状态

目前，我们的集成服务输出JSON配置，并同步到低代码平台，由低代码平台提供页面运行时服务能力。这种架构将数据配置（集成服务）和界面运行时（低代码平台）分离，使我们可以专注于数据集成和配置。

### 1.2 问题陈述

随着业务发展，部分新服务不再使用低代码平台，我们需要一个新的解决方案来：
1. 替代低代码平台的页面渲染能力
2. 支持现有的集成服务JSON配置
3. 集成新的权限管理体系
4. 提供被外部系统嵌入的能力

### 1.3 目标

- 构建一个既能独立运行又能被外部系统嵌入的页面服务系统
- 支持解析和渲染现有的集成JSON配置
- 集成新的权限体系，控制页面访问和功能操作权限
- 提供多种集成方式，满足不同场景的需求
- 确保平滑迁移，不影响现有系统运行

## 2. 系统架构概述

### 2.1 总体架构

我们将构建一个新的页面服务系统，既可以作为独立系统运行，也可以作为现有系统的一个模块集成。

```
┌─────────────────────┐  JSON配置   ┌──────────────────────┐
│                     │ ═════════>  │                      │
│   集成服务模块       │             │    页面服务系统       │  <══════ 权限系统
│ (现有配置管理系统)   │  <══════════│    (新模块/系统)     │
│                     │   API调用   │                      │
└─────────────────────┘             └──────────────────────┘
                                            ▲  │
                                            │  │
                                    访问嵌入 │  │ 渲染服务
                                            │  ▼
                                     ┌──────────────┐
                                     │              │
                                     │  用户/外部系统 │
                                     │              │
                                     └──────────────┘
```

### 2.2 系统边界

- **集成服务模块**：负责配置管理和JSON生成（现有系统）
- **页面服务系统**：负责解析JSON配置并渲染页面，提供用户交互能力
- **权限系统**：提供用户认证和授权服务
- **外部系统**：通过嵌入方式使用页面服务

### 2.3 交互模式

- 集成服务生成JSON配置并存储
- 页面服务加载配置并渲染页面
- 用户通过直接访问或嵌入式访问与页面交互
- 页面服务与权限系统交互验证用户权限

## 3. 核心组件设计

### 3.1 渲染引擎

基于Vue3构建的JSON渲染引擎，将集成配置转换为交互式页面：

```typescript
// 渲染引擎核心接口
interface RenderEngine {
  // 渲染整个页面
  renderPage(container: HTMLElement, config: PageConfig, options?: RenderOptions): PageInstance;
  
  // 仅渲染特定组件
  renderComponent(container: HTMLElement, componentConfig: ComponentConfig): ComponentInstance;
  
  // 注册自定义组件
  registerComponent(type: string, component: Component): void;
}

// 页面配置接口
interface PageConfig {
  id: string;
  name: string;
  type: 'TABLE' | 'CHART' | 'FORM' | 'DASHBOARD';
  components: ComponentConfig[];
  layout: LayoutConfig;
  dataSource: DataSourceConfig;
  theme?: ThemeConfig;
}

// 组件配置接口
interface ComponentConfig {
  id: string;
  type: string;
  properties: Record<string, any>;
  events?: EventConfig[];
  permissions?: string[];
}
```

### 3.2 权限控制模块

```typescript
// 权限控制接口
interface PermissionControl {
  // 验证用户权限
  validateAccess(userId: string, pageId: string): Promise<AccessResult>;
  
  // 获取用户在页面上的操作权限
  getOperationPermissions(userId: string, pageId: string): Promise<OperationPermissions>;
  
  // 根据权限过滤页面配置
  filterConfigByPermission(config: PageConfig, permissions: OperationPermissions): PageConfig;
}

// 访问结果接口
interface AccessResult {
  hasAccess: boolean;
  deniedReason?: string;
  permissionLevel?: 'read' | 'edit' | 'admin';
}

// 操作权限接口
interface OperationPermissions {
  canView: boolean;
  canExport: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canShare: boolean;
  dataFilters?: DataFilter[];
}
```

### 3.3 多种集成方式支持

```typescript
// 对外提供的集成API
export class IntegrationService {
  // iframe快速集成方式
  generateEmbedUrl(pageId: string, options: EmbedOptions): string;
  
  // 微前端集成
  registerAsMicroApp(name: string, options: MicroAppOptions): void;
  
  // Web Component方式
  defineCustomElement(name: string): void;
  
  // SDK方式
  mountTo(container: HTMLElement, pageId: string, options: SDKOptions): PageController;
}

// iframe嵌入选项
interface EmbedOptions {
  token: string;
  theme?: string;
  hideHeader?: boolean;
  initialFilters?: Record<string, any>;
  onMessage?: (message: any) => void;
}

// SDK选项
interface SDKOptions {
  authToken: string;
  theme?: string;
  locale?: string;
  onLoad?: () => void;
  onError?: (error: Error) => void;
  onData?: (data: any) => void;
}
```

### 3.4 通信模块

```typescript
// 跨窗口/应用通信接口
interface CommunicationChannel {
  // 发送消息
  sendMessage(type: string, payload: any): void;
  
  // 监听消息
  onMessage(type: string, handler: (payload: any) => void): void;
  
  // 移除监听
  offMessage(type: string, handler?: (payload: any) => void): void;
}

// 消息类型
type MessageType = 
  | 'PAGE_LOADED'
  | 'DATA_UPDATED'
  | 'ERROR_OCCURRED'
  | 'FILTER_CHANGED'
  | 'ACTION_PERFORMED'
  | 'RESIZE_CONTENT';
```

## 4. 数据流设计

### 4.1 配置流转

```
集成服务 → JSON配置存储 → 页面渲染引擎 → 用户界面
```

1. 集成服务生成标准化的JSON配置
2. 配置存储在数据库或文件系统中
3. 页面渲染引擎加载配置
4. 解析配置生成组件树
5. 渲染组件到用户界面

### 4.2 权限控制流

```
用户访问 → 权限验证 → 配置过滤 → 功能限制渲染
```

1. 用户请求访问特定页面
2. 系统验证用户身份和权限
3. 根据权限过滤页面配置
4. 渲染受限制的页面内容
5. 禁用未授权功能

### 4.3 数据交互流

```
用户操作 → 参数收集 → API调用 → 数据获取 → 页面更新
```

1. 用户执行操作（点击、输入等）
2. 系统收集操作参数
3. 调用相应的API服务
4. 获取结果数据
5. 更新页面显示

## 5. 技术选型

### 5.1 前端技术栈

- **核心框架**：Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI组件库**：Ant Design Vue（主系统）/ 按需加载库（嵌入模式）
- **图表库**：ECharts（可按需加载）
- **构建工具**：Vite
- **微前端方案**：qiankun/micro-app（可选）

### 5.2 打包策略

- **模块化打包**：核心引擎、组件库、工具集分离打包
- **按需加载**：组件和插件支持按需加载
- **多入口构建**：支持不同场景的入口（独立应用、嵌入库等）
- **CSS隔离**：采用CSS模块化或Shadow DOM

### 5.3 性能优化策略

- 代码分割与懒加载
- Tree-shaking减小包体积
- 缓存策略优化
- 虚拟滚动处理大量数据
- WebWorker处理计算密集任务

## 6. 系统功能模块

### 6.1 页面管理中心

- **页面列表与组织**：管理所有可用的集成页面
- **权限配置界面**：设置页面访问和操作权限
- **页面预览与调试**：在管理界面中预览和测试页面
- **版本管理与回滚**：管理页面配置的版本历史

### 6.2 渲染服务

- **集成JSON解析器**：解析标准JSON配置
- **组件动态加载**：按需加载页面组件
- **事件处理系统**：处理用户交互事件
- **数据绑定机制**：连接UI和数据源

### 6.3 嵌入服务

- **嵌入令牌生成**：创建安全的嵌入令牌
- **iframe自适应处理**：自动调整iframe大小
- **跨窗口通信API**：提供与宿主系统的通信机制
- **SDK包与示例**：提供多种集成方式的示例代码

### 6.4 权限管理

- **用户权限验证**：验证用户访问权限
- **页面访问控制**：控制页面的可见性
- **功能操作权限**：控制可用的功能和操作
- **数据范围限制**：基于权限过滤数据

## 7. 部署架构

```
                  ┌──────────────────┐
                  │  CDN静态资源     │
                  └──────────────────┘
                          ▲
                          │
┌─────────────┐   ┌──────┴───────┐   ┌─────────────┐
│ 集成服务API  │◄──┤ 页面服务前端 ├──►│ 权限服务API  │
└─────────────┘   └──────────────┘   └─────────────┘
```

- **静态资源**：通过CDN分发
- **前端应用**：部署为静态网站或与后端集成
- **API服务**：独立部署或集成到现有系统
- **数据存储**：复用现有数据库或使用独立数据库

## 8. 外部系统集成方案

### 8.1 iframe嵌入方式（推荐首选）

最简单直接的方式，适合快速集成：

```html
<iframe 
  src="https://page-service.example.com/embed/{pageId}?token={authToken}" 
  style="width: 100%; height: 700px; border: none;"
></iframe>
```

**优势**：
- 实现简单，几乎不需要修改老系统
- 完全隔离，避免CSS和JS冲突
- 安全边界清晰

**实现细节**：
- 提供token验证机制确保安全
- 支持自适应高度调整
- 提供postMessage通信API

### 8.2 SDK集成方式

通过JavaScript SDK提供更灵活的集成方式：

```javascript
import { IntegrationViewer } from '@company/integration-viewer';

const viewer = new IntegrationViewer({
  container: '#app-container',
  authToken: userToken,
  theme: 'light'
});

viewer.loadPage(pageId);
```

**优势**：
- 提供更大的控制灵活性
- 更好的用户体验和性能
- 支持深度定制

**实现细节**：
- 轻量级核心包 + 按需加载组件
- 提供丰富的生命周期钩子
- 样式隔离确保不影响宿主系统

### 8.3 微前端方式

适合复杂系统的深度集成：

```javascript
// 在外部系统主应用中注册
import { registerMicroApp } from 'qiankun';

registerMicroApp({
  name: 'integration-page',
  entry: '//page-service.example.com/micro/',
  container: '#integration-container',
  props: { pageId, authToken }
});
```

**优势**：
- 可共享状态和上下文
- 更好的应用间通信
- 适合大型系统集成

**实现细节**：
- 符合微前端标准的入口
- 生命周期管理
- 状态隔离和共享机制

## 9. 实施路径

### 9.1 第一阶段：基础功能（3个月）

- 开发核心渲染引擎
- 实现基本权限控制
- 提供iframe嵌入方式
- 支持现有集成JSON格式

**关键任务**：
- 设计并实现渲染引擎核心架构
- 开发基础组件库（表格、图表等）
- 实现配置解析和渲染流程
- 开发iframe嵌入和通信机制

### 9.2 第二阶段：增强功能（2个月）

- 开发SDK集成方式
- 增强权限管理功能
- 优化性能和用户体验
- 完善文档和示例

**关键任务**：
- 设计并实现SDK架构
- 优化组件动态加载机制
- 完善权限控制细节
- 编写详细的集成文档

### 9.3 第三阶段：高级功能（2个月）

- 实现微前端集成
- 开发Web Component支持
- 构建高级交互功能
- 提供完整的分析与监控

**关键任务**：
- 实现微前端规范兼容
- 开发监控和分析功能
- 构建完整的示例应用
- 进行全面的性能测试

## 10. 系统优势

1. **灵活部署**：既可作为独立应用，也可嵌入集成
2. **无缝迁移**：支持现有JSON配置，平滑过渡
3. **完整权限**：集成新权限体系，精细化控制
4. **多种集成**：提供多种嵌入方式满足不同需求
5. **性能优化**：按需加载，减少资源消耗
6. **开发效率**：共享组件和代码，降低维护成本

## 11. 风险与应对

### 11.1 性能问题

**风险**：iframe嵌入可能导致性能问题

**应对措施**：
- 按需加载组件和资源
- 优化初始加载时间
- 实现资源预加载策略
- 提供性能监控和诊断工具

### 11.2 样式冲突

**风险**：嵌入外部系统可能有样式冲突

**应对措施**：
- 使用Shadow DOM或CSS模块化
- 命名空间隔离
- 提供主题定制能力
- 样式重置和隔离

### 11.3 权限复杂度

**风险**：新权限系统集成可能复杂

**应对措施**：
- 设计标准化权限接口
- 分阶段实现权限功能
- 提供默认权限策略
- 开发权限调试工具

### 11.4 兼容性问题

**风险**：不同浏览器可能有兼容性问题

**应对措施**：
- 制定最低浏览器支持标准
- 使用Polyfill兼容旧浏览器
- 提供降级渲染方案
- 全面的浏览器测试

## 12. 前后端API规范

为确保前后端协作顺畅和系统集成的一致性，我们定义了以下API规范。

### 12.1 API设计原则

- 采用RESTful设计风格
- 使用JSON作为数据交换格式
- 实现统一的错误处理机制
- 支持版本控制
- 提供完整的API文档

### 12.2 请求规范

#### 基本URL结构

```
https://api.example.com/v1/{resource}/{resourceId}/{subResource}
```

#### HTTP方法使用

- **GET**: 获取资源
- **POST**: 创建资源
- **PUT**: 全量更新资源
- **PATCH**: 部分更新资源
- **DELETE**: 删除资源

#### 请求头规范

```
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
X-Request-ID: {uuid}
```

#### 分页请求参数

```
?page=1&pageSize=20&sort=createdAt:desc
```

### 12.3 响应规范

#### 统一响应结构

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 实际业务数据
  },
  "timestamp": "2023-07-02T10:30:15Z",
  "requestId": "550e8400-e29b-41d4-a716-************"
}
```

#### 分页响应结构

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      // 数据项
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 58,
      "totalPages": 3
    }
  }
}
```

#### 错误响应规范

```json
{
  "code": 400,
  "message": "Bad Request",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2023-07-02T10:30:15Z",
  "requestId": "550e8400-e29b-41d4-a716-************"
}
```

#### 状态码使用

- **2xx**: 成功状态
  - 200: OK
  - 201: Created
  - 204: No Content
- **4xx**: 客户端错误
  - 400: Bad Request
  - 401: Unauthorized
  - 403: Forbidden
  - 404: Not Found
  - 422: Unprocessable Entity
- **5xx**: 服务端错误
  - 500: Internal Server Error
  - 503: Service Unavailable

### 12.4 核心API接口

#### 页面配置API

```
GET /api/v1/pages               # 获取页面列表
GET /api/v1/pages/{pageId}      # 获取页面详情
POST /api/v1/pages              # 创建页面
PUT /api/v1/pages/{pageId}      # 更新页面
DELETE /api/v1/pages/{pageId}   # 删除页面
```

#### 页面渲染API

```
GET /api/v1/render/{pageId}     # 获取页面渲染配置
POST /api/v1/render/preview     # 预览渲染结果
```

#### 权限API

```
GET /api/v1/permissions/{pageId}       # 获取页面权限
PUT /api/v1/permissions/{pageId}       # 设置页面权限
POST /api/v1/permissions/validate      # 验证用户权限
```

#### 嵌入令牌API

```
POST /api/v1/embed/token        # 创建嵌入令牌
GET /api/v1/embed/validate      # 验证嵌入令牌
```

### 12.5 数据交互规范

#### 数据查询

```
POST /api/v1/data/query
```

请求体:
```json
{
  "pageId": "page_001",
  "dataSourceId": "ds_001",
  "parameters": {
    "startDate": "2023-01-01",
    "endDate": "2023-01-31"
  },
  "pagination": {
    "page": 1,
    "pageSize": 20
  },
  "sort": [
    { "field": "createdAt", "order": "desc" }
  ]
}
```

#### 数据导出

```
POST /api/v1/data/export
```

#### 批量操作

```
POST /api/v1/data/batch
```

### 12.6 API文档与测试

- 使用OpenAPI/Swagger进行API文档自动生成
- 提供在线API测试工具
- 每个接口提供示例请求和响应
- 自动化API测试确保接口稳定性

## 13. 附录

### 13.1 术语表

- **集成服务**：现有的配置管理系统，生成标准JSON
- **页面服务**：新开发的渲染和交互系统
- **渲染引擎**：解析JSON并渲染UI组件的核心模块
- **嵌入令牌**：用于安全嵌入的验证令牌

### 13.2 参考资料

- Vue.js官方文档：https://vuejs.org/
- 微前端架构：https://micro-frontends.org/
- Web Components：https://developer.mozilla.org/en-US/docs/Web/Web_Components
- iframe通信：https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage

### 13.3 审批信息

| 审批人 | 职位 | 日期 | 状态 |
|--------|------|------|------|
| | | | 待审批 |

## 14. 最终实施方案

在深入分析现有系统和需求后，我们确定最终实施方案如下：

### 14.1 系统定位与架构

#### 核心定位
构建一个专注于**JSON配置解析和渲染**的页面服务系统，作为低代码平台的替代方案，不重复实现现有集成服务系统的功能。

#### 精简架构
```
┌────────────────────┐        ┌────────────────────┐
│  集成服务系统      │        │    页面服务系统     │
│  (现有系统)        │        │    (新开发)        │
├────────────────────┤  JSON  ├────────────────────┤
│ - 配置管理         │───────>│ - 渲染引擎          │
│ - JSON配置生成     │        │ - 组件库            │
│ - 数据源管理       │<───────│ - 嵌入支持          │
└────────────────────┘  结果  └────────────────────┘
         │                             │  
         │                             │  
         ▼                             ▼  
┌────────────────────┐        ┌────────────────────┐
│    查询服务API     │        │    权限系统        │
└────────────────────┘        └────────────────────┘
```

### 14.2 系统功能边界

#### 包含功能
1. **JSON配置解析引擎**：解析现有集成服务生成的配置
2. **组件渲染系统**：基于配置动态渲染UI组件
3. **iframe嵌入支持**：提供页面嵌入能力和通信机制
4. **权限控制适配**：支持权限系统集成和控制

#### 明确不包含的功能
1. ~~配置管理和编辑~~（由现有集成服务系统提供）
2. ~~JSON配置生成~~（由现有集成服务系统提供）
3. ~~数据源管理~~（由现有集成服务系统提供）

### 14.3 技术实现方案

#### 14.3.1 技术栈
- **前端框架**：Vue 3 + TypeScript
- **UI组件库**：Ant Design Vue
- **图表库**：ECharts
- **构建工具**：Vite

#### 14.3.2 核心模块

##### 渲染引擎
```typescript
class RenderEngine {
  // 解析配置并创建组件树
  parse(jsonConfig: string): ComponentTree;
  
  // 渲染组件到DOM
  render(container: HTMLElement): void;
  
  // 更新渲染内容
  update(newConfig?: string): void;
}
```

##### 组件库
基于Ant Design Vue封装一系列可配置组件：
- 查询条件组件集（表单元素）
- 数据表格组件
- 图表组件（基于ECharts）

##### iframe嵌入支持
```typescript
class EmbedService {
  // 初始化嵌入服务
  init(config: EmbedConfig): void;
  
  // 发送消息到父窗口
  sendMessage(type: string, payload: any): void;
  
  // 监听来自父窗口的消息
  onMessage(type: string, handler: Function): void;
  
  // 自动调整iframe高度
  startHeightSync(): void;
}
```

##### 权限适配器
```typescript
class PermissionAdapter {
  // 检查用户对特定操作的权限
  checkPermission(action: string): Promise<boolean>;
  
  // 过滤用户无权访问的组件
  filterComponents(components: Component[]): Component[];
  
  // 注册权限变更监听
  onPermissionChange(handler: Function): void;
}
```

### 14.4 实施路径与时间表

#### 14.4.1 阶段一：基础框架（1.5周）
- 搭建项目基础架构
- 实现配置解析器核心
- 集成Ant Design Vue
- 开发基础组件封装层

#### 14.4.2 阶段二：查询表单与表格（2周）
- 实现查询表单渲染
- 开发表格组件及数据绑定
- 集成分页和排序功能
- 对接查询服务API

#### 14.4.3 阶段三：图表与嵌入支持（1.5周）
- 集成ECharts
- 实现图表配置解析
- 开发iframe嵌入支持
- 实现跨窗口通信机制

#### 14.4.4 阶段四：权限与优化（1周）
- 集成权限系统
- 实现权限控制
- 性能优化
- 浏览器兼容性测试

### 14.5 关键技术实现细节

#### 14.5.1 动态组件渲染
使用Vue 3的动态组件和异步组件机制：

```vue
<template>
  <component 
    :is="resolveComponent(config.type)" 
    v-bind="config.props" 
    @update:modelValue="handleValueChange"
  />
</template>

<script setup>
const componentMap = {
  'input': defineAsyncComponent(() => import('./components/Input.vue')),
  'select': defineAsyncComponent(() => import('./components/Select.vue')),
  'datepicker': defineAsyncComponent(() => import('./components/DatePicker.vue')),
  // ...其他组件
};

function resolveComponent(type) {
  return componentMap[type] || componentMap['input'];
}
</script>
```

#### 14.5.2 iframe通信机制
```javascript
// 页面内部
window.addEventListener('message', (event) => {
  // 验证来源
  if (trustedOrigins.includes(event.origin)) {
    handleMessage(event.data);
  }
});

// 向父窗口发送消息
function sendToParent(type, data) {
  window.parent.postMessage({ type, data }, parentOrigin);
}

// 主动调整高度
function syncHeight() {
  const height = document.body.scrollHeight;
  sendToParent('resize', { height });
}
```

### 14.6 总结

本实施方案聚焦于构建一个专注于渲染和展示的页面服务系统，作为低代码平台的替代方案。通过明确系统边界，避免与现有集成服务系统功能重复，我们可以更高效地实现核心目标：提供页面渲染能力和嵌入支持。

这种设计让两个系统各司其职：现有集成服务系统负责配置管理和生成，新页面服务系统负责配置解析和渲染，从而形成清晰的职责分离和高效的协作模式。

### 14.7 JSON配置接口定义

新页面服务系统与现有集成服务系统之间的关键接口是JSON配置，我们采用以下方案：

#### 14.7.1 配置格式选择

新系统将使用**标准JSON格式**（由`convertToStandardConfig`函数生成的格式）作为输入，而非低代码平台格式。标准JSON是现有集成服务中内部格式到低代码平台格式的中间表示，具有以下特点：

- 结构清晰，直接对应UI组件的配置需求
- 不包含特定低代码平台的实现细节
- 避免对低代码转换服务的外部依赖

#### 14.7.2 标准JSON结构

标准JSON的基本结构如下：

```json
{
  "meta": {
    "database": "数据库标识",
    "schema": "数据库schema",
    "table": "表名",
    "pageCode": "页面编码",
    "apis": {
      "query": { "method": "GET", "path": "/api/query" },
      "download": { "method": "GET", "path": "/download" }
    }
  },
  "filter": [
    {
      "key": "参数名称",
      "label": "显示标签",
      "fieldType": "string|number|boolean|date",
      "dataFormat": "string|int|decimal|date",
      "displayType": "input|select|date-picker",
      "config": {
        "required": true,
        "placeholder": "请输入"
      },
      "defaultValue": "默认值"
    }
  ],
  "list": [
    {
      "isPrimaryKey": false,
      "key": "字段名",
      "label": "显示名",
      "fieldType": "string|number|date",
      "dataFormat": "string|int|decimal|date",
      "columnType": "text|tag|status",
      "config": {
        "width": "宽度",
        "align": "left|center|right",
        "sortable": true
      }
    }
  ],
  "operation": {
    "paginationEnable": true,
    "totalEnable": true,
    "downloadEnable": true,
    "operationColumnFixed": "right",
    "batchEnable": false,
    "tableActions": [],
    "rowActions": [],
    "defaultPageSize": 10
  },
  "chart": {
    "type": "bar|line|pie",
    "title": "图表标题",
    "description": "图表描述",
    "height": 400,
    "showLegend": true,
    "animation": true,
    "xField": "X轴字段",
    "yField": "Y轴字段",
    "config": {
      "theme": "default|dark|light"
    }
  }
}
```

#### 14.7.3 配置模块映射

新系统将按以下对应关系将标准JSON映射到UI组件：

1. **filter → 查询表单**
   - 每个filter项映射为一个表单控件
   - displayType决定使用哪种Ant Design Vue表单组件
   - config包含表单验证和展示配置

2. **list → 数据表格**
   - 每个list项映射为表格的一列
   - columnType决定使用哪种单元格渲染方式
   - config包含列的展示配置

3. **operation → 表格操作**
   - 控制分页、导出、批量操作等功能
   - tableActions映射为表格顶部操作按钮
   - rowActions映射为每行的操作按钮

4. **chart → 图表**
   - type决定使用哪种ECharts图表类型
   - 其他配置映射为ECharts的具体选项

#### 14.7.4 配置获取方式

1. **配置传递**：作为iframe嵌入参数或通过API获取
2. **配置缓存**：在浏览器本地存储中缓存配置以提高性能
3. **增量更新**：支持接收配置的部分更新，避免完全重新渲染

通过定义明确的JSON配置接口，我们可以建立新旧系统之间清晰的边界，实现职责分离，同时确保系统协作的一致性和稳定性。

### 14.8 代码目录结构设计

新建的页面服务系统将作为一个独立工程存在，与现有的集成服务系统代码分离，以确保职责明确、边界清晰。两个系统通过JSON配置文件和API进行数据交换。

#### 14.8.1 独立代码仓库

新页面服务系统将使用独立的代码仓库：
- 现有集成服务系统：`integration-service`
- 新页面服务系统：`page-service-system`

这种隔离确保两个系统可以独立演进、部署和维护，同时减少互相干扰的风险。

#### 14.8.2 新工程目录结构

新页面服务系统的目录结构采用Vue 3项目的标准组织形式：

```
page-service-system/
├── public/                      # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/                     # API接口层
│   │   ├── config.ts            # API配置
│   │   ├── request.ts           # 请求工具
│   │   └── services/            # 服务模块
│   │       ├── auth.ts          # 认证服务
│   │       └── page.ts          # 页面服务
│   ├── assets/                  # 静态资源
│   ├── components/              # 通用组件
│   │   ├── common/              # 基础通用组件
│   │   └── renderer/            # 渲染器组件
│   │       ├── form/            # 表单渲染组件
│   │       ├── table/           # 表格渲染组件
│   │       └── chart/           # 图表渲染组件
│   ├── core/                    # 核心模块
│   │   ├── engine/              # 渲染引擎
│   │   │   ├── parser.ts        # JSON解析器
│   │   │   ├── renderer.ts      # 组件渲染器
│   │   │   └── transformer.ts   # 数据转换器
│   │   ├── embed/               # 嵌入服务
│   │   │   ├── communication.ts # 跨窗口通信
│   │   │   └── resize.ts        # 尺寸同步
│   │   └── permission/          # 权限控制
│   │       ├── adapter.ts       # 权限适配器
│   │       └── validator.ts     # 权限验证器
│   ├── hooks/                   # 自定义钩子
│   ├── models/                  # 类型定义
│   │   ├── config.ts            # 配置类型
│   │   ├── component.ts         # 组件类型
│   │   └── permission.ts        # 权限类型
│   ├── pages/                   # 页面组件
│   │   ├── viewer/              # 查看器页面
│   │   └── embed/               # 嵌入页面
│   ├── router/                  # 路由配置
│   ├── store/                   # 状态管理
│   ├── styles/                  # 全局样式
│   ├── utils/                   # 工具函数
│   ├── App.vue                  # 根组件
│   ├── main.ts                  # 入口文件
│   └── shims-vue.d.ts           # TypeScript声明
├── tests/                       # 测试文件
├── .eslintrc.js                 # ESLint配置
├── .gitignore                   # Git忽略文件
├── package.json                 # 依赖配置
├── tsconfig.json                # TypeScript配置
├── vite.config.ts               # Vite配置
└── README.md                    # 项目说明
```

#### 14.8.3 与现有集成服务系统的关系

两个系统完全独立，但通过以下方式建立联系：

1. **数据交换**
   - JSON配置文件作为两个系统的主要数据交换媒介
   - 标准化的API接口用于实时数据查询和操作

2. **资源引用**
   - 页面服务系统可能引用集成服务系统的API接口
   - 集成服务系统可能通过iframe嵌入页面服务系统

3. **协作模式**
   - 集成服务系统负责生成和管理配置
   - 页面服务系统负责解析配置并渲染页面
   - 用户数据的增删改查仍由集成服务系统的API处理

4. **部署关系**
   - 两个系统可以独立部署在不同服务器上
   - 也可以部署在同一服务器的不同路径下
   - 支持通过网关或代理服务器统一访问

通过这种分离但协作的架构，我们可以确保:
- 系统职责清晰，避免功能重复
- 减少系统间的耦合，提高可维护性
- 允许两个系统以不同的速度和方向演进
- 优化资源利用，专注于各自的核心功能

## 15. 外围框架与微前端方案

随着系统需求的发展，页面服务系统需要增强其集成能力，通过外围框架搭建和微前端架构支持，使其能够更好地承载多个子系统。以下是针对这些新需求的设计方案：

### 15.1 外围框架设计

外围框架是指包裹页面服务系统的UI结构，提供统一的导航、布局和交互体验。

#### 15.1.1 框架结构

```
MainLayout
  ├── SideNavigation (左侧主导航)
  │    └── 包含首页、用户、数据、计算、营销等一级菜单
  ├── Content (内容区域)
  │    ├── HeaderBar (顶部标题栏)
  │    ├── SecondaryNavigation (二级导航，根据需要显示)
  │    │    └── 包含策略管理、场景管理等二级菜单
  │    └── PageContainer (页面容器)
  │         ├── SearchFilterBar (搜索/筛选区)
  │         ├── PageContentArea (主内容区 - 嵌入iframe或直接渲染)
  │         └── Pagination (分页控制)
  └── Footer (页脚，可选)
```

#### 15.1.2 页面嵌入方式

页面服务系统通过iframe方式嵌入到外围框架中：

```html
<iframe 
  :src="`${baseUrl}/embed/${pageId}?hideHeader=true&hideFooter=true&theme=${theme}`"
  class="embed-frame"
  frameborder="0"
  @load="handleIframeLoad"
></iframe>
```

通过URL参数控制嵌入页面的显示方式，如隐藏子系统自身的头部和底部，只显示核心内容。

#### 15.1.3 框架通信机制

框架与嵌入页面之间通过postMessage进行通信：

```javascript
// 框架向子系统发送消息
function sendToSubsystem(type, data) {
  const iframe = document.querySelector('.embed-frame');
  if (iframe && iframe.contentWindow) {
    iframe.contentWindow.postMessage({
      type,
      source: 'main-system',
      target: 'page-service',
      data,
      timestamp: Date.now()
    }, '*');
  }
}

// 监听子系统消息
window.addEventListener('message', (event) => {
  if (event.data && event.data.source === 'page-service') {
    handleSubsystemMessage(event.data);
  }
});
```

### 15.2 微前端架构支持

为了更好地支持多个子系统的集成，页面服务系统将扩展其架构以支持微前端模式。

#### 15.2.1 微前端架构概述

微前端架构允许多个独立开发、部署的前端应用在一个统一的用户界面中共存。在我们的场景中，主系统将作为容器应用，而各个子系统（包括页面服务系统）将作为微应用集成进来。

```
主系统（容器应用）
  ├── 微应用1（如页面服务系统）
  ├── 微应用2（如数据分析系统）
  ├── 微应用3（如用户管理系统）
  └── ...
```

#### 15.2.2 集成方案

我们将采用以下策略实现微前端架构：

1. **基于iframe的隔离方案**
   - 提供良好的应用隔离
   - 简化CSS和JS冲突问题
   - 支持独立部署和更新

2. **统一的认证和权限管理**
   - 所有子系统共享同一认证机制
   - 权限中心统一管理应用权限
   - 单点登录支持

3. **标准化的通信协议**
   - 定义清晰的消息结构和类型
   - 支持事件广播和点对点通信
   - 提供API代理机制

#### 15.2.3 技术实现

1. **子系统注册机制**

```javascript
// 主系统中的微应用注册表
const subSystems = [
  {
    id: 'page-service',
    name: '页面服务系统',
    entry: '/page-service-system/embed/',
    activeRule: '/pages'
  },
  {
    id: 'data-analysis',
    name: '数据分析系统',
    entry: '/data-analysis/embed/',
    activeRule: '/analysis'
  }
  // ...其他子系统
];

// 根据路由动态加载子系统
function loadSubSystem(systemId, container, props) {
  const system = subSystems.find(s => s.id === systemId);
  if (!system) return;
  
  // 创建iframe并设置属性
  const iframe = document.createElement('iframe');
  iframe.src = `${system.entry}?${new URLSearchParams(props)}`;
  iframe.className = 'subsystem-frame';
  iframe.frameBorder = '0';
  
  // 清空容器并添加iframe
  container.innerHTML = '';
  container.appendChild(iframe);
  
  // 设置通信桥接
  setupCommunicationBridge(systemId, iframe);
}
```

2. **统一的状态共享**

为了在不同子系统间共享状态，我们将实现一个基于localStorage和事件的状态同步机制：

```javascript
// 全局状态存储与同步
const globalState = {
  // 在localStorage中存储共享状态
  set(key, value) {
    localStorage.setItem(`global_${key}`, JSON.stringify(value));
    window.dispatchEvent(new CustomEvent('global-state-change', { 
      detail: { key, value } 
    }));
  },
  
 // 获取共享状态
  get(key) {
    const value = localStorage.getItem(`global_${key}`);
    return value ? JSON.parse(value) : null;
  },
  
  // 监听状态变化
  onChange(key, callback) {
    const handler = (e) => {
      if (e.detail.key === key) {
        callback(e.detail.value);
      }
    };
    window.addEventListener('global-state-change', handler);
    return () => window.removeEventListener('global-state-change', handler);
  }
};
```

### 15.3 实施路径

外围框架与微前端支持的实施将分为以下阶段：

1. **阶段一：外围框架搭建** (1-2周)
   - 创建基础框架组件
   - 实现导航系统
   - 设置iframe嵌入机制
   - 实现基本通信功能

2. **阶段二：微前端基础设施** (2周)
   - 开发子系统注册与加载机制
   - 实现跨系统状态管理
   - 建立统一的事件总线
   - 完善错误处理机制

3. **阶段三：多系统集成** (1-2周)
   - 集成第一个额外子系统
   - 优化多系统共存时的性能
   - 实现子系统间的导航联动
   - 统一用户体验与主题

4. **阶段四：优化与扩展** (1周)
   - 性能优化与监控
   - 开发调试工具
   - 文档完善
   - 用户培训

### 15.4 优势与挑战

#### 15.4.1 优势
- **灵活集成**：能够集成不同技术栈和独立开发的子系统
- **独立部署**：各系统可以独立开发、测试和部署
- **技术隔离**：减少技术栈和依赖冲突
- **渐进式迁移**：支持现有系统的渐进式迁移
- **团队自治**：不同团队可以负责不同子系统的开发

#### 15.4.2 挑战
- **性能开销**：iframe方案可能带来一定的性能开销
- **用户体验一致性**：需要额外努力确保跨子系统的UI一致性
- **通信复杂性**：子系统间通信需要精心设计
- **调试难度**：多系统协作增加调试复杂度

### 15.5 总结

通过外围框架搭建和微前端架构支持，页面服务系统将能够更好地承载多个子系统，提供统一的用户体验和集成能力。这一方案既保持了各子系统的独立性和灵活性，又提供了统一的视觉和交互体验，是系统向更复杂业务场景扩展的关键基础设施。
