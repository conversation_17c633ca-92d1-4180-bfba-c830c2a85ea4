# DataScope API设计文档

## 1. API设计原则

DataScope API遵循以下设计原则：

1. **RESTful设计风格** - 使用标准HTTP方法表示操作
2. **统一的JSON响应格式** - 保持一致的数据结构
3. **明确的版本化策略** - URL路径版本（/api/v1/...）
4. **规范的错误处理** - 使用HTTP状态码和错误消息
5. **支持分页、排序和过滤** - 提供灵活的数据访问方式
6. **适当的缓存策略** - 提高性能
7. **严格的接口文档** - 使用Swagger/OpenAPI

## 2. 响应格式标准

所有API响应遵循以下统一格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 业务数据
  }
}
```

错误响应示例：

```json
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

## 3. 核心API接口

### 3.1 数据源管理API

# 获取数据源列表
GET /api/v1/datasources
# 创建数据源
POST /api/v1/datasources
# 获取数据源详情
GET /api/v1/datasources/{id}
# 更新数据源
PUT /api/v1/datasources/{id}
# 删除数据源
DELETE /api/v1/datasources/{id}
# 测试数据源连接
POST /api/v1/datasources/{id}/test-connection
# 触发数据源元数据同步
POST /api/v1/datasources/{id}/sync
# 获取数据源同步状态
GET /api/v1/datasources/{id}/sync-status

### 3.2 元数据查询API

# 获取数据源的schema列表
GET /api/v1/datasources/{id}/schemas
# 获取schema的表列表
GET /api/v1/schemas/{id}/tables
# 获取表的列列表
GET /api/v1/tables/{id}/columns
# 获取表的索引列表
GET /api/v1/tables/{id}/indexes
# 搜索元数据（支持关键词搜索）
GET /api/v1/metadata/search?keyword={keyword}
# 获取元数据同步差异
GET /api/v1/datasources/{id}/sync-diff?job_id={syncJobId}
# 获取历史同步差异
GET /api/v1/datasources/{id}/sync-history/{syncJobId}/diff
# 导出差异报告
GET /api/v1/datasources/{id}/sync-diff/{syncJobId}/export
# 获取变更影响分析
GET /api/v1/datasources/{id}/sync-diff/{syncJobId}/impact-analysis

### 3.3 查询管理API

# 执行SQL查询
POST /api/v1/queries/sql
# 自然语言转SQL
POST /api/v1/queries/nl-to-sql
# 执行自然语言查询
POST /api/v1/queries/nl
# 获取查询历史
GET /api/v1/queries/history
# 收藏查询
POST /api/v1/queries/{id}/favorite
# 取消收藏
DELETE /api/v1/queries/{id}/favorite
# 获取收藏的查询
GET /api/v1/queries/favorites
# 导出查询结果为CSV
GET /api/v1/queries/{id}/export-csv
# 设置查询超时时间
PUT /api/v1/queries/{id}/timeout

### 3.4 表关系管理API

# 获取表的关系列表
GET /api/v1/tables/{id}/relations
# 创建表关系
POST /api/v1/relations
# 更新表关系
PUT /api/v1/relations/{id}
# 删除表关系
DELETE /api/v1/relations/{id}
# 自动推断表关系
POST /api/v1/tables/{id}/infer-relations
# 获取关系推断任务状态
GET /api/v1/relations/inference-tasks/{taskId}
# 获取表关系可视化数据
GET /api/v1/tables/{id}/relations-visualization
# 导出关系图
POST /api/v1/relation-visualization/export

### 3.5 页面配置API

# 获取页面配置列表
GET /api/v1/page-configs
# 创建页面配置
POST /api/v1/page-configs
# 获取页面配置详情
GET /api/v1/page-configs/{id}
# 更新页面配置
PUT /api/v1/page-configs/{id}
# 删除页面配置
DELETE /api/v1/page-configs/{id}
# 发布页面配置
POST /api/v1/page-configs/{id}/publish
# AI辅助生成页面配置
POST /api/v1/page-configs/ai-generate
# 获取页面配置历史版本
GET /api/v1/page-configs/{id}/versions
# 回滚到指定版本
POST /api/v1/page-configs/{id}/rollback/{versionId}
# 下载页面配置JSON
GET /api/v1/page-configs/{id}/download
# 配置敏感数据掩码规则
POST /api/v1/page-configs/{id}/masking-rules
# 配置自动隐藏条件规则
POST /api/v1/page-configs/{id}/auto-hide-rules
# 获取配置版本间差异
GET /api/v1/page-configs/{id}/diff?from={versionId1}&to={versionId2}
# 获取版本操作审计日志
GET /api/v1/page-configs/{id}/audit-log
# 解决版本冲突
POST /api/v1/page-configs/{id}/resolve-conflict

### 3.6 低代码平台集成API

# 按ID获取页面配置JSON
GET /api/v1/integration/page-configs/{id}
# 按版本获取页面配置JSON
GET /api/v1/integration/page-configs/{id}/versions/{versionId}
# 执行页面配置对应的查询
POST /api/v1/integration/execute-query
# 获取数据类型到UI组件的映射规则
GET /api/v1/integration/datatype-ui-mappings
# 获取可用的数据展示模板
GET /api/v1/integration/display-templates

### 3.7 LLM服务配置API

# 获取所有LLM服务配置
GET /api/v1/llm/configs
# 添加LLM服务配置
POST /api/v1/llm/configs
# 获取特定LLM服务配置
GET /api/v1/llm/configs/{id}
# 更新LLM服务配置
PUT /api/v1/llm/configs/{id}
# 删除LLM服务配置
DELETE /api/v1/llm/configs/{id}
# 测试LLM服务连接
POST /api/v1/llm/configs/{id}/test-connection
# 切换默认LLM服务
PUT /api/v1/llm/configs/default/{id}
# 获取LLM服务性能统计
GET /api/v1/llm/stats

### 3.8 查询性能分析API

# 获取慢查询列表
GET /api/v1/performance/slow-queries
# 获取慢查询详情
GET /api/v1/performance/slow-queries/{id}
# 获取查询执行计划
GET /api/v1/performance/slow-queries/{id}/execution-plan
# 获取查询优化建议
GET /api/v1/performance/slow-queries/{id}/optimization-suggestions
# 配置性能警报规则
POST /api/v1/performance/alert-rules
# 获取性能趋势报告
GET /api/v1/performance/trends?period={period}&metric={metric}
# 设置慢查询阈值
PUT /api/v1/performance/thresholds

### 3.9 系统监控API

# 获取系统指标
GET /api/v1/monitoring/metrics?type={metricType}&period={period}
# 获取可用仪表盘
GET /api/v1/monitoring/dashboards
# 获取仪表盘详情
GET /api/v1/monitoring/dashboards/{id}
# 创建自定义仪表盘
POST /api/v1/monitoring/dashboards
# 更新仪表盘
PUT /api/v1/monitoring/dashboards/{id}
# 获取资源利用率预测
GET /api/v1/monitoring/predictions?resource={resource}&days={days}
# 配置监控告警
POST /api/v1/monitoring/alerts
# 获取系统健康状态
GET /api/v1/monitoring/health-check

## 4. API安全考量

### 4.1 认证与授权
- 所有API请求需要携带有效的认证令牌
- 基于角色的访问控制机制
- 敏感API的访问日志审计

### 4.2 数据保护
- 传输层使用HTTPS加密
- 敏感数据响应自动掩码
- 个人信息保护合规性

### 4.3 防护机制
- API访问速率限制
- 恶意请求检测与阻断
- SQL注入防护
- 参数验证和净化

## 5. API实现与最佳实践

### 5.1 控制器实现模式
```java
@RestController
@RequestMapping("/api/v1/datasources")
public class DataSourceController {
    @GetMapping
    public Response<List<DataSourceDTO>> list() {
        // 实现
    }
    
    @PostMapping
    public Response<DataSourceDTO> create(@RequestBody DataSourceCreateDTO dto) {
        // 实现
    }
    
    // 其他方法
}
```

### 5.2 异常处理
```java
@ExceptionHandler(ValidationException.class)
public ResponseEntity<Response<Void>> handleValidationException(ValidationException ex) {
    return ResponseEntity.badRequest()
        .body(Response.error(400, ex.getMessage()));
}
```

### 5.3 参数验证
```java
public class DataSourceCreateDTO {
    @NotBlank(message = "名称不能为空")
    private String name;
    
    @NotBlank(message = "主机地址不能为空")
    private String host;
    
    // 其他字段和验证
}
```

## 6. API版本管理策略

### 6.1 版本控制方法
- 使用URL路径版本标识: `/api/v1/...`
- 主版本号变更表示不兼容的API变更
- 次版本号变更表示向后兼容的功能新增

### 6.2 版本过渡
- 新版本发布后，旧版本保持一定时间的支持
- 使用废弃标记提示客户端升级
- 提供版本迁移指南

### 6.3 API文档
- 使用Swagger/OpenAPI自动生成API文档
- 每个版本维护独立的文档
- 文档中包含示例请求和响应

## 参考
- [架构设计文档](architecture-design.md)
- [需求分析文档](../需求分析.md)
