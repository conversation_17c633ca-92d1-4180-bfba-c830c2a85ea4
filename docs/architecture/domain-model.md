# DataScope 领域模型文档

## 概述

本文档描述了DataScope系统的核心业务模型和数据实体。这些模型构成了整个系统的领域层，反映了业务问题域的核心概念和关系。

## 1. 核心业务模型

DataScope系统围绕以下核心业务流程展开：

1. **数据源注册与管理**：用户注册外部数据库系统（MySQL、DB2）作为数据源，配置连接信息和同步策略。
2. **元数据提取与维护**：系统自动从已注册的数据源中提取元数据信息并持久化存储，支持增量更新。
3. **数据查询**：用户通过SQL或自然语言描述来查询数据源中的数据。
4. **表关系管理**：系统自动推断或允许用户手动定义表之间的关联关系。
5. **页面配置生成**：为低代码平台生成符合规范的页面配置JSON和API接口。

## 2. 核心数据实体

### 2.1 数据源实体 (DataSource)

数据源实体表示系统中注册的外部数据库连接，是整个系统的基础：

- **基本属性**：
  - ID、名称、描述
  - 类型（MySQL/DB2）
  - 创建时间、更新时间
- **连接信息**：
  - URL、端口、用户名
  - 密码（AES-GCM加密存储）
- **状态信息**：
  - 状态（活跃/非活跃）
  - 同步配置（频率、上次同步时间）

### 2.2 元数据实体

元数据实体表示从数据源中提取的数据库结构信息：

#### Schema
- ID、数据源ID
- 名称、描述

#### Table
- ID、Schema ID
- 名称、类型
- 描述、行数估计
- 上次更新时间

#### Column
- ID、Table ID
- 名称、数据类型
- 长度、精度
- 可空性、默认值
- 描述、位置
- 是否敏感数据标记

#### Index
- ID、Table ID
- 名称、类型（主键/唯一/普通）
- 包含的列、创建时间

#### MetadataSyncJob
- ID、数据源ID
- 开始时间、结束时间
- 状态、增量/全量标记
- 日志

### 2.3 表关系实体 (TableRelation)

表关系实体表示数据库表之间的关联关系：

- **标识信息**：
  - ID、源表ID、目标表ID
- **关系属性**：
  - 关系类型（一对一、一对多、多对多）
  - 源列ID数组、目标列ID数组
- **推断信息**：
  - 推断方式（自动/手动）
  - 可信度得分（0-100）
- **使用统计**：
  - 使用频率、上次使用时间
  - 创建时间、更新时间

### 2.4 查询实体 (Query)

查询实体表示用户创建并可能保存的查询：

- **标识信息**：
  - ID、用户ID、数据源ID
- **查询内容**：
  - 查询类型（SQL/自然语言）
  - SQL文本、自然语言描述
- **执行信息**：
  - 执行状态、执行结果
  - 执行时间、影响行数
  - 超时设置（默认30秒）
- **元数据**：
  - 是否收藏、收藏时间
  - 创建时间、最后执行时间

### 2.5 页面配置实体 (PageConfig)

页面配置实体表示为低代码平台生成的UI配置：

- **基本信息**：
  - ID、名称、描述
  - 关联查询ID
- **版本控制**：
  - 版本号、状态（草稿/发布）
- **配置内容**：
  - 查询条件配置（JSON）
  - 结果展示配置（JSON）
  - 操作列配置（JSON）
- **特性配置**：
  - 敏感数据掩码规则
  - 自动隐藏不常用条件设置
- **时间戳**：
  - 创建时间、更新时间

### 2.6 用户实体 (User)

用户实体表示系统用户及其配置：

- **基本信息**：
  - ID、用户名、邮箱
- **配额管理**：
  - 查询配额设置
  - 配额使用统计
- **活动追踪**：
  - 上次活动时间
  - 创建时间、更新时间

## 3. 实体关系图

```mermaid
erDiagram
    DataSource ||--o{ Schema : contains
    Schema ||--o{ Table : contains
    Table ||--o{ Column : contains
    Table ||--o{ Index : has
    DataSource ||--o{ MetadataSyncJob : logs
    Table ||--o{ TableRelation : participates
    User ||--o{ Query : creates
    DataSource ||--o{ Query : targets
    Query ||--o{ PageConfig : configures
```

## 4. 领域行为

除了静态数据结构外，领域模型还定义了以下关键行为：

### 4.1 数据源行为
- 测试连接有效性
- 同步元数据（全量/增量）
- 加密/解密敏感信息

### 4.2 查询行为
- 验证SQL语法和安全性
- 执行查询并转换结果
- 应用超时和资源限制

### 4.3 表关系行为
- 基于查询模式推断关系
- 基于数据内容分析关系
- 计算关系可信度评分

### 4.4 页面配置行为
- 基于数据类型推断UI组件
- 版本管理与回滚
- 生成低代码平台配置

## 参考
- [架构设计文档](architecture-design.md)
- [API设计文档](api-design.md)
- [需求分析文档](../需求分析.md)
