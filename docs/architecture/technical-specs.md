# DataScope 技术规格文档

本文档详细说明 DataScope 系统的技术实现细节、规范和标准，作为架构设计的补充文档，专注于具体的技术实现规格。

## 1. 项目目录结构

### 1.1 后端项目结构

```java
datascopbe-backend/
├── src/
│   ├── main/
│   │   ├── java/com/datascope/
│   │   │   ├── application/           # 应用服务层
│   │   │   │   ├── dto/               # 数据传输对象
│   │   │   │   ├── mapper/            # DTO与领域对象映射
│   │   │   │   └── service/           # 应用服务实现
│   │   │   ├── domain/                # 领域层
│   │   │   │   ├── model/             # 领域模型
│   │   │   │   ├── repository/        # 仓储接口
│   │   │   │   └── service/           # 领域服务
│   │   │   ├── infrastructure/        # 基础设施层
│   │   │   │   ├── config/            # 配置类
│   │   │   │   ├── datasource/        # 数据源配置
│   │   │   │   ├── persistence/       # 持久化实现
│   │   │   │   └── external/          # 外部服务集成
│   │   │   └── interfaces/            # 接口层
│   │   │       ├── rest/              # REST API
│   │   │       ├── websocket/         # WebSocket接口
│   │   │       └── scheduler/         # 定时任务
│   │   └── resources/                 # 资源文件
│   └── test/                          # 测试代码
├── docs/                            # 项目文档
│   ├── api/                         # API文档
│   ├── architecture/                # 架构文档
│   └── guides/                      # 用户指南
├── pom.xml                            # Maven配置
└── README.md                          # 项目说明
```

### 1.2 前端项目结构

```
datascopbe-frontend/
├── src/
│   ├── api/                        # API接口定义
│   │   ├── datasource.ts
│   │   ├── metadata.ts
│   │   ├── query.ts
│   │   └── user.ts
│   ├── assets/                     # 静态资源
│   │   ├── fonts/
│   │   └── images/
│   ├── components/                 # 组件
│   │   ├── common/                 # 通用组件
│   │   │   ├── AppHeader.vue
│   │   │   ├── AppLayout.vue
│   │   │   ├── AppSidebar.vue
│   │   │   ├── TheFooter.vue
│   │   │   └── TheNavigation.vue
│   │   └── query/                  # 查询相关组件
│   │       ├── ChartConfig.vue
│   │       ├── ChartView.vue
│   │       ├── MetadataExplorer.vue
│   │       ├── NaturalLanguageQuery.vue
│   │       ├── QueryAnalysis.vue
│   │       ├── QueryBuilder.vue
│   │       ├── QueryEditor.vue
│   │       ├── QueryExecutionPlan.vue
│   │       ├── QueryHistory.vue
│   │       ├── QueryManager.vue
│   │       ├── QueryResults.vue
│   │       ├── QueryResultTable.vue
│   │       ├── QuerySuggestions.vue
│   │       ├── QueryVisualization.vue
│   │       ├── SaveQueryModal.vue
│   │       ├── SqlEditor.vue
│   │       ├── builder/              # 查询构建器子组件
│   │       │   ├── ConditionBuilder.vue
│   │       │   ├── ConditionGroup.vue
│   │       │   ├── ExpressionEditor.vue
│   │       │   ├── FieldSelector.vue
│   │       │   └── TableSelector.vue
│   │       └── detail/               # 查询详情子组件
│   │           ├── QueryDetailHeader.vue
│   │           └── QueryDetailTabs.vue
│   ├── mocks/                      # Mock API 数据
│   │   ├── datasource.ts
│   │   └── query.ts
│   ├── plugins/                    # Vue 插件
│   │   ├── antd-locale.ts
│   │   ├── dayjs.ts
│   │   └── echarts.ts
│   ├── router/                     # 路由配置
│   │   └── index.ts                # 路由配置 (TypeScript)
│   ├── services/                   # API 服务层
│   │   ├── api.ts
│   │   ├── datasource.ts
│   │   ├── integrationService.ts
│   │   ├── loading.ts
│   │   ├── message.ts
│   │   ├── mock-query.ts
│   │   ├── mockData.ts
│   │   ├── modal.ts
│   │   ├── query.ts
│   │   └── queryTemplates.ts
│   ├── stores/                     # Pinia 状态管理
│   │   ├── datasource.ts
│   │   ├── integration.ts
│   │   ├── message.ts
│   │   ├── query.ts
│   │   ├── system.ts
│   │   └── user.ts
│   ├── styles/                     # 全局样式
│   │   └── index.css
│   ├── types/                      # TypeScript 类型定义
│   │   ├── datasource.ts
│   │   ├── form.ts
│   │   ├── integration.ts
│   │   ├── message.ts
│   │   ├── metadata.ts
│   │   ├── modal.ts
│   │   ├── query.ts
│   │   ├── table.ts
│   │   ├── user.ts
│   │   ├── builder/
│   │   │   └── index.ts
│   │   └── integration/
│   │       └── index.ts
│   ├── utils/                      # 工具函数
│   │   ├── apiTransformer.ts
│   │   ├── configConverter.ts
│   │   ├── formatter.ts
│   │   ├── request.ts
│   │   └── typeMapping.ts
│   └── views/                      # 页面视图
│       ├── HomeView.vue
│       ├── datasource/
│       │   └── DataSourceView.vue
│       ├── examples/               # 示例页面
│       │   ├── ExamplesIndex.vue
│       │   ├── FormExample.vue
│       │   ├── LoadingExample.vue
│       │   ├── MessageExample.vue
│       │   ├── ModalExample.vue
│       │   ├── TableExample.vue
│       │   └── TestView.vue
│       ├── integration/            # 低代码集成页面
│       │   ├── FullIntegrationEdit.vue
│       │   ├── IntegrationEdit.vue
│       │   ├── IntegrationList.vue
│       │   ├── IntegrationPreview.vue
│       │   ├── IntegrationPreview.vue.original
│       │   ├── MinimalPage.vue
│       │   ├── SimpleIntegrationEdit.vue
│       │   └── SimplifiedIntegrationEdit.vue
│       ├── query/                  # 查询相关页面
│       │   ├── QueryAnalytics.vue
│       │   ├── QueryDetail.vue
│       │   ├── QueryEditor.vue
│       │   ├── QueryHistory.vue
│       │   ├── QueryList.vue
│       │   └── QueryView.vue
│       └── settings/               # 设置页面
│           └── SettingsView.vue
├── .eslintrc.cjs                   # ESLint配置
├── .gitignore                      # Git忽略文件
├── tsconfig.json                   # TypeScript配置
├── package.json                    # 依赖配置
├── tailwind.config.cjs             # Tailwind CSS配置
└── vite.config.ts                  # Vite 配置 (替代 vue.config.ts)
```

## 2. 技术选型理由及版本建议

### 2.1 后端技术栈

| 技术              | 推荐版本   | 选择理由                            |
|-----------------|--------|---------------------------------|
| Java            | 17 LTS | 长期支持版本，提供了许多现代Java特性，性能和安全性更好   |
| Spring Boot     | 3.2.x  | 简化Spring应用开发，自动配置，内嵌服务器，支持响应式编程 |
| MyBatis         | 3.5.x  | 灵活的SQL映射，与Spring Boot集成良好，控制力强  |
| MySQL           | 8.0.x  | 稳定可靠，广泛使用，支持JSON和窗口函数等现代特性      |
| Redis           | 7.0.x  | 高性能缓存，支持多种数据结构，适合缓存元数据和查询结果     |
| Maven           | 3.9.x  | 依赖管理工具，构建管理，模块化支持               |
| Swagger/OpenAPI | 3.0.x  | REST API文档自动生成，简化API开发和测试       |
| Jackson         | 2.15.x | JSON序列化/反序列化，与Spring Boot默认集成   |

### 2.2 前端技术栈

| 技术            | 推荐版本   | 选择理由                                  |
|---------------|--------|---------------------------------------|
| TypeScript    | 5.3.x  | 强类型语言，提高代码健壮性和可维护性，与Vue 3生态良好集成       |
| Vue           | 3.3.x  | 响应式数据绑定，组件化开发，对TypeScript支持良好         |
| Vue Router    | 4.2.x  | Vue官方路由，与Vue 3和TypeScript完全集成         |
| Pinia         | 2.1.x  | Vue 3官方推荐的状态管理库，提供优秀的TypeScript支持     |
| Axios         | 1.6.x  | 基于Promise的HTTP客户端，提供类型定义文件            |
| Tailwind CSS  | 3.4.x  | 实用优先的CSS框架，高度可定制，开发效率高                |
| Font Awesome  | 6.5.x  | 丰富的图标库，易于使用和定制                        |
| Vue Query     | 5.0.x  | 数据获取和缓存库，提供良好的TypeScript支持，提升用户体验     |
| Monaco Editor | 0.45.x | VS Code的编辑器核心，适合SQL编辑器实现，TypeScript编写 |
| ECharts       | 5.4.x  | 功能强大的图表库，适合关系可视化和数据展示，提供类型定义          |
| vue-i18n      | 9.2.x  | 国际化支持，方便未来扩展多语言，支持TypeScript          |

### 2.3 DevOps工具

| 技术             | 推荐版本    | 选择理由                 |
|----------------|---------|----------------------|
| Docker         | 24.x    | 容器化部署，环境一致性，隔离性好     |
| Docker Compose | 2.23.x  | 多容器应用编排，简化开发和部署流程    |
| Jenkins        | 2.414.x | 自动化构建、测试和部署，CI/CD支持  |
| Prometheus     | 2.48.x  | 监控指标收集，适合监控查询性能和资源使用 |
| Grafana        | 10.2.x  | 可视化监控数据，直观展示系统状态     |

## 3. 可能的性能瓶颈及解决方案

### 3.1 数据源元数据同步性能

**潜在问题**：

- 大型数据库的元数据提取可能耗时，尤其是全量同步
- 增量同步策略可能不精确，导致不必要的更新
- 多个数据源同时同步可能导致系统资源竞争
- 每个数据源最多有100个表，每个数据源有不超过100个表，但表的数据量可能达到几亿级别

**解决方案**：

1. **采用分布式任务调度**：使用Spring Batch或Quartz配合Redis实现分布式调度
2. **增量同步优化**：基于时间戳和校验和的高效增量同步算法
3. **优先级同步队列**：根据数据源优先级和使用频率安排同步顺序
4. **异步处理**：使用Spring的@Async注解或CompletableFuture异步处理同步任务
5. **资源限制**：设置最大并发同步任务数，避免资源耗尽
6. **定向同步**：支持选择性同步特定Schema或Table，而非整个数据源
7. **智能调度**：在系统负载较低时执行同步任务，默认24小时同步周期

### 3.2 查询执行性能

**潜在问题**：

- 复杂SQL查询可能导致长时间执行
- 大结果集处理内存消耗大
- 外部数据源响应慢或不稳定
- 多用户并发查询导致系统负载高

**解决方案**：

1. **查询超时控制**：实现可配置的查询超时机制（默认30秒）
2. **结果分页处理**：强制分页返回结果，避免一次性加载大量数据
3. **查询优化建议**：集成SQL优化器，提供执行计划分析和优化建议
4. **连接池管理**：使用HikariCP等高性能连接池，合理配置连接池大小
5. **查询缓存**：对频繁执行的相同查询进行结果缓存
6. **慢查询日志**：记录并分析慢查询，帮助优化
7. **用户查询配额**：实现基于Redis的速率限制器，控制单用户查询频率
8. **CSV导出限制**：限制单次导出数据量不超过50000条

### 3.3 自然语言到SQL转换性能

**潜在问题**：

- LLM API调用延迟高
- 复杂的自然语言理解可能需要多次API调用
- 大量元数据和关系信息可能导致提示过长，超出LLM上下文窗口

**解决方案**：

1. **提示优化**：设计高效紧凑的提示模板，减少LLM输入长度
2. **元数据筛选**：基于自然语言内容智能筛选相关的表和列，只传递必要信息
3. **缓存常见查询**：缓存常见自然语言查询的SQL转换结果
4. **分层转换**：先确定查询意图和涉及的表，再分步构建复杂SQL
5. **本地预处理**：使用本地NLP组件预处理自然语言，减轻LLM负担
6. **请求批处理**：在高负载时实现请求队列和批处理机制
7. **备用LLM**：配置多个LLM提供商作为备选，实现自动故障转移
8. **LLM参数配置**：允许在配置文件中维护和调整LLM接口参数

### 3.4 关系推断性能

**潜在问题**：

- 基于数据内容分析的关系推断计算密集且耗时
- 对大表进行数据分析可能导致数据源负载增加
- 关系推断算法准确性与性能的平衡难以把握

**解决方案**：

1. **限制分析范围**：只分析表的样本数据，而非全表数据
2. **调度优化**：将关系推断任务安排在数据源低负载时段执行
3. **Progressive分析**：先使用轻量级算法快速推断，必要时再使用复杂算法深入分析
4. **缓存中间结果**：存储和复用分析的中间结果
5. **基于历史查询的优先推断**：优先分析被频繁查询的表之间的关系
6. **异步批处理**：将推断任务设计为异步长时间运行的批处理任务
7. **关系信息持久化**：将推断出的表关系持久化存储，避免重复计算

## 4. 系统安全考量

### 4.1 数据源密码保护

- 使用AES-GCM模式加密存储数据源密码
- 密钥管理与轮转策略
- 在内存中安全处理密码，避免日志泄露
- 密码解密仅在需要连接数据源时执行

### 4.2 SQL注入防护

- 对所有用户直接输入的SQL进行严格验证
- 使用参数化查询而非字符串拼接
- 实现SQL白名单过滤机制
- 限制允许的SQL操作类型（仅允许SELECT）
- 对LLM生成的SQL也进行安全校验

### 4.3 自然语言安全风险

- 防止通过精心构造的自然语言生成恶意SQL
- 对LLM生成的SQL强制检查，禁止执行非查询操作
- 提示安全原则，避免在提示中包含敏感数据
- 实现提示注入防护机制

### 4.4 API安全

- 实现适当的认证和授权机制
- API访问速率限制和防DoS措施
- 敏感API的访问日志审计
- 适当的CORS配置
- 请求参数验证和响应数据过滤

### 4.5 敏感数据保护

- 支持查询结果中的敏感数据掩码
- 敏感列自动识别和标记
- 日志中敏感信息的脱敏处理
- 实现细粒度的数据访问控制
- 对特定类型的数据（如个人信息）实现自动识别和保护
- 提供"查看"按钮功能，允许授权用户查看原始敏感数据

### 4.6 通信安全

- API和前端通信使用HTTPS
- 与外部LLM提供商的通信加密
- 与数据源的连接使用TLS/SSL（如可能）
- WebSocket连接的安全处理

### 4.7 容器和部署安全

- 容器镜像安全扫描
- 最小权限原则的容器配置
- 定期更新基础镜像和依赖
- 运行时环境的安全加固
- 机密信息使用Docker Secrets或环境变量管理

## 5. 编码标准与实践

### 5.1 Java编码规范

- 遵循Google Java风格指南
- 使用Lombok简化POJO代码
- 优先使用不可变对象和集合
- 使用Java Stream API进行集合操作
- 使用Optional处理可能为null的情况
- 异常处理策略：检查性异常转换为运行时异常，统一错误处理
- 日志级别正确使用：ERROR、WARN、INFO、DEBUG

### 5.2 TypeScript与Vue编码规范

- 遵循Airbnb TypeScript风格指南
- 优先使用TypeScript接口定义数据结构
- 使用Vue 3组合式API
- 基于功能的组件组织而非基于类型
- 组件通信：Props、Emits、Provide/Inject、Store
- 使用Composition API代替Mixins
- 命名约定：PascalCase用于组件，camelCase用于函数和变量
- 单文件组件结构：<script setup>, <template>, <style>顺序

### 5.3 REST API设计规范

- RESTful URL设计: `/api/resource/{id}`
- 正确使用HTTP方法：GET、POST、PUT、DELETE
- 一致的错误响应格式
- 分页参数标准：page, size, sort
- API版本控制策略：URL路径版本 `/api/v1/`
- 请求与响应数据格式标准化
- API文档实时更新与Swagger集成

### 5.4 数据库设计规范

- 命名约定：表名使用复数形式
- 主键策略：使用自增长整数或UUID
- 必须包含创建和更新时间字段
- 软删除实现：使用is_deleted标志
- 索引和约束命名规范
- 字段类型标准化与最佳实践
- 分表与分区策略

### 5.5 测试规范

- 单元测试覆盖率目标：80%以上
- 集成测试与API测试策略
- 测试命名约定：shouldXXXWhenYYY
- 测试环境和数据准备
- 模拟与存根的使用准则
- 性能测试基准和执行计划
- 测试驱动开发(TDD)期望

## 6. 部署与运维规范

### 6.1 环境配置标准

- 开发、测试、预发布、生产环境配置差异
- 配置外部化策略
- 敏感配置管理
- 环境变量使用准则
- 配置文件组织与加载顺序

### 6.2 容器化与编排标准

- Docker镜像构建最佳实践
- 多阶段构建优化
- 容器资源限制配置
- Docker Compose服务定义规范
- 健康检查配置

### 6.3 监控与日志标准

- 日志格式与结构化日志实践
- 监控指标定义与收集
- 告警策略与级别
- 应用性能监控点
- 分布式跟踪实现

### 6.4 CI/CD流程规范

- 分支策略与合并流程
- 自动化构建触发条件
- 测试自动化要求
- 部署审批流程
- 回滚策略与机制
