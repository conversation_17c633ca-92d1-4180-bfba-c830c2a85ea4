# 低代码域名配置问题解决方案总结

## 问题描述

用户反馈：点击发布低代码生成的预览链接，在本地启动项目没问题，域名是 `qaboss.yeepay.com`，但是前端发布到测试环境，生成的链接是生产的域名 `boss.yeepay.com`。

## 问题根因

之前的代码中，低代码预览链接的域名是通过 `import.meta.env.PROD` 来判断的：
- 开发环境（`PROD=false`）：使用 `qaboss.yeepay.com`（测试域名）
- 生产环境（`PROD=true`）：使用 `boss.yeepay.com`（生产域名）

**问题在于**：当前端发布到测试环境时，如果使用生产构建模式（`NODE_ENV=production`），`import.meta.env.PROD` 就会是 `true`，导致错误地使用生产域名。

## 解决方案

### 1. 引入新的环境变量

新增 `VITE_LOWCODE_DOMAIN` 环境变量来明确指定低代码平台的域名，不再依赖构建环境判断。

### 2. 配置优先级

1. **环境变量优先**：如果设置了 `VITE_LOWCODE_DOMAIN`，则使用该值
2. **构建环境判断**：如果未设置环境变量，则根据 `import.meta.env.PROD` 判断

### 3. 代码修改

更新了以下文件：
- `src/utils/config.ts` - 低代码配置和回调URL
- `src/components/integration/modals/PublishLowcodeModal.vue` - 发布弹窗中的预览链接
- `src/views/integration/IntegrationList.vue` - 集成列表中的预览功能

### 4. 环境配置文件

创建了新的环境配置：
- `.env.development` - 开发环境，使用 `qaboss.yeepay.com`
- `.env.test` - 测试环境，使用 `qaboss.yeepay.com`
- `.env.production` - 生产环境，使用 `boss.yeepay.com`

### 5. 构建脚本

添加了新的构建命令：
- `npm run build:test` - 测试环境构建
- `npm run preview:test` - 测试环境预览

## 部署指南

### 测试环境部署

使用以下任一方式：

1. **使用测试环境构建命令**：
   ```bash
   npm run build:test
   ```

2. **在构建时设置环境变量**：
   ```bash
   VITE_LOWCODE_DOMAIN=qaboss.yeepay.com npm run build
   ```

3. **在 CI/CD 中设置环境变量**：
   ```yaml
   variables:
     VITE_LOWCODE_DOMAIN: "qaboss.yeepay.com"
   ```

### 生产环境部署

继续使用原有的构建命令：
```bash
npm run build
```

## 验证方法

1. **运行验证脚本**：
   ```bash
   node scripts/verify-domain-config.js
   ```

2. **检查构建结果**：
   ```bash
   # 测试环境构建后检查
   npm run build:test
   grep -r "qaboss.yeepay.com" dist/
   
   # 生产环境构建后检查
   npm run build
   grep -r "boss.yeepay.com" dist/
   ```

## 测试结果

✅ 验证脚本通过所有检查
✅ 测试环境构建成功，正确使用测试域名
✅ 代码中所有相关文件已更新

## 总结

通过引入 `VITE_LOWCODE_DOMAIN` 环境变量，成功解决了测试环境错误使用生产域名的问题。现在可以在不同环境中灵活配置低代码平台的域名，确保预览链接指向正确的环境。
