# 域名配置问题解决方案

## 问题描述

在发布低代码时，预览链接的域名配置不正确：
- **本地开发环境**：预览链接正确显示为 `qaboss.yeepay.com`
- **QA测试环境**：预览链接错误显示为 `boss.yeepay.com`，应该是 `qaboss.yeepay.com`
- **生产环境**：预览链接应该显示为 `boss.yeepay.com`

## 根本原因

QA测试环境在构建时使用了生产构建模式，导致使用了生产环境的域名配置，而不是测试环境的配置。

## 解决方案

### 1. 环境配置文件

项目已经正确配置了三个环境的配置文件：

#### `.env.development` (本地开发)
```bash
VITE_LOWCODE_DOMAIN=qaboss.yeepay.com
```

#### `.env.test` (QA测试环境)
```bash
VITE_LOWCODE_DOMAIN=qaboss.yeepay.com
```

#### `.env.production` (生产环境)
```bash
VITE_LOWCODE_DOMAIN=boss.yeepay.com
```

### 2. 构建脚本配置

#### 方案A：使用专用的QA构建脚本

在QA环境部署时，使用专门的构建脚本：

```bash
# 使用QA专用构建脚本
./build-qa.sh
```

#### 方案B：设置环境变量

在CI/CD流水线中设置环境变量：

```bash
# QA环境构建
BUILD_ENV=test ./build.sh

# 或者直接使用npm脚本
npm run build:test
```

#### 方案C：修改GitLab CI配置

如果使用GitLab CI，可以在 `.gitlab-ci.yml` 中为不同环境设置不同的构建命令：

```yaml
# QA环境部署
deploy:qa:
  stage: deploy
  script:
    - npm run build:test  # 使用测试模式构建
  environment:
    name: qa
  only:
    - develop

# 生产环境部署
deploy:production:
  stage: deploy
  script:
    - npm run build:stable  # 使用生产模式构建
  environment:
    name: production
  only:
    - master
```

### 3. 验证方法

#### 本地验证
```bash
# 检查配置
node scripts/check-domain-config.cjs

# 测试构建
npm run build:test
npm run preview:test
```

#### 部署后验证
1. 在QA环境发布一个低代码集成
2. 检查预览链接是否为 `https://qaboss.yeepay.com/low-code-editor/...`
3. 在生产环境发布时，检查预览链接是否为 `https://boss.yeepay.com/low-code-editor/...`

## 立即解决步骤

### 对于QA环境管理员

1. **修改构建脚本**：
   ```bash
   # 将原来的构建命令
   npm run build
   
   # 改为
   npm run build:test
   # 或者使用
   ./build-qa.sh
   ```

2. **设置环境变量**（如果使用CI/CD）：
   ```bash
   export BUILD_ENV=test
   ./build.sh
   ```

3. **重新部署**：
   使用正确的构建命令重新构建和部署项目

### 对于开发人员

1. **本地测试**：
   ```bash
   # 测试QA环境构建
   npm run build:test
   npm run preview:test
   
   # 测试生产环境构建
   npm run build:stable
   npm run preview:stable
   ```

2. **验证配置**：
   ```bash
   node scripts/check-domain-config.cjs
   ```

## 技术细节

### 域名配置逻辑

代码中的域名配置逻辑（在 `PublishLowcodeModal.vue` 和 `IntegrationList.vue` 中）：

```javascript
// 优先使用环境变量配置的域名，如果未设置则根据环境判断
const domain = import.meta.env.VITE_LOWCODE_DOMAIN ||
               (import.meta.env.PROD ? 'boss.yeepay.com' : 'qaboss.yeepay.com');
```

### 构建模式说明

- `npm run build:test`：使用 `--mode test`，加载 `.env.test` 配置
- `npm run build:stable`：使用生产模式，加载 `.env.production` 配置
- `npm run build`：默认生产模式，加载 `.env.production` 配置

## 预防措施

1. **CI/CD配置**：确保不同环境使用正确的构建脚本
2. **文档更新**：更新部署文档，明确不同环境的构建方法
3. **自动化检查**：在构建后自动验证域名配置是否正确

## 联系支持

如果按照以上步骤操作后仍有问题，请联系开发团队并提供：
1. 当前使用的构建命令
2. 构建后的预览链接
3. 部署环境信息 