# DataScope系统需求分析文档

## 文档信息
- 版本: 1.0.0
- 创建日期: 2024-05-30
- 状态: 初稿

## 目录
1. [简介](#1-简介)
2. [用户群体](#2-用户群体)
3. [核心业务需求](#3-核心业务需求)
4. [详细功能需求](#4-详细功能需求)
5. [非功能性需求](#5-非功能性需求)
6. [技术约束](#6-技术约束)
7. [关键使用场景](#7-关键使用场景)
8. [实施优先级](#8-实施优先级)
9. [引用关系](#9-引用关系)
10. [变更记录](#10-变更记录)

## 1. 简介

DataScope是一个专为公司内部员工设计的智能化数据发现与查询系统。系统允许用户将不同的数据库系统（如MySQL、DB2）无缝集成为数据源，自动提取和管理元数据，并通过SQL或自然语言进行数据查询。系统特别强调了自然语言转SQL（NL2SQL）的能力，降低数据访问门槛。此外，系统支持表关系智能推断与手动维护，以及与低代码平台的深度集成，通过生成标准化的JSON配置和API接口，实现快速的应用开发和数据展示。

## 2. 用户群体

主要用户为公司内部员工，系统定位为内部信息管理系统。不同角色包括：

- **系统管理员**：负责数据源管理和系统配置，包括添加/删除数据源、配置同步策略、监控系统性能、设置查询限制等
- **数据分析人员**：进行数据查询和分析，包括编写SQL、使用自然语言查询、保存查询、导出数据等
- **开发人员**：将查询集成到低代码平台，包括配置页面展示、定义参数映射、管理API版本等

## 3. 核心业务需求

### 3.1 数据源管理
- 支持MySQL和DB2数据库集成
- 自动提取和维护元数据（模式、表、列等）
- 安全存储连接凭据（AES-GCM加密）
- 增量更新元数据（24小时同步周期）
- 系统内保存关系信息而非实际数据

### 3.2 查询管理
- 基于SQL的数据查询
- 自然语言转SQL功能
- 查询历史和收藏功能
- 表关系自动推断和手动维护
- 查询结果导出（CSV，上限50000条）

### 3.3 系统集成
- 为低代码平台提供页面配置JSON
- 统一的API接口
- 配置版本化管理
- 敏感数据掩码功能

## 4. 详细功能需求

### 4.1 数据源管理模块
- **添加/编辑/删除数据源**：提供界面添加新数据源，支持编辑现有数据源的连接信息，允许删除不再使用的数据源
- **测试连接功能**：在保存前验证数据源连接是否正常
- **手动触发元数据同步**：提供界面触发特定数据源的元数据同步
- **自动定时同步元数据**：支持配置自动同步周期，默认为24小时
- **数据源状态监控**：显示数据源的连接状态和上次同步时间

### 4.2 查询管理模块
- **SQL编辑和执行**：提供SQL编辑器，支持语法高亮和自动完成，执行查询并显示结果
- **自然语言转SQL**：通过对接OpenRouter LLM将自然语言描述转换为SQL查询
- **保存和组织查询**：允许用户命名和保存查询，分类和组织查询
- **查询历史查看和重用**：记录查询历史，允许浏览、搜索和重复执行历史查询
- **查询超时控制**：设置查询超时时间，默认为30秒
- **查询频率限制**：限制每个用户的查询频率，防止系统过载

### 4.3 元数据浏览模块
- **浏览数据源的模式、表、列**：提供层级导航视图浏览数据库对象
- **搜索元数据**：支持跨数据源搜索表和列
- **查看数据类型和描述**：显示列的数据类型、长度、是否可空等属性
- **表和列的标签和注释**：支持为表和列添加标签和注释

### 4.4 表关系管理模块
- **定义表间外键关系**：手动定义表之间的关系
- **自动推断表关系**：通过命名规则和数据分析自动推断表间关系
- **关系可视化**：图形化展示表关系
- **关系维护和管理**：管理已定义关系，包括编辑和删除

### 4.5 页面配置模块
- **配置查询参数的UI展示**：定义查询参数如何在UI中显示
- **配置结果列表的显示方式**：定义结果列的顺序、格式和可见性
- **敏感数据掩码配置**：为敏感列配置掩码规则
- **不同数据类型对应不同UI组件**：自动选择适合数据类型的UI组件，如日期选择器、数字输入框等
- **自动隐藏不常用查询条件**：根据使用频率自动隐藏不常用的查询条件

## 5. 非功能性需求

### 5.1 性能要求
- 支持最多100个数据源
- 每个数据源最多100个表
- 单次查询超时30秒
- CSV导出上限50000条
- 用户查询频率限制

### 5.2 安全要求
- 数据源密码AES-GCM加密存储
- SQL注入防护
- 敏感数据掩码功能
- API访问控制

### 5.3 可用性要求
- 直观的用户界面
- 提供多种数据展示形式
- 支持图表和表格视图
- 基于用户习惯优化界面

### 5.4 可扩展性要求
- 支持后续添加更多数据源类型
- 系统功能模块化设计
- 低代码平台集成的标准化接口

## 6. 技术约束

### 6.1 前端技术栈
- Vue 3.4+
- TypeScript 5.2+
- Tailwind CSS
- FontAwesome
- Ant Design Vue
- ECharts

### 6.2 后端技术栈
- Java 17
- Spring Boot 3.2+
- MyBatis 3.5+
- Maven 3.9+

### 6.3 部署环境
- MySQL 8.0+ 数据库
- Redis 7.0+ 缓存
- Docker 24+ 容器

## 7. 关键使用场景

### 7.1 数据源管理
系统管理员添加新数据源，提供连接信息，系统验证连接并提取元数据，24小时自动同步。

**步骤**：
1. 管理员登录系统，进入数据源管理页面
2. 点击"添加数据源"，填写数据源信息（名称、类型、连接URL、凭据等）
3. 点击"测试连接"验证连接有效性
4. 保存数据源，系统自动提取元数据
5. 系统每24小时自动同步元数据

### 7.2 SQL查询
用户浏览可用数据源，选择表和列，编写SQL查询，查看结果，保存或导出查询。

**步骤**：
1. 用户登录系统，进入查询编辑器
2. 在左侧元数据浏览器中选择数据源和表
3. 在SQL编辑器中编写查询（可通过拖拽表/列辅助）
4. 执行查询并查看结果
5. 可选择保存查询或导出结果为CSV

### 7.3 自然语言查询
用户用自然语言描述需求，系统转换为SQL，用户可以调整生成的SQL，执行查询。

**步骤**：
1. 用户在自然语言查询界面输入查询需求（如"显示近30天销售额最高的10个产品"）
2. 系统调用LLM服务将自然语言转换为SQL
3. 系统展示生成的SQL及可能的查询结果
4. 用户可以微调SQL或直接执行
5. 查询执行后，用户可以保存或导出结果

### 7.4 低代码集成
开发人员配置查询的UI展示，系统生成JSON配置，低代码平台使用这些配置生成应用界面。

**步骤**：
1. 开发人员选择已保存的查询
2. 配置查询参数的UI展示方式（如日期范围选择器、下拉列表等）
3. 配置结果列的显示方式（列顺序、格式、是否显示等）
4. 配置敏感数据掩码规则
5. 系统生成JSON配置和API端点
6. 低代码平台通过API获取配置并生成应用界面

## 8. 实施优先级

根据用户故事完成情况：
- **第一优先级**：已实现的21个用户故事的功能维护和完善
  - 数据源管理基础功能
  - SQL查询执行
  - 查询历史和收藏
  - 低代码集成基础功能
  
- **第二优先级**：7个开发中的用户故事
  - 自然语言查询功能
  - AI辅助界面配置
  - 智能数据推荐
  - 用户习惯分析优化
  
- **第三优先级**：其他新功能和优化
  - 更多数据源类型支持
  - 高级数据可视化
  - 性能优化
  - 使用体验改进

## 9. 引用关系

本文档与现有文档体系的关系：

- **需求文档.md**：提供高层需求概述，本文档对其进行详细分解
- **user-stories.md**：提供具体的用户故事，本文档中的功能需求可对应到特定用户故事
- **architecture/architecture-design.md**：提供系统架构设计，基于本文档中的需求

## 10. 变更记录

API端点的详细规格：[API设计文档](./api-design.md)