# 技术环境和开发设置

## 开发环境要求

### 前端开发环境
- Node.js >= 16.0.0
- npm >= 8.0.0
- Vue.js 3.x
- TypeScript 4.x
- VS Code 或 WebStorm

### 后端开发环境
- JDK 17
- Maven 3.8+
- Spring Boot 3.x
- IntelliJ IDEA

### 数据库环境
- MySQL 8.0+
- PostgreSQL 14+
- Oracle 19c+
- SQL Server 2019+

## 项目依赖

### 前端依赖
```json
{
  "vue": "^3.3.0",
  "typescript": "^4.9.0",
  "pinia": "^2.1.0",
  "vue-router": "^4.2.0",
  "element-plus": "^2.3.0",
  "tailwindcss": "^3.3.0",
  "axios": "^1.4.0"
}
```

### 后端依赖
```xml
<dependencies>
  <dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>3.1.0</version>
  </dependency>
  <dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
    <version>3.1.0</version>
  </dependency>
  <dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.3</version>
  </dependency>
</dependencies>
```

## 开发工具配置

### VS Code 推荐插件
- Volar
- TypeScript Vue Plugin
- ESLint
- Prettier
- Tailwind CSS IntelliSense

### IntelliJ IDEA 推荐插件
- Lombok
- Spring Boot Assistant
- Maven Helper
- SonarLint

## 开发规范

### 代码风格
- 使用 ESLint + Prettier
- 遵循 TypeScript 严格模式
- 使用 EditorConfig 统一编辑器配置
- 遵循 Google Java Style Guide

### Git 工作流
- 使用 Git Flow 工作流
- 遵循语义化版本控制
- 使用 Conventional Commits
- 代码审查必须

## 部署环境

### 开发环境
- Docker 容器化部署
- Jenkins CI/CD
- SonarQube 代码质量检查
- Nexus 私有仓库

### 测试环境
- 自动化测试集成
- 性能测试环境
- 安全测试环境
- 模拟生产数据

### 生产环境
- Kubernetes 集群
- 负载均衡
- 监控告警
- 数据备份 