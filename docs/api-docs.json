{"openapi": "3.0.1", "info": {"title": "DataScope API Documentation", "description": "API documentation for DataScope application", "version": "1.0"}, "servers": [{"url": "http://localhost:8080/data-scope", "description": "Generated server url"}], "tags": [{"name": "查询管理模块", "description": "查询管理相关接口"}], "paths": {"/api/queries/{id}": {"get": {"tags": ["查询管理模块"], "summary": "获取查询详情", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDTO"}}}}}}, "put": {"tags": ["查询管理模块"], "summary": "更新查询信息", "operationId": "updateQuery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDTO"}}}}}}, "delete": {"tags": ["查询管理模块"], "summary": "删除查询", "operationId": "deleteQuery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}}, "/api/queries/versions/{versionId}": {"get": {"tags": ["查询管理模块"], "summary": "获取查询版本详情", "operationId": "getQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}, "put": {"tags": ["查询管理模块"], "operationId": "updateQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/metadata/columns/{columnId}/decrypt-config": {"put": {"tags": ["元数据管理模块"], "summary": "更新字段解密配置", "operationId": "updateColumnDecryptConfig", "parameters": [{"name": "columnId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseVoid"}}}}}}}, "/api/metadata/columns/{columnId}/config": {"put": {"tags": ["元数据管理模块"], "summary": "更新字段元数据", "operationId": "updateColumnEntry", "parameters": [{"name": "columnId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseVoid"}}}}}}}, "/api/metadata/auth": {"put": {"tags": ["元数据管理模块"], "summary": "更新授权信息", "operationId": "authMetaData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseVoid"}}}}}}}, "/api/integrations/{id}": {"get": {"tags": ["集成管理模块"], "summary": "获取集成详情", "operationId": "getIntegrationById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}, "put": {"tags": ["集成管理模块"], "summary": "更新集成", "description": "更新已有的集成配置", "operationId": "updateIntegration", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}, "delete": {"tags": ["集成管理模块"], "summary": "删除集成", "operationId": "deleteIntegration", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseVoid"}}}}}}}, "/api/datasources/{id}": {"get": {"tags": ["数据源管理模块"], "operationId": "getDatasource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceDTO"}}}}}}, "put": {"tags": ["数据源管理模块"], "operationId": "updateDatasource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDataSourceRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceDTO"}}}}}}, "delete": {"tags": ["数据源管理模块"], "operationId": "deleteDatasource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBoolean"}}}}}}}, "/api/queries": {"get": {"tags": ["查询管理模块"], "summary": "获取查询列表", "operationId": "getQueries", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "queryType", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "serviceStatus", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "dataSourceId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDir", "in": "query", "required": false, "schema": {"type": "string", "default": "desc"}}, {"name": "includeDrafts", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}, "post": {"tags": ["查询管理模块"], "summary": "创建新查询", "operationId": "createQuery", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDTO"}}}}}}}, "/api/queries/{queryId}/versions/{versionId}/{integrationId}/execute-append": {"post": {"tags": ["查询管理模块"], "summary": "执行特定版本动态拼接查询", "operationId": "executeAppendQueryVersion", "parameters": [{"name": "queryId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "integrationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryResultDTO"}}}}}}}, "/api/queries/{queryId}/versions/{versionId}/execute": {"post": {"tags": ["查询管理模块"], "summary": "执行特定版本的查询", "description": "sort 值比如 id,desc  id  id,asc  这三种", "operationId": "executeQueryVersion", "parameters": [{"name": "queryId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryResultDTO"}}}}}}}, "/api/queries/{queryId}/versions/{versionId}/activate": {"post": {"tags": ["查询管理模块"], "summary": "激活查询版本", "operationId": "activateQueryVersion", "parameters": [{"name": "queryId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBoolean"}}}}}}}, "/api/queries/{id}/versions": {"get": {"tags": ["查询管理模块"], "summary": "获取查询版本列表", "operationId": "getQueryVersions", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}, "post": {"tags": ["查询管理模块"], "summary": "创建查询版本", "operationId": "createQueryVersion", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/queries/{id}/favorite": {"post": {"tags": ["查询管理模块"], "summary": "收藏查询", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}, "delete": {"tags": ["查询管理模块"], "summary": "取消收藏查询", "operationId": "unfavorite<PERSON><PERSON>y", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}}}}, "/api/queries/{id}/execute": {"post": {"tags": ["查询管理模块"], "summary": "执行查询", "operationId": "execute<PERSON>uery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteQueryParams"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryResultDTO"}}}}}}}, "/api/queries/{id}/enable": {"post": {"tags": ["查询管理模块"], "summary": "启用查询", "operationId": "enable<PERSON>uery", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBoolean"}}}}}}}, "/api/queries/{id}/disable": {"post": {"tags": ["查询管理模块"], "summary": "禁用查询", "operationId": "disable<PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseBoolean"}}}}}}}, "/api/queries/versions/{versionId}/publish": {"post": {"tags": ["查询管理模块"], "summary": "发布查询版本", "operationId": "publishQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/queries/versions/{versionId}/deprecate": {"post": {"tags": ["查询管理模块"], "summary": "废弃查询版本", "operationId": "deprecateQueryVersion", "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryVersionDTO"}}}}}}}, "/api/queries/nl-to-sql": {"post": {"tags": ["查询管理模块"], "summary": "自然语言转SQL", "operationId": "nlToSql", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NaturalLanguageQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseNaturalLanguageToSqlResult"}}}}}}}, "/api/queries/execution-plan": {"post": {"tags": ["查询管理模块"], "summary": "获取SQL执行计划（无需保存查询）", "operationId": "getExecutionPlanForSQL", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecutionPlanParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseExecutionPlanDTO"}}}}}}}, "/api/queries/execute-sql": {"post": {"tags": ["查询管理模块"], "summary": "直接执行SQL（无需保存查询）", "operationId": "executeSQL", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryResultDTO"}}}}}}}, "/api/queries/analyze-parameters": {"post": {"tags": ["查询管理模块"], "summary": "分析SQL获取查询参数、字段等信息", "operationId": "analyzeQueryParameters", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyzeQueryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDefinitionDTO"}}}}}}}, "/api/metadata/datasources/{dataSourceId}/sync": {"post": {"tags": ["元数据管理模块"], "summary": "同步元数据", "operationId": "syncMetadata", "parameters": [{"name": "dataSourceId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncMetadataRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseSyncMetadataResponse"}}}}}}}, "/api/integrations": {"get": {"tags": ["集成管理模块"], "summary": "获取集成列表", "operationId": "getIntegrationList", "parameters": [{"name": "param", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/IntegrationQueryParam"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponsePageResponseIntegrationDTO"}}}}}}, "post": {"tags": ["集成管理模块"], "summary": "创建集成", "description": "创建新的集成配置", "operationId": "createIntegration", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}}, "/api/integrations/execute-query": {"post": {"tags": ["集成管理模块"], "summary": "执行集成查询", "description": "执行集成的查询并返回结果", "operationId": "executeIntegrationQuery", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteIntegrationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}}}, "/api/integrations/api/integration/execute-query": {"post": {"tags": ["集成管理模块"], "summary": "执行集成查询(兼容旧路径)", "description": "兼容旧路径，请使用/api/integrations/execute-query", "operationId": "executeIntegrationQueryCompat", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteIntegrationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}, "deprecated": true}}, "/api/integration/execute-query": {"post": {"tags": ["集成管理兼容"], "operationId": "executeIntegrationQuery_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteIntegrationQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}, "deprecated": true}}, "/api/datasources": {"get": {"tags": ["数据源管理模块"], "operationId": "listDatasources", "parameters": [{"name": "param", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/DataSourceQueryParam"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponsePageResponseDataSourceDTO"}}}}}}, "post": {"tags": ["数据源管理模块"], "operationId": "createDatasource", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDataSourceRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceDTO"}}}}}}}, "/api/datasources/{id}/test-connection": {"post": {"tags": ["数据源管理模块"], "operationId": "testConnection", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseTestConnectionResultDTO"}}}}}}}, "/api/datasources/{id}/sync": {"post": {"tags": ["数据源管理模块"], "operationId": "syncDatasourceMetadata", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncDataSourceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseSyncStatusDTO"}}}}}}}, "/api/datasources/test-connection": {"post": {"tags": ["数据源管理模块"], "operationId": "testNewConnection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestConnectionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseTestConnectionResultDTO"}}}}}}}, "/api/integrations/{id}/status": {"patch": {"tags": ["集成管理模块"], "summary": "更新集成状态", "description": "更新集成的状态：ACTIVE、INACTIVE、DRAFT", "operationId": "updateIntegrationStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationStatusRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationDTO"}}}}}}}, "/api/queries/{id}/parameters": {"get": {"tags": ["查询管理模块"], "summary": "获取查询参数、字段等信息", "operationId": "getQueryParameters", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseQueryDefinitionDTO"}}}}}}}, "/api/queries/{id}/history": {"get": {"tags": ["查询管理模块"], "summary": "获取查询执行历史", "operationId": "getExecutionHistory", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}}, "/api/queries/{id}/execution-plan": {"get": {"tags": ["查询管理模块"], "summary": "获取查询执行计划", "operationId": "getExecutionPlan", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseExecutionPlanDTO"}}}}}}}, "/api/queries/favorites": {"get": {"tags": ["查询管理模块"], "summary": "获取收藏的查询列表", "operationId": "getFavorites", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "dataSourceId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseMapStringObject"}}}}}}}, "/api/metadata/tables/{tableId}/datas": {"get": {"tags": ["元数据管理模块"], "summary": "分页查询表数据", "operationId": "getTableData", "parameters": [{"name": "tableId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "request", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/TableDataQueryRequest"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseTableDataResponse"}}}}}}}, "/api/metadata/tables/{id}/columns": {"get": {"tags": ["元数据管理模块"], "summary": "获取表下的字段", "operationId": "getColumns", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListColumnDTO"}}}}}}}, "/api/metadata/schemas/{id}/tables": {"get": {"tags": ["元数据管理模块"], "summary": "获取schema下的表", "operationId": "getTables", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListTableDTO"}}}}}}}, "/api/metadata/datasources/{id}/schemas": {"get": {"tags": ["元数据管理模块"], "summary": "获取数据源下的schema列表", "operationId": "getSchemas", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseListSchemaDTO"}}}}}}}, "/api/integrations/{id}/preview": {"get": {"tags": ["集成管理模块"], "summary": "预览集成", "description": "预览集成的数据结果", "operationId": "previewIntegration", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseIntegrationQueryResultDTO"}}}}}}}, "/api/datasources/{id}/stats": {"get": {"tags": ["数据源管理模块"], "operationId": "getDatasourceStats", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceStatsDTO"}}}}}}}, "/api/datasources/{id}/check-status": {"get": {"tags": ["数据源管理模块"], "operationId": "checkDatasourceStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResponseDataSourceStatusDTO"}}}}}}}, "/test": {"get": {"tags": ["metric-controller"], "operationId": "test", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "put": {"tags": ["metric-controller"], "operationId": "test_3", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "post": {"tags": ["metric-controller"], "operationId": "test_2", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "delete": {"tags": ["metric-controller"], "operationId": "test_5", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "options": {"tags": ["metric-controller"], "operationId": "test_6", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "head": {"tags": ["metric-controller"], "operationId": "test_1", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "patch": {"tags": ["metric-controller"], "operationId": "test_4", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/metrics/healthcheck": {"get": {"tags": ["metric-controller"], "operationId": "healthCheck", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "put": {"tags": ["metric-controller"], "operationId": "healthCheck_3", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "post": {"tags": ["metric-controller"], "operationId": "healthCheck_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "delete": {"tags": ["metric-controller"], "operationId": "healthCheck_5", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "options": {"tags": ["metric-controller"], "operationId": "healthCheck_6", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "head": {"tags": ["metric-controller"], "operationId": "healthCheck_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "patch": {"tags": ["metric-controller"], "operationId": "healthCheck_4", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/": {"get": {"tags": ["metric-controller"], "operationId": "healthCheck_7", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "put": {"tags": ["metric-controller"], "operationId": "healthCheck_10", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "post": {"tags": ["metric-controller"], "operationId": "healthCheck_9", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "delete": {"tags": ["metric-controller"], "operationId": "healthCheck_12", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "options": {"tags": ["metric-controller"], "operationId": "healthCheck_13", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "head": {"tags": ["metric-controller"], "operationId": "healthCheck_8", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "patch": {"tags": ["metric-controller"], "operationId": "healthCheck_11", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}, "components": {"schemas": {"ResponseVoid": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "SaveQueryParams": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "dataSourceId": {"type": "string"}, "sql": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string"}, "serviceStatus": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "isPublic": {"type": "boolean"}, "queryType": {"type": "string"}}}, "DataSourceInfo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "icon": {"type": "string"}}}, "QueryDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "folderId": {"type": "string"}, "status": {"type": "string"}, "serviceStatus": {"type": "string"}, "dataSourceId": {"type": "string"}, "dataSourceName": {"type": "string"}, "queryType": {"type": "string"}, "queryText": {"type": "string"}, "resultCount": {"type": "integer", "format": "int32"}, "executionTime": {"type": "number"}, "error": {"type": "string"}, "isActive": {"type": "boolean"}, "isFavorite": {"type": "boolean"}, "createdBy": {"$ref": "#/components/schemas/UserInfo"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"$ref": "#/components/schemas/UserInfo"}, "updatedAt": {"type": "string", "format": "date-time"}, "executionCount": {"type": "integer", "format": "int32"}, "lastExecutedAt": {"type": "string", "format": "date-time"}, "tags": {"type": "array", "items": {"type": "string"}}, "currentVersion": {"$ref": "#/components/schemas/QueryVersionDTO"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParameterDTO"}}}}, "QueryParameterDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "label": {"type": "string"}, "defaultValue": {"type": "string"}, "required": {"type": "boolean"}, "options": {"type": "array", "items": {"type": "object"}}}}, "QueryVersionDTO": {"type": "object", "properties": {"id": {"type": "string"}, "queryId": {"type": "string"}, "versionNumber": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string"}, "sql": {"type": "string"}, "dataSourceId": {"type": "string"}, "dataSource": {"$ref": "#/components/schemas/DataSourceInfo"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParameterDTO"}}, "status": {"type": "string"}, "isLatest": {"type": "boolean"}, "createdBy": {"$ref": "#/components/schemas/UserInfo"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"$ref": "#/components/schemas/UserInfo"}, "updatedAt": {"type": "string", "format": "date-time"}, "comment": {"type": "string"}}}, "ResponseQueryDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryDTO"}}}, "UserInfo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "avatar": {"type": "string"}}}, "ResponseQueryVersionDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryVersionDTO"}}}, "AuthDTO": {"type": "object", "properties": {"type": {"type": "string", "description": "授权类型:TABLE, COLUMN, DATASOURCE, SCHEMA", "example": "COLUMN"}, "authRequired": {"type": "boolean", "description": "是否需要授权: ture, false"}, "id": {"type": "string", "description": "行主键id"}}, "description": "授权参数信息"}, "Config": {"type": "object", "properties": {"isFuzzyMatch": {"type": "boolean"}, "isMultiSelect": {"type": "boolean"}}}, "ExportConfig": {"type": "object", "properties": {"config": {"$ref": "#/components/schemas/Config"}}}, "IntegrationPointInfo": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "urlConfig": {"type": "object"}}}, "QueryParam": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "format": {"type": "string"}, "formType": {"type": "string"}, "required": {"type": "boolean"}, "displayOrder": {"type": "integer", "format": "int32"}, "defaultValue": {"type": "object"}, "options": {"type": "array", "items": {"type": "object"}}, "exportConfig": {"$ref": "#/components/schemas/ExportConfig"}, "tableName": {"type": "string"}}}, "UpdateIntegrationRequest": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "queryId": {"type": "string"}, "versionId": {"type": "string"}, "dataSourceId": {"type": "string"}, "queryParams": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParam"}}, "tableConfig": {"type": "object"}, "chartConfig": {"type": "object"}, "meta": {"type": "object"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}}}, "IntegrationDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "queryId": {"type": "string"}, "versionId": {"type": "string"}, "dataSourceId": {"type": "string"}, "queryParams": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParam"}}, "tableConfig": {"type": "object"}, "chartConfig": {"type": "object"}, "meta": {"type": "object"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointDTO"}, "nonce": {"type": "integer", "format": "int32"}, "createdBy": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "IntegrationPointDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "urlConfig": {"type": "object"}}}, "ResponseIntegrationDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/IntegrationDTO"}}}, "UpdateDataSourceRequest": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "syncFrequency": {"type": "string"}, "connectionParams": {"type": "string"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "string"}}}, "DataSourceDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "status": {"type": "string"}, "syncFrequency": {"type": "string"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "errorMessage": {"type": "string"}, "connectionParams": {"type": "string"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "string"}, "isActive": {"type": "boolean"}, "isAuthRequired": {"type": "boolean"}}}, "ResponseDataSourceDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataSourceDTO"}}}, "FieldInfo": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "label": {"type": "string"}, "isEncrypted": {"type": "boolean"}}}, "QueryResultDTO": {"type": "object", "properties": {"id": {"type": "string"}, "queryId": {"type": "string"}, "status": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "executionTime": {"type": "number", "format": "double"}, "rowCount": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}}, "error": {"type": "string"}, "warnings": {"type": "array", "items": {"type": "string"}}, "hasMore": {"type": "boolean"}, "total": {"type": "integer", "format": "int64"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}}}, "ResponseQueryResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryResultDTO"}}}, "ResponseBoolean": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "boolean"}}}, "ResponseString": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "string"}}}, "ExecuteQueryParams": {"type": "object", "properties": {"parameters": {"type": "object", "additionalProperties": {"type": "object"}}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sql": {"type": "string"}, "dataSourceId": {"type": "string"}, "sort": {"type": "string"}}}, "NaturalLanguageQueryParams": {"type": "object", "properties": {"dataSourceId": {"type": "string"}, "question": {"type": "string"}, "contextTables": {"type": "array", "items": {"type": "string"}}, "maxRows": {"type": "integer", "format": "int32"}, "timeout": {"type": "integer", "format": "int32"}}}, "NaturalLanguageToSqlResult": {"type": "object", "properties": {"sql": {"type": "string"}, "explanation": {"type": "string"}, "tables": {"type": "array", "items": {"type": "string"}}}}, "ResponseNaturalLanguageToSqlResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/NaturalLanguageToSqlResult"}}}, "ExecutionPlanParams": {"type": "object", "properties": {"dataSourceId": {"type": "string"}, "sql": {"type": "string"}, "parameters": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ExecutionPlanDTO": {"type": "object", "properties": {"id": {"type": "string"}, "queryId": {"type": "string"}, "createdAt": {"type": "string"}, "estimatedCost": {"type": "number", "format": "double"}, "estimatedRows": {"type": "integer", "format": "int64"}, "planningTime": {"type": "integer", "format": "int64"}, "executionTime": {"type": "integer", "format": "int64"}, "plan": {"type": "object"}, "planDetails": {"$ref": "#/components/schemas/PlanDetails"}}}, "PlanDetails": {"type": "object", "properties": {"totalCost": {"type": "number", "format": "double"}, "estimatedRows": {"type": "integer", "format": "int64"}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/PlanStep"}}}}, "PlanStep": {"type": "object", "properties": {"type": {"type": "string"}, "table": {"type": "string"}, "condition": {"type": "string"}, "cost": {"type": "number", "format": "double"}, "rows": {"type": "integer", "format": "int64"}, "index": {"type": "string"}, "columns": {"type": "array", "items": {"type": "string"}}}}, "ResponseExecutionPlanDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ExecutionPlanDTO"}}}, "AnalyzeQueryParams": {"type": "object", "properties": {"sqlText": {"type": "string"}, "dataSourceId": {"type": "string"}}}, "QueryDefinitionDTO": {"type": "object", "properties": {"sql": {"type": "string"}, "dataSourceId": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParameterDTO"}}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/QueryFieldDTO"}}, "searchFields": {"type": "array", "items": {"$ref": "#/components/schemas/QueryFieldDTO"}}}}, "QueryFieldDTO": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "label": {"type": "string"}, "tableName": {"type": "string"}, "isEncrypted": {"type": "boolean"}, "entryConfig": {"type": "object"}}}, "ResponseQueryDefinitionDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/QueryDefinitionDTO"}}}, "FiltersConfig": {"type": "object", "properties": {"includeSchemas": {"type": "array", "items": {"type": "string"}}, "excludeSchemas": {"type": "array", "items": {"type": "string"}}, "includeTables": {"type": "array", "items": {"type": "string"}}, "excludeTables": {"type": "array", "items": {"type": "string"}}}}, "SyncMetadataRequest": {"type": "object", "properties": {"filters": {"$ref": "#/components/schemas/FiltersConfig"}}}, "ResponseSyncMetadataResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SyncMetadataResponse"}}}, "SyncMetadataResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "syncId": {"type": "string"}, "dataSourceId": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "tablesCount": {"type": "integer", "format": "int32"}, "viewsCount": {"type": "integer", "format": "int32"}, "syncDuration": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "message": {"type": "string"}, "errors": {"type": "array", "items": {"type": "string"}}}}, "CreateIntegrationRequest": {"required": ["dataSourceId", "name", "queryId", "type"], "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "queryId": {"type": "string"}, "versionId": {"type": "string"}, "dataSourceId": {"type": "string"}, "queryParams": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParam"}}, "tableConfig": {"type": "object"}, "chartConfig": {"type": "object"}, "meta": {"type": "object"}, "integrationPoint": {"$ref": "#/components/schemas/IntegrationPointInfo"}}}, "ExecuteIntegrationQueryRequest": {"required": ["integrationId"], "type": "object", "properties": {"integrationId": {"type": "string"}, "parameters": {"type": "object"}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "ColumnDefinition": {"type": "object", "properties": {"field": {"type": "string"}, "label": {"type": "string"}, "type": {"type": "string"}}}, "IntegrationQueryResultDTO": {"type": "object", "properties": {"columns": {"type": "array", "items": {"$ref": "#/components/schemas/ColumnDefinition"}}, "rows": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}, "ResponseIntegrationQueryResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/IntegrationQueryResultDTO"}}}, "CreateDataSourceRequest": {"required": ["databaseName", "host", "name", "password", "port", "syncFrequency", "type", "username"], "type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "syncFrequency": {"type": "string"}, "connectionParams": {"type": "object"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "string"}}}, "ResponseTestConnectionResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/TestConnectionResultDTO"}}}, "TestConnectionResultDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}}, "SyncDataSourceRequest": {"type": "object", "properties": {"includeSchemas": {"type": "array", "items": {"type": "string"}}, "excludeSchemas": {"type": "array", "items": {"type": "string"}}, "includeTables": {"type": "array", "items": {"type": "string"}}, "excludeTables": {"type": "array", "items": {"type": "string"}}}}, "ResponseSyncStatusDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SyncStatusDTO"}}}, "SyncStatusDTO": {"type": "object", "properties": {"syncId": {"type": "string"}, "dataSourceId": {"type": "string"}, "dataSourceName": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "tablesCount": {"type": "integer", "format": "int32"}, "viewsCount": {"type": "integer", "format": "int32"}, "syncDuration": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "progress": {"type": "number", "format": "double"}, "message": {"type": "string"}, "errors": {"type": "array", "items": {"type": "string"}}, "operator": {"type": "string"}, "createdTime": {"type": "string", "format": "date-time"}, "modifiedTime": {"type": "string", "format": "date-time"}}}, "TestConnectionRequest": {"required": ["databaseName", "host", "password", "port", "type", "username"], "type": "object", "properties": {"type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "databaseName": {"type": "string"}, "database": {"type": "string"}, "schema": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "connectionParams": {"type": "object"}, "encryptionType": {"type": "string"}, "encryptionOptions": {"type": "string"}}}, "UpdateIntegrationStatusRequest": {"required": ["status"], "type": "object", "properties": {"status": {"type": "string"}}}, "ResponseMapStringObject": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"type": "object"}}}}, "TableDataQueryRequest": {"type": "object", "properties": {"page": {"minimum": 1, "type": "integer", "description": "页码，从1开始", "format": "int32", "example": 1}, "size": {"maximum": 1000, "minimum": 1, "type": "integer", "description": "每页记录数", "format": "int32", "example": 10}}, "description": "表数据查询请求"}, "ResponseTableDataResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/TableDataResponse"}}}, "TableDataResponse": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数", "format": "int64"}, "pages": {"type": "integer", "description": "总页数", "format": "int32"}, "page": {"type": "integer", "description": "当前页码", "format": "int32"}, "size": {"type": "integer", "description": "每页记录数", "format": "int32"}, "items": {"type": "array", "description": "数据列表，每行数据用Map表示，key为列名，value为列值", "items": {"type": "object", "additionalProperties": {"type": "object", "description": "数据列表，每行数据用Map表示，key为列名，value为列值"}, "description": "数据列表，每行数据用Map表示，key为列名，value为列值"}}}, "description": "表数据查询响应"}, "ColumnDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "dataType": {"type": "string"}, "columnType": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "isNullable": {"type": "boolean"}, "isPrimaryKey": {"type": "boolean"}, "isUnique": {"type": "boolean"}, "isIndexed": {"type": "boolean"}, "isEncrypted": {"type": "boolean"}, "defaultValue": {"type": "string"}, "characterLength": {"type": "integer", "format": "int32"}, "numericPrecision": {"type": "integer", "format": "int32"}, "numericScale": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "entryConfig": {"type": "object"}, "isAuthRequired": {"type": "boolean"}}}, "ResponseListColumnDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ColumnDTO"}}}}, "ResponseListTableDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TableDTO"}}}}, "TableDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "rowCount": {"type": "integer", "format": "int64"}, "columnsCount": {"type": "integer", "format": "int32"}, "isAuthRequired": {"type": "boolean"}}}, "ResponseListSchemaDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaDTO"}}}}, "SchemaDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "tablesCount": {"type": "integer", "format": "int32"}, "isAuthRequired": {"type": "boolean"}}}, "IntegrationQueryParam": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}}}, "PageResponseIntegrationDTO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "success": {"type": "boolean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationDTO"}}, "hasPrevious": {"type": "boolean"}, "hasNext": {"type": "boolean"}, "last": {"type": "boolean"}, "first": {"type": "boolean"}}}, "ResponsePageResponseIntegrationDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResponseIntegrationDTO"}}}, "DataSourceQueryParam": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}}}, "PageResponseDataSourceDTO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int32"}, "success": {"type": "boolean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DataSourceDTO"}}, "hasPrevious": {"type": "boolean"}, "hasNext": {"type": "boolean"}, "last": {"type": "boolean"}, "first": {"type": "boolean"}}}, "ResponsePageResponseDataSourceDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResponseDataSourceDTO"}}}, "DataSourceStatsDTO": {"type": "object", "properties": {"dataSourceId": {"type": "string"}, "tablesCount": {"type": "integer", "format": "int32"}, "viewsCount": {"type": "integer", "format": "int32"}, "totalRows": {"type": "integer", "format": "int64"}, "totalSize": {"type": "string"}, "lastUpdate": {"type": "string", "format": "date-time"}, "queriesCount": {"type": "integer", "format": "int32"}, "connectionPoolSize": {"type": "integer", "format": "int32"}, "activeConnections": {"type": "integer", "format": "int32"}, "avgQueryTime": {"type": "string"}, "totalTables": {"type": "integer", "format": "int32"}, "totalViews": {"type": "integer", "format": "int32"}, "totalQueries": {"type": "integer", "format": "int32"}}}, "ResponseDataSourceStatsDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataSourceStatsDTO"}}}, "DataSourceStatusDTO": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "isActive": {"type": "boolean"}, "lastCheckedAt": {"type": "string", "format": "date-time"}, "message": {"type": "string"}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ResponseDataSourceStatusDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataSourceStatusDTO"}}}}}}