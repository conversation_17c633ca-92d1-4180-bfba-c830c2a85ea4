# 消息通知系统迁移计划

## 背景

目前项目中存在三种消息通知系统：

1. **基于Pinia的messageStore**
   - 在`src/stores/message.ts`中定义
   - 通过`useMessageStore()`和`messageStore.success()`等方法调用

2. **基于服务的message**
   - 在`src/services/message.ts`中定义
   - 通过`message.success()`等方法直接调用
   - 已在`main.ts`中注册为全局服务

3. **Ant Design Vue的message系统**
   - 来自`ant-design-vue`库
   - 在查询模块的部分组件中使用

这种混合使用多种消息通知系统导致了界面交互的不一致，并可能造成消息提示缺失等问题。

## 已完成的迁移

- 数据源模块：已完全使用`message`服务
- 集成模块：已将`SaveManager.vue`和`IntegrationEditContainer.vue`从`messageStore`迁移到`message`服务

## 迁移计划

### 第一阶段：统一messageStore到message服务

1. 识别所有使用`messageStore`的组件：
   ```bash
   grep -r "import { useMessageStore }" src/
   grep -r "messageStore\." src/
   ```

2. 将这些组件中的`messageStore`替换为`message`服务：
   - 导入：`import { message } from '@/services/message';`
   - 替换调用：`messageStore.success()` -> `message.success()`

### 第二阶段：统一Ant Design Vue的message到message服务

1. 识别所有直接使用Ant Design Vue消息系统的组件：
   ```bash
   grep -r "import { message } from 'ant-design-vue'" src/
   ```

2. 将这些组件中的Ant Design消息替换为我们的`message`服务：
   - 修改导入：`import { message } from 'ant-design-vue'` -> `import { message } from '@/services/message';`

### 第三阶段：移除废弃的消息组件

1. 一旦确认所有组件都已迁移到`message`服务，考虑移除或弃用：
   - `src/stores/message.ts`（标记为废弃）
   - 最终可完全移除不再使用的消息系统

### 注意事项

1. 迁移过程需谨慎，每次修改一个文件并进行充分测试
2. 确保消息的持续时间、样式等参数正确迁移
3. 对于已使用`messageStore`的地方，检查是否有依赖其特殊功能的代码
4. 在迁移过程中及时更新CHANGELOG.md

## 统一后的优势

1. 代码一致性更好，减少学习和维护成本
2. 消息提示行为一致，提升用户体验
3. 简化代码，减少维护多套消息系统的开销
4. 便于未来统一扩展消息功能