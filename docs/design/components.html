<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件概览 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .component-card {
            transition: all 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="./components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="./basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="./basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="./basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="./basic/index.html" class="block text-indigo-600 hover:text-indigo-800 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="./forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="./forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="./forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="./forms/index.html" class="block text-indigo-600 hover:text-indigo-800 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="./container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="./container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="./container/index.html" class="block text-indigo-600 hover:text-indigo-800 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="./display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="./display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="./display/index.html" class="block text-indigo-600 hover:text-indigo-800 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./feedback/message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="./feedback/notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="./feedback/dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="./feedback/alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="./feedback/index.html" class="block text-indigo-600 hover:text-indigo-800 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="./animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="./animations/index.html" class="block text-indigo-600 hover:text-indigo-800 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="flex items-center mb-8">
                <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                    <i class="ri-home-line"></i>
                </a>
                <span class="text-gray-400 mx-2">/</span>
                <span class="text-gray-900 font-medium">组件</span>
            </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">组件库概览</h2>
            <p class="text-lg text-gray-600 max-w-3xl">
                DataScope UI 提供了丰富的组件，分为基础组件、表单组件、容器组件、数据展示和动画效果等类别，满足各种界面开发需求。
            </p>
        </div>

        <!-- 搜索框 -->
        <div class="mb-8">
            <div class="flex w-full max-w-2xl mx-auto">
                <div class="w-full relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="ri-search-line text-gray-400"></i>
                    </div>
                    <input type="text" class="w-full pl-10 pr-12 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="搜索组件...">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <span class="text-xs text-gray-400">按 / 搜索</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类导航 -->
        <div class="mb-8 bg-white rounded-lg shadow-md p-4">
            <h3 class="text-lg font-semibold mb-3">组件分类导航</h3>
            <div class="flex flex-wrap gap-3">
                <a href="#basic" class="px-4 py-2 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200 transition">
                    <i class="ri-layout-2-line mr-1"></i> 基础组件
                </a>
                <a href="#forms" class="px-4 py-2 bg-green-100 text-green-800 rounded-md hover:bg-green-200 transition">
                    <i class="ri-file-list-3-line mr-1"></i> 表单组件
                </a>
                <a href="#container" class="px-4 py-2 bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200 transition">
                    <i class="ri-layout-masonry-line mr-1"></i> 容器组件
                </a>
                <a href="#display" class="px-4 py-2 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 transition">
                    <i class="ri-table-line mr-1"></i> 数据展示
                </a>
                <a href="#feedback" class="px-4 py-2 bg-indigo-100 text-indigo-800 rounded-md hover:bg-indigo-200 transition">
                    <i class="ri-notification-3-line mr-1"></i> 反馈组件
                </a>
                <a href="#animations" class="px-4 py-2 bg-pink-100 text-pink-800 rounded-md hover:bg-pink-200 transition">
                    <i class="ri-slideshow-3-line mr-1"></i> 动画效果
                </a>
            </div>
        </div>
        
        <!-- 组件分类 -->
        <!-- 基础组件 -->
        <h2 id="basic" class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">基础组件</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 component-card">
                <div class="mb-4">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3">
                            <i class="ri-layout-2-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">基础组件</h3>
                    </div>
                </div>
                
                <p class="text-gray-600 mb-4">
                    构建界面的基础元素，包括按钮、标签、输入框等常用组件。
                </p>
                
                <ul class="space-y-2 mb-4">
                    <li>
                        <a href="./basic/buttons.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-radio-button-line mr-2 text-indigo-500"></i>
                            <span>按钮组件</span>
                        </a>
                    </li>
                    <li>
                        <a href="./basic/icon-buttons.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-cpu-line mr-2 text-indigo-500"></i>
                            <span>图标按钮</span>
                        </a>
                    </li>
                    <li>
                        <a href="./basic/inputs.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-input-cursor-move mr-2 text-indigo-500"></i>
                            <span>输入框组件</span>
                        </a>
                    </li>
                    <li>
                        <a href="./basic/badges.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-price-tag-3-line mr-2 text-indigo-500"></i>
                            <span>状态标签</span>
                        </a>
                    </li>
                </ul>
                
                <a href="./basic/index.html" class="inline-flex items-center text-indigo-600 hover:text-indigo-800">
                    查看全部基础组件 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
            
            </div>

        <!-- 表单组件 -->
        <h2 id="forms" class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">表单组件</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 component-card">
                <div class="mb-4">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                            <i class="ri-file-list-3-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">表单组件</h3>
                    </div>
                </div>
                
                <p class="text-gray-600 mb-4">
                    用于数据录入和表单处理的专用组件，支持验证和不同状态。
                </p>
                
                <ul class="space-y-2 mb-4">
                    <li>
                        <a href="./forms/basic-inputs.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-text mr-2 text-green-500"></i>
                            <span>基础输入</span>
                        </a>
                    </li>
                    <li>
                        <a href="./forms/selectors.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-list-check mr-2 text-green-500"></i>
                            <span>选择器</span>
                        </a>
                    </li>
                    <li>
                        <a href="./forms/toggles.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-toggle-line mr-2 text-green-500"></i>
                            <span>开关控件</span>
                        </a>
                    </li>
                    <li>
                        <a href="./forms/date-picker.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-calendar-line mr-2 text-green-500"></i>
                            <span>日期选择器</span>
                        </a>
                    </li>
                </ul>
                
                <a href="./forms/index.html" class="inline-flex items-center text-indigo-600 hover:text-indigo-800">
                    查看全部表单组件 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
            
            </div>
        
        <!-- 容器组件 -->
        <h2 id="container" class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">容器组件</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 component-card">
                <div class="mb-4">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center mr-3">
                            <i class="ri-layout-masonry-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">容器组件</h3>
                    </div>
                </div>
                
                <p class="text-gray-600 mb-4">
                    用于内容布局和信息分组的容器，提供不同的展示方式。
                </p>
                
                <ul class="space-y-2 mb-4">
                    <li>
                        <a href="./container/cards.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-layout-card-line mr-2 text-purple-500"></i>
                            <span>卡片容器</span>
                        </a>
                    </li>
                    <li>
                        <a href="./container/panels.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-layout-bottom-line mr-2 text-purple-500"></i>
                            <span>面板容器</span>
                        </a>
                    </li>
                    <li>
                        <a href="./container/boxes.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-box-3-line mr-2 text-purple-500"></i>
                            <span>盒子容器</span>
                        </a>
                    </li>
                </ul>
                
                <a href="./container/index.html" class="inline-flex items-center text-indigo-600 hover:text-indigo-800">
                    查看全部容器组件 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
            
            </div>

        <!-- 数据展示 -->
        <section id="display" class="mb-12">
            <h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">数据展示</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <a href="./display/data-table.html" class="block">
                        <div class="bg-white rounded-lg shadow-md component-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                                    <i class="ri-table-line text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">数据表格</h3>
                            </div>
                            <p class="text-gray-600 mb-4">用于展示大量结构化数据的表格组件，支持排序、筛选和分页。</p>
                        </div>
                    </a>
                    
                    <a href="./display/action-buttons.html" class="block">
                        <div class="bg-white rounded-lg shadow-md component-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 text-green-600 mr-4">
                                    <i class="ri-settings-line text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">操作按钮</h3>
                            </div>
                            <p class="text-gray-600 mb-4">用于表格和列表中的操作区域，提供查看、编辑、删除等常见操作。</p>
                            <div class="mt-4">
                                <div class="flex justify-center space-x-2">
                                    <button class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 bg-blue-100 hover:bg-blue-200">
                                        <i class="ri-eye-line"></i>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 bg-green-100 hover:bg-green-200">
                                        <i class="ri-edit-line"></i>
                                    </button>
                                    <button class="w-8 h-8 flex items-center justify-center rounded-full text-red-600 bg-red-100 hover:bg-red-200">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </a>

                    <a href="./display/page-header.html" class="block">
                        <div class="bg-white rounded-lg shadow-md component-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                                    <i class="ri-layout-top-line text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">页面标题</h3>
                            </div>
                            <p class="text-gray-600 mb-4">页面顶部的标题和功能区，包括返回按钮、标题、描述和操作按钮。</p>
                        </div>
                    </a>
                </div>
            </section>
            
                    </section>

        <!-- 反馈组件 -->
        <section id="feedback" class="mb-12">
            <h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">反馈组件</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <a href="./feedback/message.html" class="block">
                        <div class="bg-white rounded-lg shadow-md component-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-blue-600 mr-4">
                                    <i class="ri-message-2-line text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">消息提示</h3>
                            </div>
                            <p class="text-gray-600 mb-4">轻量级的反馈提示，用于操作后的结果展示。支持成功、警告、错误等不同状态。</p>
                            <div class="mt-4">
                                <div class="flex flex-col space-y-2">
                                    <div class="px-3 py-1 bg-green-100 text-green-800 rounded text-sm flex items-center">
                                        <i class="ri-check-line mr-2"></i> 操作成功
                                    </div>
                                    <div class="px-3 py-1 bg-red-100 text-red-800 rounded text-sm flex items-center">
                                        <i class="ri-close-circle-line mr-2"></i> 操作失败
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                    
                    <a href="./feedback/index.html" class="block">
                        <div class="bg-white rounded-lg shadow-md component-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 text-purple-600 mr-4">
                                    <i class="ri-notification-3-line text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">反馈组件</h3>
                            </div>
                            <p class="text-gray-600 mb-4">提供操作反馈的组件集合，包括通知提醒、对话框、警告提示、进度指示器和结果页。</p>
                            <div class="mt-4">
                                <div class="flex justify-center">
                                    <button class="px-4 py-2 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700">查看所有反馈组件</button>
                                </div>
                            </div>
                        </div>
                    </a>
                    
                    <a href="./feedback/dialog.html" class="block">
                        <div class="bg-white rounded-lg shadow-md component-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                                    <i class="ri-question-answer-line text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">对话框</h3>
                            </div>
                            <p class="text-gray-600 mb-4">模态对话框，用于重要操作的确认、信息展示和简单的表单输入。</p>
                            <div class="mt-4">
                                <div class="border border-gray-200 rounded-md p-3 text-center">
                                    <div class="text-sm font-medium mb-2">确认删除</div>
                                    <div class="flex justify-center space-x-2">
                                        <button class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">取消</button>
                                        <button class="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600">删除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </section>
            
                    </section>

        <!-- 动画效果 -->
        <h2 id="animations" class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">动画效果</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 component-card">
                <div class="mb-4">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-pink-100 text-pink-600 flex items-center justify-center mr-3">
                            <i class="ri-slideshow-3-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">动画效果</h3>
                    </div>
                </div>
                
                <p class="text-gray-600 mb-4">
                    为组件和页面添加生动的动画效果，提升用户体验。
                </p>
                
                <ul class="space-y-2 mb-4">
                    <li>
                        <a href="./animations/transitions.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-slideshow-3-line mr-2 text-pink-500"></i>
                            <span>过渡动画</span>
                        </a>
                    </li>
                    <li>
                        <a href="./animations/loading.html" class="flex items-center text-gray-700 hover:text-indigo-600 hover:bg-gray-50 px-2 py-1 rounded-md">
                            <i class="ri-loader-4-line mr-2 text-pink-500"></i>
                            <span>加载动画</span>
                        </a>
                    </li>
                </ul>
                
                <a href="./animations/index.html" class="inline-flex items-center text-indigo-600 hover:text-indigo-800">
                    查看全部动画效果 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
        </div>

        <!-- 使用指南和Vue3示例链接 -->
        <div class="flex flex-col md:flex-row gap-6 mb-12">
            <div class="md:w-1/2 bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center mr-3">
                        <i class="ri-book-open-line text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold">使用指南</h3>
                </div>
                
                <p class="text-gray-600 mb-4">
                    了解组件库的设计原则、使用规范和最佳实践，帮助您快速上手和高效开发。
                </p>
                
                <a href="./guides/index.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                    查看使用指南 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
            
            <div class="md:w-1/2 bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                        <i class="ri-vuejs-line text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold">Vue 3 示例</h3>
                </div>
                
                <p class="text-gray-600 mb-4">
                    基于 Vue 3 和 Composition API 使用组件库的示例代码和最佳实践。
                </p>
                
                <a href="./guides/vue3.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700">
                    查看 Vue 3 示例 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
        </div>

        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-home-line mr-1"></i> 返回首页
            </a>
        </div>
    </div>
    
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="./guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>