<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作按钮 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .action-button {
            transition: all 0.2s;
        }
        .action-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">组件</a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">数据展示</a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">操作按钮</span>
        </div>

        <div class="mb-12">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">操作按钮</h2>
            <p class="text-lg text-gray-600 max-w-3xl">
                操作按钮用于表格、列表等数据展示组件中，提供查看、编辑、删除等常见操作。设计合理的操作按钮可以让用户快速找到并执行所需的操作。
            </p>
        </div>

        <!-- 基础操作按钮 -->
        <section class="mb-16">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">基础操作按钮</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h4 class="text-lg font-semibold mb-4">常用操作图标</h4>
                <p class="text-gray-600 mb-6">以下是数据操作中最常用的操作按钮及其含义：</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 查看 -->
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 bg-blue-100 hover:bg-blue-200 action-button">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                        <p class="font-medium">查看</p>
                        <p class="text-sm text-gray-500 mt-1">查看详细信息</p>
                    </div>
                    
                    <!-- 编辑 -->
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 bg-green-100 hover:bg-green-200 action-button">
                                <i class="ri-edit-line"></i>
                            </button>
                        </div>
                        <p class="font-medium">编辑</p>
                        <p class="text-sm text-gray-500 mt-1">修改当前数据</p>
                    </div>
                    
                    <!-- 下载 -->
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-indigo-600 bg-indigo-100 hover:bg-indigo-200 action-button">
                                <i class="ri-download-line"></i>
                            </button>
                        </div>
                        <p class="font-medium">下载</p>
                        <p class="text-sm text-gray-500 mt-1">下载相关数据</p>
                    </div>
                    
                    <!-- 删除 -->
                    <div class="border border-gray-200 rounded-lg p-4 text-center">
                        <div class="flex justify-center mb-3">
                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-red-600 bg-red-100 hover:bg-red-200 action-button">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                        <p class="font-medium">删除</p>
                        <p class="text-sm text-gray-500 mt-1">删除当前数据</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h4 class="text-lg font-semibold mb-4">常见应用场景</h4>
                
                <div class="mb-6">
                    <h5 class="font-medium text-gray-900 mb-3">表格操作列</h5>
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">示例用户数据表格</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">已激活</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023/03/06 22:34</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div class="flex space-x-2">
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 hover:bg-blue-100 action-button">
                                                <i class="ri-eye-line"></i>
                                            </button>
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 hover:bg-green-100 action-button">
                                                <i class="ri-edit-line"></i>
                                            </button>
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-indigo-600 hover:bg-indigo-100 action-button">
                                                <i class="ri-download-line"></i>
                                            </button>
                                            <button class="w-8 h-8 flex items-center justify-center rounded-full text-red-600 hover:bg-red-100 action-button">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">设计规范</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">按钮设计原则</h5>
                        <ul class="list-disc pl-5 space-y-1 text-gray-600">
                            <li>使用直观的图标，确保用户一眼就能理解其含义</li>
                            <li>按照常见操作的重要性和使用频率排序</li>
                            <li>危险操作（如删除）应使用红色或需要额外确认</li>
                            <li>保持图标风格的一致性</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">颜色规范</h5>
                        <ul class="list-disc pl-5 space-y-1 text-gray-600">
                            <li>查看：蓝色 (#3B82F6)</li>
                            <li>编辑：绿色 (#10B981)</li>
                            <li>下载：靛蓝色 (#6366F1)</li>
                            <li>删除：红色 (#EF4444)</li>
                            <li>其他操作：灰色 (#6B7280)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 代码示例 -->
        <section class="mb-12">
            <h3 class="text-xl font-bold mb-6 pb-2 border-b border-gray-200">代码示例</h3>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold mb-4">Vue 3 实现</h4>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-6 overflow-auto">
                    <pre class="text-sm text-gray-800">
&lt;template&gt;
  &lt;div class="flex space-x-2"&gt;
    &lt;!-- 查看按钮 --&gt;
    &lt;button 
      class="w-8 h-8 flex items-center justify-center rounded-full text-blue-600 hover:bg-blue-100 transition-all"
      @click="handleView(row)"
      :title="'查看'"
    &gt;
      &lt;i class="ri-eye-line"&gt;&lt;/i&gt;
    &lt;/button&gt;
    
    &lt;!-- 编辑按钮 --&gt;
    &lt;button 
      class="w-8 h-8 flex items-center justify-center rounded-full text-green-600 hover:bg-green-100 transition-all"
      @click="handleEdit(row)"
      :title="'编辑'"
    &gt;
      &lt;i class="ri-edit-line"&gt;&lt;/i&gt;
    &lt;/button&gt;
    
    &lt;!-- 下载按钮 --&gt;
    &lt;button 
      class="w-8 h-8 flex items-center justify-center rounded-full text-indigo-600 hover:bg-indigo-100 transition-all"
      @click="handleDownload(row)"
      :title="'下载'"
    &gt;
      &lt;i class="ri-download-line"&gt;&lt;/i&gt;
    &lt;/button&gt;
    
    &lt;!-- 删除按钮 --&gt;
    &lt;button 
      class="w-8 h-8 flex items-center justify-center rounded-full text-red-600 hover:bg-red-100 transition-all"
      @click="handleDelete(row)"
      :title="'删除'"
    &gt;
      &lt;i class="ri-delete-bin-line"&gt;&lt;/i&gt;
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { defineProps } from 'vue'

const props = defineProps({
  row: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['view', 'edit', 'download', 'delete'])

const handleView = (row) => {
  emit('view', row)
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleDownload = (row) => {
  emit('download', row)
}

const handleDelete = (row) => {
  // 通常删除操作需要先确认
  if (confirm(`确定要删除 "${row.name}" 吗？`)) {
    emit('delete', row)
  }
}
&lt;/script&gt;
                    </pre>
                </div>
            </div>
        </section>

        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回数据展示组件
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>