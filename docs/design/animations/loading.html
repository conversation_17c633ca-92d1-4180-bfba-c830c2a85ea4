<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>加载动画 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    
    /* 加载动画 */
    /* 基础旋转加载 */
    .spinner {
      display: inline-block;
      border-radius: 50%;
      border-top: 2px solid transparent;
      border-right: 2px solid transparent;
      border-bottom: 2px solid transparent;
      border-left: 2px solid currentColor;
      animation: spin 0.8s linear infinite;
    }
    
    .spinner-sm {
      width: 16px;
      height: 16px;
    }
    
    .spinner-md {
      width: 24px;
      height: 24px;
    }
    
    .spinner-lg {
      width: 36px;
      height: 36px;
    }
    
    .spinner-xl {
      width: 48px;
      height: 48px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 圆形加载 */
    .circular-spinner {
      display: inline-block;
      position: relative;
      width: 36px;
      height: 36px;
    }
    
    .circular-spinner div {
      box-sizing: border-box;
      display: block;
      position: absolute;
      width: 30px;
      height: 30px;
      margin: 3px;
      border: 3px solid currentColor;
      border-radius: 50%;
      animation: circular-spinner 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
      border-color: currentColor transparent transparent transparent;
    }
    
    .circular-spinner div:nth-child(1) {
      animation-delay: -0.45s;
    }
    
    .circular-spinner div:nth-child(2) {
      animation-delay: -0.3s;
    }
    
    .circular-spinner div:nth-child(3) {
      animation-delay: -0.15s;
    }
    
    @keyframes circular-spinner {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 波浪加载 */
    .wave-spinner {
      display: inline-flex;
      align-items: flex-end;
      height: 30px;
    }
    
    .wave-spinner > div {
      width: 4px;
      height: 100%;
      margin: 0 2px;
      background-color: currentColor;
      animation: wave 1.2s infinite ease-in-out;
    }
    
    .wave-spinner > div:nth-child(2) {
      animation-delay: -1.1s;
    }
    
    .wave-spinner > div:nth-child(3) {
      animation-delay: -1.0s;
    }
    
    .wave-spinner > div:nth-child(4) {
      animation-delay: -0.9s;
    }
    
    .wave-spinner > div:nth-child(5) {
      animation-delay: -0.8s;
    }
    
    @keyframes wave {
      0%, 40%, 100% { transform: scaleY(0.4); }
      20% { transform: scaleY(1); }
    }
    
    /* 脉冲加载 */
    .pulse-spinner {
      display: inline-flex;
      align-items: center;
    }
    
    .pulse-spinner > div {
      width: 10px;
      height: 10px;
      margin: 0 2px;
      background-color: currentColor;
      border-radius: 50%;
      animation: pulse 1.4s infinite ease-in-out both;
    }
    
    .pulse-spinner > div:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    .pulse-spinner > div:nth-child(2) {
      animation-delay: -0.16s;
    }
    
    @keyframes pulse {
      0%, 80%, 100% { transform: scale(0); }
      40% { transform: scale(1); }
    }
    
    /* 骨架屏加载 */
    .skeleton {
      background: linear-gradient(110deg, #ebebeb 8%, #f5f5f5 18%, #ebebeb 33%);
      background-size: 200% 100%;
      animation: skeleton 1.5s linear infinite;
    }
    
    @keyframes skeleton {
      to { background-position-x: -200%; }
    }
    
    .skeleton-item {
      height: 20px;
      border-radius: 4px;
      margin-bottom: 8px;
    }
    
    /* 进度条 */
    .progress-bar {
      height: 6px;
      background-color: #e2e8f0;
      border-radius: 3px;
      overflow: hidden;
    }
    
    .progress-bar-fill {
      height: 100%;
      background-color: #4f46e5;
      border-radius: 3px;
      transition: width 0.3s ease;
    }
    
    /* 圆形进度条 */
    .circular-progress {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 60px;
    }
    
    .circular-progress svg {
      transform: rotate(-90deg);
    }
    
    .circular-progress-background {
      fill: none;
      stroke: #e2e8f0;
      stroke-width: 4;
    }
    
    .circular-progress-value {
      fill: none;
      stroke: #4f46e5;
      stroke-width: 4;
      stroke-linecap: round;
      transition: stroke-dashoffset 0.3s ease;
    }
    
    .circular-progress-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px;
      font-weight: 500;
    }
    
    /* 加载状态容器 */
    .loading-container {
      position: relative;
      min-height: 100px;
    }
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-5xl">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">加载动画</h1>
    <p class="text-lg text-gray-600 mb-8">
      加载动画为用户提供了操作反馈，让用户知道系统正在处理他们的请求，提高用户体验。
    </p>

    <!-- 基础加载动画 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">基础加载动画</h2>
      <p class="text-gray-600 mb-6">
        基础加载动画是一个简单的旋转图标，用于表示加载状态。
      </p>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div class="flex flex-col items-center">
          <div class="spinner spinner-sm text-blue-600"></div>
          <p class="text-sm text-gray-500 mt-2">超小尺寸</p>
        </div>
        <div class="flex flex-col items-center">
          <div class="spinner spinner-md text-blue-600"></div>
          <p class="text-sm text-gray-500 mt-2">小尺寸</p>
        </div>
        <div class="flex flex-col items-center">
          <div class="spinner spinner-lg text-blue-600"></div>
          <p class="text-sm text-gray-500 mt-2">中等尺寸</p>
        </div>
        <div class="flex flex-col items-center">
          <div class="spinner spinner-xl text-blue-600"></div>
          <p class="text-sm text-gray-500 mt-2">大尺寸</p>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">不同颜色</h3>
        <div class="flex flex-wrap gap-6">
          <div class="flex flex-col items-center">
            <div class="spinner spinner-md text-blue-600"></div>
            <p class="text-sm text-gray-500 mt-2">蓝色</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="spinner spinner-md text-green-600"></div>
            <p class="text-sm text-gray-500 mt-2">绿色</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="spinner spinner-md text-yellow-500"></div>
            <p class="text-sm text-gray-500 mt-2">黄色</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="spinner spinner-md text-red-600"></div>
            <p class="text-sm text-gray-500 mt-2">红色</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="spinner spinner-md text-gray-600"></div>
            <p class="text-sm text-gray-500 mt-2">灰色</p>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 基础加载动画示例 -->
<template>
  <!-- 基础加载动画 -->
  <Spinner size="xs" />
  <Spinner size="sm" />
  <Spinner size="md" />
  <Spinner size="lg" />
  
  <!-- 不同颜色 -->
  <Spinner type="primary" />
  <Spinner type="success" />
  <Spinner type="warning" />
  <Spinner type="danger" />
  <Spinner type="info" />
</template>
      </div>
    </div>

    <!-- 加载动画变体 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">加载动画变体</h2>
      <p class="text-gray-600 mb-6">
        不同风格的加载动画可以用于不同的场景，增加界面的视觉丰富性。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex flex-col items-center">
          <div class="circular-spinner text-blue-600">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <p class="text-sm text-gray-500 mt-2">圆形加载</p>
        </div>
        <div class="flex flex-col items-center">
          <div class="wave-spinner text-blue-600">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <p class="text-sm text-gray-500 mt-2">波浪加载</p>
        </div>
        <div class="flex flex-col items-center">
          <div class="pulse-spinner text-blue-600">
            <div></div>
            <div></div>
            <div></div>
          </div>
          <p class="text-sm text-gray-500 mt-2">脉冲加载</p>
        </div>
      </div>

      <div class="code-block">
<!-- 加载动画变体示例 -->
<template>
  <!-- 圆形加载 -->
  <Spinner type="circular" />
  
  <!-- 波浪加载 -->
  <Spinner type="wave" />
  
  <!-- 脉冲加载 -->
  <Spinner type="pulse" />
</template>
      </div>
    </div>

    <!-- 骨架屏 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">骨架屏</h2>
      <p class="text-gray-600 mb-6">
        骨架屏是一种占位符技术，在内容加载时显示页面的大致结构，减少用户等待时的空白感。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">文本骨架屏</h3>
          <div class="skeleton skeleton-item w-3/4"></div>
          <div class="skeleton skeleton-item w-full"></div>
          <div class="skeleton skeleton-item w-full"></div>
          <div class="skeleton skeleton-item w-2/3"></div>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">卡片骨架屏</h3>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center mb-4">
              <div class="skeleton w-12 h-12 rounded-full mr-3"></div>
              <div>
                <div class="skeleton skeleton-item w-32"></div>
                <div class="skeleton skeleton-item w-24 mb-0"></div>
              </div>
            </div>
            <div class="skeleton skeleton-item w-full"></div>
            <div class="skeleton skeleton-item w-full"></div>
            <div class="skeleton skeleton-item w-3/4"></div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">表格骨架屏</h3>
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <div class="skeleton h-12"></div>
          <div class="border-t border-gray-200">
            <div class="flex">
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4"></div>
            </div>
            <div class="flex">
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4"></div>
            </div>
            <div class="flex">
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4 border-r border-white"></div>
              <div class="skeleton h-10 w-1/4"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 骨架屏示例 -->
<template>
  <!-- 文本骨架屏 -->
  <Skeleton :rows="4" />
  
  <!-- 自定义骨架屏 -->
  <Skeleton>
    <template #default>
      <div class="flex">
        <SkeletonItem shape="circle" style="width: 40px; height: 40px" />
        <div class="ml-3">
          <SkeletonItem style="width: 120px; height: 16px; margin-bottom: 8px" />
          <SkeletonItem style="width: 80px; height: 16px" />
        </div>
      </div>
      <SkeletonItem style="margin-top: 16px" />
      <SkeletonItem />
      <SkeletonItem :width="200" />
    </template>
  </Skeleton>
  
  <!-- 表格骨架屏 -->
  <TableSkeleton :columns="4" :rows="3" />
</template>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">进度条</h2>
      <p class="text-gray-600 mb-6">
        进度条用于显示操作的进度，让用户了解当前任务完成的比例。
      </p>

      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">基础进度条</h3>
          <div class="space-y-4">
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm text-gray-500">默认</span>
                <span class="text-sm text-gray-500">30%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-bar-fill" style="width: 30%;"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm text-gray-500">成功</span>
                <span class="text-sm text-gray-500">50%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-bar-fill bg-green-500" style="width: 50%;"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm text-gray-500">警告</span>
                <span class="text-sm text-gray-500">70%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-bar-fill bg-yellow-500" style="width: 70%;"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm text-gray-500">危险</span>
                <span class="text-sm text-gray-500">90%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-bar-fill bg-red-500" style="width: 90%;"></div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">带条纹的进度条</h3>
          <div class="progress-bar">
            <div class="progress-bar-fill bg-blue-500" style="width: 60%; background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 1rem 1rem;"></div>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">圆形进度条</h3>
          <div class="flex flex-wrap gap-6">
            <div class="circular-progress">
              <svg viewBox="0 0 36 36" width="60" height="60">
                <circle class="circular-progress-background" cx="18" cy="18" r="15" />
                <circle class="circular-progress-value" cx="18" cy="18" r="15" stroke-dasharray="94.2" stroke-dashoffset="65.94" />
              </svg>
              <div class="circular-progress-text">30%</div>
            </div>
            <div class="circular-progress">
              <svg viewBox="0 0 36 36" width="60" height="60">
                <circle class="circular-progress-background" cx="18" cy="18" r="15" />
                <circle class="circular-progress-value" cx="18" cy="18" r="15" stroke-dasharray="94.2" stroke-dashoffset="47.1" style="stroke: #10b981;" />
              </svg>
              <div class="circular-progress-text">50%</div>
            </div>
            <div class="circular-progress">
              <svg viewBox="0 0 36 36" width="60" height="60">
                <circle class="circular-progress-background" cx="18" cy="18" r="15" />
                <circle class="circular-progress-value" cx="18" cy="18" r="15" stroke-dasharray="94.2" stroke-dashoffset="28.26" style="stroke: #eab308;" />
              </svg>
              <div class="circular-progress-text">70%</div>
            </div>
            <div class="circular-progress">
              <svg viewBox="0 0 36 36" width="60" height="60">
                <circle class="circular-progress-background" cx="18" cy="18" r="15" />
                <circle class="circular-progress-value" cx="18" cy="18" r="15" stroke-dasharray="94.2" stroke-dashoffset="9.42" style="stroke: #ef4444;" />
              </svg>
              <div class="circular-progress-text">90%</div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 进度条示例 -->
<template>
  <!-- 基础进度条 -->
  <Progress :percentage="30" />
  <Progress :percentage="50" type="success" />
  <Progress :percentage="70" type="warning" />
  <Progress :percentage="90" type="danger" />
  
  <!-- 带条纹的进度条 -->
  <Progress :percentage="60" stripe />
  
  <!-- 圆形进度条 -->
  <CircularProgress :percentage="30" />
  <CircularProgress :percentage="50" type="success" />
  <CircularProgress :percentage="70" type="warning" />
  <CircularProgress :percentage="90" type="danger" />
</template>
      </div>
    </div>

    <!-- 应用场景示例 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
      <p class="text-gray-600 mb-6">
        以下是加载动画在不同场景中的应用示例。
      </p>

      <div class="space-y-8">
        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">页面加载</h3>
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="p-4 flex justify-center items-center h-60 bg-gray-50">
              <div class="text-center">
                <div class="circular-spinner text-blue-600 inline-block">
                  <div></div>
                  <div></div>
                  <div></div>
                  <div></div>
                </div>
                <p class="text-gray-500 mt-2">数据加载中，请稍候...</p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">局部加载</h3>
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="p-4 loading-container">
              <div class="loading-overlay">
                <div class="spinner spinner-lg text-blue-600"></div>
              </div>
              <div class="p-4">
                <h4 class="text-lg font-medium mb-2">数据统计</h4>
                <div class="space-y-2">
                  <div class="grid grid-cols-2 gap-2">
                    <div class="p-3 bg-gray-100 rounded">总销售额: ¥10,234</div>
                    <div class="p-3 bg-gray-100 rounded">订单数: 123</div>
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    <div class="p-3 bg-gray-100 rounded">访问量: 1,234</div>
                    <div class="p-3 bg-gray-100 rounded">转化率: 5.6%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">按钮加载</h3>
          <div class="flex flex-wrap gap-4">
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition flex items-center disabled:opacity-70 disabled:cursor-not-allowed" disabled>
              <div class="spinner spinner-sm text-white mr-2"></div>
              提交中...
            </button>
            <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition flex items-center disabled:opacity-70 disabled:cursor-not-allowed" disabled>
              <div class="spinner spinner-sm text-white mr-2"></div>
              保存中...
            </button>
            <button class="border border-gray-300 hover:bg-gray-100 px-4 py-2 rounded transition flex items-center disabled:opacity-70 disabled:cursor-not-allowed" disabled>
              <div class="spinner spinner-sm text-gray-600 mr-2"></div>
              加载中...
            </button>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-medium mb-4 text-gray-700">文件上传进度</h3>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center mb-2">
              <i class="ri-file-line text-gray-500 mr-2"></i>
              <span>document.pdf</span>
              <span class="text-gray-500 text-sm ml-auto">2.5MB / 5MB</span>
            </div>
            <div class="progress-bar">
              <div class="progress-bar-fill" style="width: 50%;"></div>
            </div>
            <div class="text-right text-sm text-gray-500 mt-1">50%</div>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="./index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回动画效果
      </a>
    </div>
  </div>
</body>
</html>