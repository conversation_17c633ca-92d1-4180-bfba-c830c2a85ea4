<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>过渡动画 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    
    /* 过渡动画 */
    /* 淡入淡出 */
    .fade-enter {
      opacity: 0;
    }
    .fade-enter-active {
      opacity: 1;
      transition: opacity 300ms;
    }
    .fade-exit {
      opacity: 1;
    }
    .fade-exit-active {
      opacity: 0;
      transition: opacity 300ms;
    }
    
    /* 滑动 */
    .slide-enter {
      transform: translateX(-20px);
      opacity: 0;
    }
    .slide-enter-active {
      transform: translateX(0);
      opacity: 1;
      transition: all 300ms;
    }
    .slide-exit {
      transform: translateX(0);
      opacity: 1;
    }
    .slide-exit-active {
      transform: translateX(20px);
      opacity: 0;
      transition: all 300ms;
    }
    
    /* 缩放 */
    .zoom-enter {
      transform: scale(0.9);
      opacity: 0;
    }
    .zoom-enter-active {
      transform: scale(1);
      opacity: 1;
      transition: all 300ms;
    }
    .zoom-exit {
      transform: scale(1);
      opacity: 1;
    }
    .zoom-exit-active {
      transform: scale(0.9);
      opacity: 0;
      transition: all 300ms;
    }
    
    /* 折叠 */
    .collapse-enter {
      max-height: 0;
      overflow: hidden;
    }
    .collapse-enter-active {
      max-height: 200px;
      transition: max-height 300ms;
    }
    .collapse-exit {
      max-height: 200px;
    }
    .collapse-exit-active {
      max-height: 0;
      overflow: hidden;
      transition: max-height 300ms;
    }
    
    /* 示例盒子 */
    .demo-box {
      width: 100px;
      height: 100px;
      background-color: #4f46e5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
    }
    
    /* 折叠内容 */
    .collapse-content {
      background-color: #f1f5f9;
      border-radius: 4px;
      padding: 1rem;
    }
    
    /* 控制按钮 */
    .control-button {
      background-color: #4f46e5;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0.5rem 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .control-button:hover {
      background-color: #4338ca;
    }
    
    /* 动画控制区 */
    .animation-control {
      margin-top: 1rem;
      display: flex;
      gap: 0.5rem;
    }
    
    /* 演示容器 */
    .demo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 1rem;
      border: 1px dashed #e2e8f0;
      border-radius: 4px;
      min-height: 200px;
    }
    
    /* 动画触发区域 */
    .animation-trigger {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;
    }
    
    /* 动画显示区域 */
    .animation-display {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 120px;
    }
    
    .list-demo-item {
      background-color: #f1f5f9;
      padding: 0.5rem 1rem;
      margin-bottom: 0.5rem;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    /* 加载状态覆盖 */
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
    
    /* 示例动画 */
    .fade-demo-active {
      animation: fadeIn 0.5s ease-in-out;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .slide-demo-active {
      animation: slideIn 0.5s ease-in-out;
    }
    @keyframes slideIn {
      from { transform: translateX(-20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    .zoom-demo-active {
      animation: zoomIn 0.5s ease-in-out;
    }
    @keyframes zoomIn {
      from { transform: scale(0.8); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .collapse-demo-active {
      animation: collapseIn 0.5s ease-in-out;
    }
    @keyframes collapseIn {
      from { max-height: 0; overflow: hidden; }
      to { max-height: 200px; }
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- 头部导航 -->
  <header class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <img class="h-8 w-auto" src="../../assets/logo.png" alt="DataScope Logo">
          </div>
          <nav class="ml-6 flex space-x-8">
            <a href="../../index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
              首页
            </a>
            <a href="../index.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
              组件
            </a>
            <a href="../guides/getting-started.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
              使用指南
            </a>
            <a href="../sitemap.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
              网站地图
            </a>
          </nav>
        </div>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <a href="https://github.com/yourusername/datascope" target="_blank" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <i class="ri-github-fill mr-2"></i> GitHub
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 内容布局 -->
    <div class="flex flex-col md:flex-row">
      <!-- 左侧导航 -->
      <div class="w-full md:w-64 flex-shrink-0 mb-8 md:mb-0">
        <div class="bg-white p-5 rounded-lg shadow">
          <div class="mb-4">
            <h2 class="text-lg font-medium text-gray-900 mb-2">组件分类</h2>
          </div>
          
          <!-- 基础组件 -->
          <div class="mb-3">
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-2">基础组件</h3>
            <ul class="space-y-2">
              <li>
                <a href="../basic/buttons.html" class="text-gray-600 hover:text-indigo-600 block">
                  按钮 Button
                </a>
              </li>
              <li>
                <a href="../basic/icons.html" class="text-gray-600 hover:text-indigo-600 block">
                  图标 Icon
                </a>
              </li>
              <li>
                <a href="../basic/typography.html" class="text-gray-600 hover:text-indigo-600 block">
                  排版 Typography
                </a>
              </li>
            </ul>
          </div>
          
          <!-- 表单组件 -->
          <div class="mb-3">
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-2">表单组件</h3>
            <ul class="space-y-2">
              <li>
                <a href="../forms/basic-inputs.html" class="text-gray-600 hover:text-indigo-600 block">
                  输入框 Input
                </a>
              </li>
              <li>
                <a href="../forms/checkbox.html" class="text-gray-600 hover:text-indigo-600 block">
                  复选框 Checkbox
                </a>
              </li>
              <li>
                <a href="../forms/radio.html" class="text-gray-600 hover:text-indigo-600 block">
                  单选框 Radio
                </a>
              </li>
              <li>
                <a href="../forms/select.html" class="text-gray-600 hover:text-indigo-600 block">
                  选择器 Select
                </a>
              </li>
            </ul>
          </div>
          
          <!-- 容器组件 -->
          <div class="mb-3">
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-2">容器组件</h3>
            <ul class="space-y-2">
              <li>
                <a href="../container/cards.html" class="text-gray-600 hover:text-indigo-600 block">
                  卡片 Card
                </a>
              </li>
              <li>
                <a href="../container/tabs.html" class="text-gray-600 hover:text-indigo-600 block">
                  标签页 Tabs
                </a>
              </li>
            </ul>
          </div>
          
          <!-- 数据展示 -->
          <div class="mb-3">
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-2">数据展示</h3>
            <ul class="space-y-2">
              <li>
                <a href="../display/data-table.html" class="text-gray-600 hover:text-indigo-600 block">
                  数据表格 Table
                </a>
              </li>
              <li>
                <a href="../display/pagination.html" class="text-gray-600 hover:text-indigo-600 block">
                  分页 Pagination
                </a>
              </li>
            </ul>
          </div>
          
          <!-- 反馈组件 -->
          <div class="mb-3">
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-2">反馈组件</h3>
            <ul class="space-y-2">
              <li>
                <a href="../feedback/alert.html" class="text-gray-600 hover:text-indigo-600 block">
                  警告提示 Alert
                </a>
              </li>
              <li>
                <a href="../feedback/message.html" class="text-gray-600 hover:text-indigo-600 block">
                  全局提示 Message
                </a>
              </li>
              <li>
                <a href="../feedback/dialog.html" class="text-gray-600 hover:text-indigo-600 block">
                  对话框 Dialog
                </a>
              </li>
              <li>
                <a href="../feedback/notification.html" class="text-gray-600 hover:text-indigo-600 block">
                  通知提醒 Notification
                </a>
              </li>
            </ul>
          </div>
          
          <!-- 动画效果 -->
          <div class="mb-3">
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-2">动画效果</h3>
            <ul class="space-y-2">
              <li>
                <a href="../animations/transitions.html" class="text-indigo-600 font-medium block">
                  过渡动画 Transition
                </a>
              </li>
              <li>
                <a href="../animations/loaders.html" class="text-gray-600 hover:text-indigo-600 block">
                  加载动画 Loader
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容 -->
      <div class="flex-1 md:ml-8">
        <div class="bg-white rounded-lg shadow p-6">
          <!-- 面包屑导航 -->
          <div class="mb-6">
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-2">
                <li>
                  <a href="../../index.html" class="text-gray-500 hover:text-gray-700">首页</a>
                </li>
                <li class="flex items-center">
                  <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <a href="../index.html" class="ml-2 text-gray-500 hover:text-gray-700">组件</a>
                </li>
                <li class="flex items-center">
                  <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <a href="#" class="ml-2 text-gray-500 hover:text-gray-700">动画效果</a>
                </li>
                <li class="flex items-center">
                  <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <span class="ml-2 text-gray-900 font-medium">过渡动画</span>
                </li>
              </ol>
            </nav>
          </div>
          
          <!-- 当前位置 -->
          <div class="mt-2 mb-6 flex items-center">
            <i class="ri-transit-fill text-2xl text-indigo-600 mr-2"></i>
            <h1 class="text-2xl font-bold text-gray-900">过渡动画</h1>
          </div>
          
          <!-- 组件说明 -->
          <div class="prose max-w-none">
            <p class="text-gray-500 mb-8">
              过渡动画用于在元素进入或离开DOM时添加动画效果，可以使界面变化更加流畅自然，提升用户体验。DataScope提供了多种常用的过渡效果，如淡入淡出、滑动、缩放和折叠等。
            </p>
            
            <!-- 过渡动画示例 -->
            <div class="component-container">
              <h2 class="text-xl font-semibold text-gray-800 mb-4">淡入淡出</h2>
              <p class="text-gray-600 mb-4">最常用的过渡效果，通过改变透明度来实现元素的显示和隐藏。</p>
              
              <div class="demo-container">
                <div class="animation-trigger">
                  <button class="control-button" id="fadeToggle">显示/隐藏</button>
                </div>
                <div class="animation-display">
                  <div class="demo-box fade-demo" id="fadeDemo" style="display: none;">Fade</div>
                </div>
              </div>
              
              <div class="code-block">
<pre>/* CSS */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}</pre>
              </div>
            </div>
            
            <div class="component-container">
              <h2 class="text-xl font-semibold text-gray-800 mb-4">滑动</h2>
              <p class="text-gray-600 mb-4">元素从侧面滑入或滑出，常用于侧边栏或抽屉式菜单。</p>
              
              <div class="demo-container">
                <div class="animation-trigger">
                  <button class="control-button" id="slideToggle">显示/隐藏</button>
                </div>
                <div class="animation-display">
                  <div class="demo-box slide-demo" id="slideDemo" style="display: none;">Slide</div>
                </div>
              </div>
              
              <div class="code-block">
<pre>/* CSS */
.slide-enter {
  transform: translateX(-20px);
  opacity: 0;
}
.slide-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms;
}
.slide-exit {
  transform: translateX(0);
  opacity: 1;
}
.slide-exit-active {
  transform: translateX(20px);
  opacity: 0;
  transition: all 300ms;
}</pre>
              </div>
            </div>
            
            <div class="component-container">
              <h2 class="text-xl font-semibold text-gray-800 mb-4">缩放</h2>
              <p class="text-gray-600 mb-4">元素通过大小变化来显示和隐藏，常用于模态框、弹出菜单等。</p>
              
              <div class="demo-container">
                <div class="animation-trigger">
                  <button class="control-button" id="zoomToggle">显示/隐藏</button>
                </div>
                <div class="animation-display">
                  <div class="demo-box zoom-demo" id="zoomDemo" style="display: none;">Zoom</div>
                </div>
              </div>
              
              <div class="code-block">
<pre>/* CSS */
.zoom-enter {
  transform: scale(0.9);
  opacity: 0;
}
.zoom-enter-active {
  transform: scale(1);
  opacity: 1;
  transition: all 300ms;
}
.zoom-exit {
  transform: scale(1);
  opacity: 1;
}
.zoom-exit-active {
  transform: scale(0.9);
  opacity: 0;
  transition: all 300ms;
}</pre>
              </div>
            </div>
            
            <div class="component-container">
              <h2 class="text-xl font-semibold text-gray-800 mb-4">折叠</h2>
              <p class="text-gray-600 mb-4">元素通过高度变化来展开和收起，常用于手风琴面板、下拉菜单等。</p>
              
              <div class="demo-container">
                <div class="animation-trigger">
                  <button class="control-button" id="collapseToggle">展开/收起</button>
                </div>
                <div class="animation-display">
                  <div class="collapse-demo" id="collapseDemo" style="overflow: hidden; max-height: 0;">
                    <div class="collapse-content">
                      <p>这是一段折叠内容，点击按钮可以展开或收起。</p>
                      <p>折叠动画常用于隐藏非核心内容，使界面更加简洁。</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="code-block">
<pre>/* CSS */
.collapse-enter {
  max-height: 0;
  overflow: hidden;
}
.collapse-enter-active {
  max-height: 200px;
  transition: max-height 300ms;
}
.collapse-exit {
  max-height: 200px;
}
.collapse-exit-active {
  max-height: 0;
  overflow: hidden;
  transition: max-height 300ms;
}</pre>
              </div>
            </div>
            
            <div class="component-container">
              <h2 class="text-xl font-semibold text-gray-800 mb-4">列表动画</h2>
              <p class="text-gray-600 mb-4">列表项添加或删除时的动画效果，使列表变化更加流畅。</p>
              
              <div class="demo-container">
                <div class="animation-trigger">
                  <button class="control-button" id="addItem">添加项目</button>
                  <button class="control-button ml-2" id="removeItem">删除项目</button>
                </div>
                <div class="animation-display">
                  <div class="w-full">
                    <ul id="listDemo" class="w-full">
                      <li class="list-demo-item">项目 1 <span class="text-gray-400">默认项目</span></li>
                      <li class="list-demo-item">项目 2 <span class="text-gray-400">默认项目</span></li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div class="code-block">
<pre>/* 列表项进入动画 */
@keyframes itemEnter {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 列表项离开动画 */
@keyframes itemExit {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.item-enter {
  animation: itemEnter 0.3s ease forwards;
}

.item-exit {
  animation: itemExit 0.3s ease forwards;
}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 页脚 -->
  <footer class="bg-white">
    <div class="max-w-7xl mx-auto py-12 px-4 overflow-hidden sm:px-6 lg:px-8">
      <nav class="-mx-5 -my-2 flex flex-wrap justify-center" aria-label="Footer">
        <div class="px-5 py-2">
          <a href="../../index.html" class="text-base text-gray-500 hover:text-gray-900">首页</a>
        </div>
        <div class="px-5 py-2">
          <a href="../index.html" class="text-base text-gray-500 hover:text-gray-900">组件</a>
        </div>
        <div class="px-5 py-2">
          <a href="../guides/getting-started.html" class="text-base text-gray-500 hover:text-gray-900">使用指南</a>
        </div>
        <div class="px-5 py-2">
          <a href="../guides/changelog.html" class="text-base text-gray-500 hover:text-gray-900">更新日志</a>
        </div>
      </nav>
      <p class="mt-8 text-center text-base text-gray-400">
        &copy; 2023 DataScope UI组件库. All rights reserved.
      </p>
    </div>
  </footer>
  
  <script>
    // 示例脚本 - 控制动画演示
    document.addEventListener('DOMContentLoaded', function() {
      // 淡入淡出
      const fadeToggle = document.getElementById('fadeToggle');
      const fadeDemo = document.getElementById('fadeDemo');
      fadeToggle.addEventListener('click', function() {
        if (fadeDemo.style.display === 'none') {
          fadeDemo.style.display = 'flex';
          fadeDemo.classList.add('fade-demo-active');
        } else {
          fadeDemo.style.opacity = '0';
          setTimeout(() => {
            fadeDemo.style.display = 'none';
            fadeDemo.style.opacity = '1';
            fadeDemo.classList.remove('fade-demo-active');
          }, 300);
        }
      });
      
      // 滑动
      const slideToggle = document.getElementById('slideToggle');
      const slideDemo = document.getElementById('slideDemo');
      slideToggle.addEventListener('click', function() {
        if (slideDemo.style.display === 'none') {
          slideDemo.style.display = 'flex';
          slideDemo.style.transform = 'translateX(-20px)';
          slideDemo.style.opacity = '0';
          setTimeout(() => {
            slideDemo.style.transform = 'translateX(0)';
            slideDemo.style.opacity = '1';
            slideDemo.style.transition = 'all 300ms';
          }, 10);
        } else {
          slideDemo.style.transform = 'translateX(20px)';
          slideDemo.style.opacity = '0';
          slideDemo.style.transition = 'all 300ms';
          setTimeout(() => {
            slideDemo.style.display = 'none';
            slideDemo.style.transform = 'translateX(0)';
            slideDemo.style.opacity = '1';
            slideDemo.style.transition = 'none';
          }, 300);
        }
      });
      
      // 缩放
      const zoomToggle = document.getElementById('zoomToggle');
      const zoomDemo = document.getElementById('zoomDemo');
      zoomToggle.addEventListener('click', function() {
        if (zoomDemo.style.display === 'none') {
          zoomDemo.style.display = 'flex';
          zoomDemo.style.transform = 'scale(0.9)';
          zoomDemo.style.opacity = '0';
          setTimeout(() => {
            zoomDemo.style.transform = 'scale(1)';
            zoomDemo.style.opacity = '1';
            zoomDemo.style.transition = 'all 300ms';
          }, 10);
        } else {
          zoomDemo.style.transform = 'scale(0.9)';
          zoomDemo.style.opacity = '0';
          zoomDemo.style.transition = 'all 300ms';
          setTimeout(() => {
            zoomDemo.style.display = 'none';
            zoomDemo.style.transform = 'scale(1)';
            zoomDemo.style.opacity = '1';
            zoomDemo.style.transition = 'none';
          }, 300);
        }
      });
      
      // 折叠
      const collapseToggle = document.getElementById('collapseToggle');
      const collapseDemo = document.getElementById('collapseDemo');
      collapseToggle.addEventListener('click', function() {
        if (collapseDemo.style.maxHeight === '0px' || collapseDemo.style.maxHeight === '') {
          collapseDemo.style.maxHeight = '200px';
          collapseDemo.style.transition = 'max-height 300ms';
        } else {
          collapseDemo.style.maxHeight = '0px';
          collapseDemo.style.transition = 'max-height 300ms';
        }
      });
      
      // 列表动画
      const addItem = document.getElementById('addItem');
      const removeItem = document.getElementById('removeItem');
      const listDemo = document.getElementById('listDemo');
      let itemCount = 3;
      
      addItem.addEventListener('click', function() {
        const li = document.createElement('li');
        li.className = 'list-demo-item';
        li.innerHTML = `项目 ${itemCount} <span class="text-gray-400">新添加</span>`;
        li.style.opacity = '0';
        li.style.transform = 'translateY(-10px)';
        listDemo.appendChild(li);
        
        setTimeout(() => {
          li.style.opacity = '1';
          li.style.transform = 'translateY(0)';
          li.style.transition = 'all 300ms';
        }, 10);
        
        itemCount++;
      });
      
      removeItem.addEventListener('click', function() {
        const items = listDemo.querySelectorAll('li');
        if (items.length > 0) {
          const lastItem = items[items.length - 1];
          lastItem.style.opacity = '0';
          lastItem.style.transform = 'translateY(10px)';
          lastItem.style.transition = 'all 300ms';
          
          setTimeout(() => {
            listDemo.removeChild(lastItem);
          }, 300);
          
          if (itemCount > 3) {
            itemCount--;
          }
        }
      });
    });
  </script>
</body>
</html>