<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>表单组件库 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-card {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.2s ease-in-out;
    }
    .component-card:hover {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      transform: translateY(-2px);
    }
    .component-preview {
      padding: 1.5rem;
      background-color: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      min-height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .component-preview img {
      max-width: 100%;
      max-height: 120px;
    }
    .component-info {
      padding: 1rem;
      background-color: white;
    }
    .form-control {
      display: block;
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #333;
      background-color: #fff;
      background-clip: padding-box;
      border: 2px solid #cbd5e0;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .checkbox {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border: 2px solid #cbd5e0;
      border-radius: 4px;
      outline: none;
      cursor: pointer;
      position: relative;
      vertical-align: middle;
    }
    .checkbox:checked {
      background-color: #4f46e5;
      border-color: #4f46e5;
    }
    .checkbox:checked::after {
      content: '✓';
      font-size: 14px;
      color: white;
      position: absolute;
      top: -1px;
      left: 3px;
    }
    .radio {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border: 2px solid #cbd5e0;
      border-radius: 50%;
      outline: none;
      cursor: pointer;
      position: relative;
      vertical-align: middle;
    }
    .radio:checked {
      border-color: #4f46e5;
    }
    .radio:checked::after {
      content: '';
      width: 8px;
      height: 8px;
      background-color: #4f46e5;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 22px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #cbd5e0;
      transition: .4s;
      border-radius: 22px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #4f46e5;
    }
    input:checked + .slider:before {
      transform: translateX(20px);
    }
    .slider-input {
      -webkit-appearance: none;
      appearance: none;
      width: 100%;
      height: 6px;
      background: #e2e8f0;
      outline: none;
      border-radius: 3px;
    }
    .slider-input::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      background: #4f46e5;
      cursor: pointer;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-6xl">
    <h1 class="text-3xl font-bold mb-2 text-gray-800">表单组件库</h1>
    <p class="text-lg text-gray-600 mb-8">
      表单组件是用户输入数据和进行交互的主要界面元素，包括各种输入控件、选择器、切换组件以及表单布局组件。
    </p>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      <!-- 基础输入控件 -->
      <a href="./basic-inputs.html" class="component-card">
        <div class="component-preview">
          <div class="w-full max-w-xs">
            <div class="mb-4">
              <input type="text" class="form-control" placeholder="请输入内容">
            </div>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="ri-search-line text-gray-400"></i>
              </div>
              <input type="text" class="form-control pl-10" placeholder="搜索">
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">基础输入控件</h2>
          <p class="text-gray-600 text-sm">文本框、密码框、数字输入框等基础输入控件，用于接收用户的文本和数字输入。</p>
        </div>
      </a>

      <!-- 选择器控件 -->
      <a href="./selectors.html" class="component-card">
        <div class="component-preview">
          <div class="w-full max-w-xs space-y-4">
            <select class="form-control">
              <option value="">请选择</option>
              <option value="1">选项一</option>
              <option value="2">选项二</option>
            </select>
            <div class="form-control flex flex-wrap items-center px-2 py-1 min-h-[42px]">
              <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm mr-2 mb-1 flex items-center">
                选项一
                <button class="ml-1 text-blue-600 hover:text-blue-800">
                  <i class="ri-close-line"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">选择器控件</h2>
          <p class="text-gray-600 text-sm">下拉选择器、多选选择器等，用于从预定义的选项列表中选择一个或多个值。</p>
        </div>
      </a>

      <!-- 切换控件 -->
      <a href="./toggles.html" class="component-card">
        <div class="component-preview">
          <div class="w-full max-w-xs space-y-4">
            <div class="flex items-center space-x-6">
              <div class="flex items-center">
                <input type="checkbox" id="checkbox-preview" class="checkbox mr-2" checked>
                <label for="checkbox-preview">选项</label>
              </div>
              <div class="flex items-center">
                <input type="radio" id="radio-preview" class="radio mr-2" checked>
                <label for="radio-preview">选择</label>
              </div>
              <label class="switch">
                <input type="checkbox" checked>
                <span class="slider"></span>
              </label>
            </div>
            <input type="range" class="slider-input" value="50">
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">切换控件</h2>
          <p class="text-gray-600 text-sm">单选框、复选框、开关、滑块等，用于进行选择和调整数值的控件。</p>
        </div>
      </a>

      <!-- 表单布局 -->
      <a href="./form-layout.html" class="component-card">
        <div class="component-preview">
          <div class="w-full max-w-xs border border-gray-200 rounded-md p-4">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <input type="text" class="form-control" placeholder="请输入用户名">
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <input type="password" class="form-control" placeholder="请输入密码">
            </div>
            <button class="w-full bg-blue-600 text-white px-4 py-2 rounded">提交</button>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">表单布局</h2>
          <p class="text-gray-600 text-sm">表单项、表单分组、表单验证等布局组件，用于组织和结构化表单内容。</p>
        </div>
      </a>

      <!-- 日期时间 -->
      <div class="component-card opacity-60 cursor-not-allowed">
        <div class="component-preview">
          <div class="w-full max-w-xs space-y-4">
            <input type="text" class="form-control" placeholder="选择日期" value="2023-11-15">
            <div class="form-control flex items-center justify-between">
              <span>选择时间</span>
              <span class="text-blue-600">12:30</span>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">日期时间选择（开发中）</h2>
          <p class="text-gray-600 text-sm">日期选择器、时间选择器、日期范围选择器等，用于选择日期和时间。</p>
        </div>
      </div>

      <!-- 上传组件 -->
      <div class="component-card opacity-60 cursor-not-allowed">
        <div class="component-preview">
          <div class="w-full max-w-xs border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
            <div class="text-gray-500">
              <i class="ri-upload-cloud-line text-3xl mb-2"></i>
              <p>点击或拖拽文件到此区域上传</p>
            </div>
          </div>
        </div>
        <div class="component-info">
          <h2 class="text-lg font-semibold mb-1">上传组件（开发中）</h2>
          <p class="text-gray-600 text-sm">文件上传、图片上传、拖拽上传等，用于用户上传文件和图片。</p>
        </div>
      </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-sm mb-8">
      <h2 class="text-xl font-semibold mb-4">表单组件设计原则</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">一致性</h3>
          <p class="text-gray-600">保持所有表单控件的视觉风格和交互模式一致，使用户能够轻松理解如何使用不同的控件。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">反馈性</h3>
          <p class="text-gray-600">为用户操作提供及时、清晰的反馈，包括输入验证、错误提示和成功确认。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">可访问性</h3>
          <p class="text-gray-600">确保表单组件对所有用户都可访问，包括使用键盘导航和屏幕阅读器的用户。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">引导性</h3>
          <p class="text-gray-600">通过标签、占位符和帮助文本引导用户正确填写表单，减少错误和困惑。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">效率性</h3>
          <p class="text-gray-600">设计便于快速输入和提交的表单，减少用户完成任务所需的时间和精力。</p>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">容错性</h3>
          <p class="text-gray-600">设计能够容忍用户错误的表单，提供清晰的错误修复路径和数据恢复机制。</p>
        </div>
      </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-sm mb-12">
      <h2 class="text-xl font-semibold mb-4">使用指南</h2>
      <div class="space-y-4">
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">选择适当的控件</h3>
          <p class="text-gray-600">根据输入数据的类型和用户习惯选择最合适的表单控件：</p>
          <ul class="list-disc pl-6 mt-2 text-gray-600 space-y-1">
            <li>对于少量选项（通常少于5个）使用单选框或复选框</li>
            <li>对于大量选项使用下拉选择器</li>
            <li>对于二元选择使用开关组件</li>
            <li>对于连续范围的数值使用滑块</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">表单布局</h3>
          <p class="text-gray-600">选择合适的表单布局以提高可用性：</p>
          <ul class="list-disc pl-6 mt-2 text-gray-600 space-y-1">
            <li>对于复杂表单使用垂直布局，标签放在输入框上方</li>
            <li>对于简单表单可以使用水平布局或行内布局</li>
            <li>相关的表单项应当分组，并使用明确的视觉分隔</li>
            <li>必填项应当明确标注，通常使用星号（*）</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-medium mb-2 text-gray-800">表单验证</h3>
          <p class="text-gray-600">实施有效的表单验证策略：</p>
          <ul class="list-disc pl-6 mt-2 text-gray-600 space-y-1">
            <li>优先使用即时验证，在用户输入过程中提供反馈</li>
            <li>错误消息应当清晰具体，告知问题和解决方法</li>
            <li>对于复杂规则，提供示例和说明帮助用户理解</li>
            <li>在提交前验证整个表单，并突出显示所有错误</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="../index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回组件总览
      </a>
    </div>
  </div>
</body>
</html>