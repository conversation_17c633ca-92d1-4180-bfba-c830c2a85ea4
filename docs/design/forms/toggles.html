<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>表单切换控件 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    .label {
      display: inline-block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #4a5568;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    .help-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #718096;
    }
    
    /* 单选框样式 */
    .radio-container {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      cursor: pointer;
    }
    .radio {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border: 2px solid #cbd5e0;
      border-radius: 50%;
      outline: none;
      cursor: pointer;
      position: relative;
    }
    .radio:checked {
      border-color: #4f46e5;
    }
    .radio:checked::after {
      content: '';
      width: 10px;
      height: 10px;
      background-color: #4f46e5;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .radio-label {
      margin-left: 0.5rem;
    }
    
    /* 复选框样式 */
    .checkbox-container {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      cursor: pointer;
    }
    .checkbox {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border: 2px solid #cbd5e0;
      border-radius: 4px;
      outline: none;
      cursor: pointer;
      position: relative;
    }
    .checkbox:checked {
      background-color: #4f46e5;
      border-color: #4f46e5;
    }
    .checkbox:checked::after {
      content: '';
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(45deg);
    }
    .checkbox-label {
      margin-left: 0.5rem;
    }
    
    /* 开关样式 */
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 22px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #cbd5e0;
      transition: .4s;
      border-radius: 22px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #4f46e5;
    }
    input:checked + .slider:before {
      transform: translateX(20px);
    }
    
    /* 滑块样式 */
    .slider-container {
      width: 100%;
      padding: 1rem 0;
    }
    .slider-input {
      -webkit-appearance: none;
      appearance: none;
      width: 100%;
      height: 6px;
      background: #e2e8f0;
      outline: none;
      border-radius: 3px;
    }
    .slider-input::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      background: #4f46e5;
      cursor: pointer;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .slider-input::-moz-range-thumb {
      width: 18px;
      height: 18px;
      background: #4f46e5;
      cursor: pointer;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    /* 按钮组单选样式 */
    .radio-button-group {
      display: inline-flex;
      border-radius: 0.25rem;
      overflow: hidden;
      border: 1px solid #cbd5e0;
    }
    .radio-button {
      padding: 0.5rem 1rem;
      background: white;
      cursor: pointer;
      border-right: 1px solid #cbd5e0;
      transition: all 0.15s ease-in-out;
    }
    .radio-button:last-child {
      border-right: none;
    }
    .radio-button-selected {
      background: #4f46e5;
      color: white;
    }
    .radio-button:hover:not(.radio-button-selected) {
      background: #f1f5f9;
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-5xl">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">表单切换控件</h1>
    <p class="text-lg text-gray-600 mb-8">
      切换控件用于用户进行选择和调整数值，包括单选框、复选框、开关、滑块等组件。这些控件提供了直观的界面，方便用户快速操作。
    </p>

    <!-- 单选框 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">单选框</h2>
      <p class="text-gray-600 mb-6">
        单选框用于在一组互斥选项中选择一个，通常用于选项较少，需要全部展示的场景。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">基础单选框</label>
          <div class="space-y-2">
            <label class="radio-container">
              <input type="radio" name="radio-demo" class="radio">
              <span class="radio-label">选项一</span>
            </label>
            <label class="radio-container">
              <input type="radio" name="radio-demo" class="radio" checked>
              <span class="radio-label">选项二</span>
            </label>
            <label class="radio-container">
              <input type="radio" name="radio-demo" class="radio">
              <span class="radio-label">选项三</span>
            </label>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="space-y-2">
            <label class="radio-container opacity-50 cursor-not-allowed">
              <input type="radio" name="radio-disabled" class="radio" disabled>
              <span class="radio-label">禁用未选中</span>
            </label>
            <label class="radio-container opacity-50 cursor-not-allowed">
              <input type="radio" name="radio-disabled" class="radio" checked disabled>
              <span class="radio-label">禁用已选中</span>
            </label>
          </div>
        </div>
      </div>
      
      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">按钮样式单选</h3>
        <div class="radio-button-group">
          <div class="radio-button">选项一</div>
          <div class="radio-button radio-button-selected">选项二</div>
          <div class="radio-button">选项三</div>
        </div>
      </div>

      <div class="code-block">
<!-- 单选框示例 -->
<template>
  <!-- 基础单选框 -->
  <RadioGroup v-model="selectedValue">
    <Radio value="option1">选项一</Radio>
    <Radio value="option2">选项二</Radio>
    <Radio value="option3">选项三</Radio>
  </RadioGroup>

  <!-- 禁用状态 -->
  <RadioGroup v-model="disabledValue">
    <Radio value="option1" disabled>禁用未选中</Radio>
    <Radio value="option2" disabled>禁用已选中</Radio>
  </RadioGroup>

  <!-- 按钮样式单选 -->
  <RadioButtonGroup v-model="buttonValue">
    <RadioButton value="option1">选项一</RadioButton>
    <RadioButton value="option2">选项二</RadioButton>
    <RadioButton value="option3">选项三</RadioButton>
  </RadioButtonGroup>
</template>
      </div>
    </div>

    <!-- 复选框 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">复选框</h2>
      <p class="text-gray-600 mb-6">
        复选框用于从一组选项中选择多个值，也可用于单个选项的选择。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">基础复选框</label>
          <div class="space-y-2">
            <label class="checkbox-container">
              <input type="checkbox" class="checkbox">
              <span class="checkbox-label">选项一</span>
            </label>
            <label class="checkbox-container">
              <input type="checkbox" class="checkbox" checked>
              <span class="checkbox-label">选项二</span>
            </label>
            <label class="checkbox-container">
              <input type="checkbox" class="checkbox">
              <span class="checkbox-label">选项三</span>
            </label>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="space-y-2">
            <label class="checkbox-container opacity-50 cursor-not-allowed">
              <input type="checkbox" class="checkbox" disabled>
              <span class="checkbox-label">禁用未选中</span>
            </label>
            <label class="checkbox-container opacity-50 cursor-not-allowed">
              <input type="checkbox" class="checkbox" checked disabled>
              <span class="checkbox-label">禁用已选中</span>
            </label>
          </div>
        </div>
      </div>
      
      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">中间状态</h3>
        <label class="checkbox-container">
          <input type="checkbox" class="checkbox" style="position: relative;">
          <span class="checkbox-label">全选</span>
        </label>
        <div class="pl-8 mt-2 space-y-2">
          <label class="checkbox-container">
            <input type="checkbox" class="checkbox" checked>
            <span class="checkbox-label">选项一</span>
          </label>
          <label class="checkbox-container">
            <input type="checkbox" class="checkbox">
            <span class="checkbox-label">选项二</span>
          </label>
        </div>
      </div>

      <div class="code-block">
<!-- 复选框示例 -->
<template>
  <!-- 基础复选框 -->
  <CheckboxGroup v-model="checkedValues">
    <Checkbox value="option1">选项一</Checkbox>
    <Checkbox value="option2">选项二</Checkbox>
    <Checkbox value="option3">选项三</Checkbox>
  </CheckboxGroup>

  <!-- 禁用状态 -->
  <Checkbox value="disabled1" disabled>禁用未选中</Checkbox>
  <Checkbox value="disabled2" disabled :checked="true">禁用已选中</Checkbox>

  <!-- 中间状态（半选） -->
  <Checkbox 
    :indeterminate="indeterminate" 
    :checked="checkAll" 
    @change="handleCheckAllChange"
  >
    全选
  </Checkbox>
  <div class="pl-8 mt-2">
    <CheckboxGroup v-model="checkedList" @change="handleCheckedChange">
      <Checkbox value="option1">选项一</Checkbox>
      <Checkbox value="option2">选项二</Checkbox>
    </CheckboxGroup>
  </div>
</template>
      </div>
    </div>

    <!-- 开关 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">开关</h2>
      <p class="text-gray-600 mb-6">
        开关是一种特殊的复选框，用于表示开启/关闭两种状态的切换，通常用于设置选项。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">基础开关</label>
          <div class="flex items-center">
            <label class="switch">
              <input type="checkbox">
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">默认开启</label>
          <div class="flex items-center">
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">带文字描述</label>
          <div class="flex items-center">
            <label class="switch mr-2">
              <input type="checkbox" checked>
              <span class="slider"></span>
            </label>
            <span class="text-sm text-green-600">开启</span>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="flex items-center">
            <label class="switch opacity-50 cursor-not-allowed">
              <input type="checkbox" disabled>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 开关示例 -->
<template>
  <!-- 基础开关 -->
  <Switch v-model="value1" />

  <!-- 默认开启 -->
  <Switch v-model="value2" default-checked />

  <!-- 带文字描述 -->
  <div class="flex items-center">
    <Switch v-model="value3" />
    <span class="ml-2">{{ value3 ? '开启' : '关闭' }}</span>
  </div>

  <!-- 禁用状态 -->
  <Switch v-model="value4" disabled />
</template>
      </div>
    </div>

    <!-- 滑块 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">滑块</h2>
      <p class="text-gray-600 mb-6">
        滑块允许用户通过滑动的方式在一个固定区间内选择一个值，适用于需要选择大量连续值的场景。
      </p>

      <div class="space-y-6">
        <div class="form-group">
          <label class="label">基础滑块</label>
          <div class="slider-container">
            <input type="range" class="slider-input" min="0" max="100" value="50">
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">带刻度和数值显示</label>
          <div class="slider-container">
            <input type="range" class="slider-input" min="0" max="100" value="30">
            <div class="flex justify-between text-xs text-gray-500 mt-1">
              <span>0</span>
              <span>20</span>
              <span>40</span>
              <span>60</span>
              <span>80</span>
              <span>100</span>
            </div>
            <div class="text-center mt-2">
              <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">当前值: 30</span>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="slider-container opacity-50">
            <input type="range" class="slider-input" min="0" max="100" value="40" disabled>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 滑块示例 -->
<template>
  <!-- 基础滑块 -->
  <Slider v-model="sliderValue1" :min="0" :max="100" />

  <!-- 带刻度和数值显示 -->
  <Slider 
    v-model="sliderValue2" 
    :min="0" 
    :max="100" 
    :step="20" 
    show-stops 
    show-tooltip
  />

  <!-- 禁用状态 -->
  <Slider v-model="sliderValue3" disabled />

  <!-- 范围滑块 -->
  <RangeSlider 
    v-model="rangeValue" 
    :min="0" 
    :max="100" 
    show-tooltip
  />
</template>
      </div>
    </div>

    <!-- 评分 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">评分</h2>
      <p class="text-gray-600 mb-6">
        评分组件允许用户对项目进行评级，通常使用星级显示。
      </p>

      <div class="space-y-6">
        <div class="form-group">
          <label class="label">基础评分</label>
          <div class="flex items-center text-2xl text-yellow-400">
            <i class="ri-star-fill"></i>
            <i class="ri-star-fill"></i>
            <i class="ri-star-fill"></i>
            <i class="ri-star-line"></i>
            <i class="ri-star-line"></i>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">带半星</label>
          <div class="flex items-center text-2xl text-yellow-400">
            <i class="ri-star-fill"></i>
            <i class="ri-star-fill"></i>
            <i class="ri-star-half-fill"></i>
            <i class="ri-star-line"></i>
            <i class="ri-star-line"></i>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">自定义图标</label>
          <div class="flex items-center text-2xl text-pink-500">
            <i class="ri-heart-fill"></i>
            <i class="ri-heart-fill"></i>
            <i class="ri-heart-fill"></i>
            <i class="ri-heart-line"></i>
            <i class="ri-heart-line"></i>
          </div>
        </div>
        
        <div class="form-group">
          <label class="label">只读状态</label>
          <div class="flex items-center text-2xl text-yellow-400">
            <i class="ri-star-fill"></i>
            <i class="ri-star-fill"></i>
            <i class="ri-star-fill"></i>
            <i class="ri-star-fill"></i>
            <i class="ri-star-half-fill"></i>
            <span class="ml-2 text-base text-gray-600">4.5分</span>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 评分示例 -->
<template>
  <!-- 基础评分 -->
  <Rate v-model="rateValue1" />

  <!-- 带半星 -->
  <Rate v-model="rateValue2" allow-half />

  <!-- 自定义图标 -->
  <Rate v-model="rateValue3" character="♥" />

  <!-- 只读状态 -->
  <Rate v-model="rateValue4" disabled />
  <span>{{ rateValue4 }}分</span>
</template>
      </div>
    </div>

    <!-- 应用场景 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
      <p class="text-gray-600 mb-6">
        以下是切换控件在实际场景中的应用示例。
      </p>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">个人偏好设置</h3>
        <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
          <h4 class="text-xl font-semibold mb-6">系统设置</h4>
          
          <div class="divide-y">
            <div class="py-4 flex items-center justify-between">
              <div>
                <h5 class="font-medium">深色模式</h5>
                <p class="text-sm text-gray-500">切换界面为深色主题</p>
              </div>
              <label class="switch">
                <input type="checkbox">
                <span class="slider"></span>
              </label>
            </div>
            
            <div class="py-4">
              <h5 class="font-medium mb-2">通知设置</h5>
              <div class="space-y-2 ml-2">
                <label class="checkbox-container">
                  <input type="checkbox" class="checkbox" checked>
                  <span class="checkbox-label">消息通知</span>
                </label>
                <label class="checkbox-container">
                  <input type="checkbox" class="checkbox" checked>
                  <span class="checkbox-label">系统通知</span>
                </label>
                <label class="checkbox-container">
                  <input type="checkbox" class="checkbox">
                  <span class="checkbox-label">营销信息</span>
                </label>
              </div>
            </div>
            
            <div class="py-4">
              <h5 class="font-medium mb-2">语言</h5>
              <div class="space-y-2 ml-2">
                <label class="radio-container">
                  <input type="radio" name="language" class="radio" checked>
                  <span class="radio-label">简体中文</span>
                </label>
                <label class="radio-container">
                  <input type="radio" name="language" class="radio">
                  <span class="radio-label">English</span>
                </label>
                <label class="radio-container">
                  <input type="radio" name="language" class="radio">
                  <span class="radio-label">日本語</span>
                </label>
              </div>
            </div>
            
            <div class="py-4">
              <h5 class="font-medium mb-2">字体大小</h5>
              <div class="slider-container mt-2">
                <input type="range" class="slider-input" min="12" max="24" value="16">
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>小</span>
                  <span>中</span>
                  <span>大</span>
                </div>
              </div>
            </div>
            
            <div class="py-4">
              <h5 class="font-medium mb-2">主题颜色</h5>
              <div class="flex space-x-3 mt-2">
                <button class="w-8 h-8 rounded-full bg-blue-600 ring-2 ring-offset-2 ring-blue-600"></button>
                <button class="w-8 h-8 rounded-full bg-green-600"></button>
                <button class="w-8 h-8 rounded-full bg-purple-600"></button>
                <button class="w-8 h-8 rounded-full bg-red-600"></button>
                <button class="w-8 h-8 rounded-full bg-yellow-500"></button>
              </div>
            </div>
          </div>
          
          <div class="mt-6 flex justify-end">
            <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out mr-2">
              取消
            </button>
            <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
              保存
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="../index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回组件总览
      </a>
    </div>
  </div>
</body>
</html>