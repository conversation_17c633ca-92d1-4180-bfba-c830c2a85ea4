<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>表单选择器控件 - DataScope UI组件库</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
  <style>
    .component-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    .code-block {
      background-color: #f8fafc;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      font-family: monospace;
      font-size: 14px;
      white-space: pre-wrap;
      color: #334155;
    }
    .form-control {
      display: block;
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #333;
      background-color: #fff;
      background-clip: padding-box;
      border: 2px solid #cbd5e0;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .form-control:focus {
      border-color: #4a6cf7;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
    }
    .form-control:disabled {
      background-color: #f1f5f9;
      opacity: 1;
    }
    .select-wrapper {
      position: relative;
    }
    .select-wrapper::after {
      content: '';
      position: absolute;
      right: 14px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #64748b;
      pointer-events: none;
    }
    .label {
      display: inline-block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #4a5568;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    .help-text {
      display: block;
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #718096;
    }
    .select-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      border: 1px solid #cbd5e0;
      border-radius: 0.25rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      background: white;
      z-index: 10;
      margin-top: 0.25rem;
      max-height: 200px;
      overflow-y: auto;
    }
    .select-option {
      padding: 0.5rem 0.75rem;
      cursor: pointer;
    }
    .select-option:hover {
      background-color: #f1f5f9;
    }
    .select-option.selected {
      background-color: #e6f7ff;
      color: #1890ff;
    }
    .checkbox {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border: 2px solid #cbd5e0;
      border-radius: 4px;
      outline: none;
      cursor: pointer;
      position: relative;
      vertical-align: middle;
    }
    .checkbox:checked {
      background-color: #4f46e5;
      border-color: #4f46e5;
    }
    .checkbox:checked::after {
      content: '✓';
      font-size: 14px;
      color: white;
      position: absolute;
      top: -1px;
      left: 3px;
    }
    .radio {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border: 2px solid #cbd5e0;
      border-radius: 50%;
      outline: none;
      cursor: pointer;
      position: relative;
      vertical-align: middle;
    }
    .radio:checked {
      border-color: #4f46e5;
    }
    .radio:checked::after {
      content: '';
      width: 8px;
      height: 8px;
      background-color: #4f46e5;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 22px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #cbd5e0;
      transition: .4s;
      border-radius: 22px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #4f46e5;
    }
    input:checked + .slider:before {
      transform: translateX(20px);
    }
  </style>
</head>
<body class="bg-gray-50 p-8">
  <div class="container mx-auto max-w-5xl">
    <h1 class="text-3xl font-bold mb-8 text-gray-800">表单选择器控件</h1>
    <p class="text-lg text-gray-600 mb-8">
      选择器控件提供了多种选择操作的界面，包括下拉选择、单选框、复选框和开关等，用于从预定义的选项中进行选择。
    </p>

    <!-- 下拉选择器 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">下拉选择器</h2>
      <p class="text-gray-600 mb-6">
        下拉选择器用于从多个选项中选择一个，适用于选项较多的情况。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">基础选择器</label>
          <div class="select-wrapper">
            <select class="form-control">
              <option value="">请选择</option>
              <option value="1">选项一</option>
              <option value="2">选项二</option>
              <option value="3">选项三</option>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="select-wrapper">
            <select class="form-control" disabled>
              <option value="1">选项一</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 自定义下拉示例 -->
      <div class="mt-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">自定义下拉菜单</h3>
        <div class="form-group">
          <label class="label">自定义下拉</label>
          <div class="relative">
            <div class="select-wrapper">
              <input type="text" class="form-control" placeholder="请选择" readonly value="选项二">
            </div>
            <!-- 显示一个示例下拉菜单 -->
            <div class="select-dropdown">
              <div class="select-option">选项一</div>
              <div class="select-option selected">选项二</div>
              <div class="select-option">选项三</div>
              <div class="select-option">选项四</div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 下拉选择器示例 -->
<template>
  <!-- 基础选择器 -->
  <FormItem label="选择项">
    <Select v-model="value" placeholder="请选择">
      <Option v-for="item in options" :key="item.value" :value="item.value" :label="item.label" />
    </Select>
  </FormItem>

  <!-- 禁用状态 -->
  <FormItem label="选择项">
    <Select v-model="value" disabled>
      <Option v-for="item in options" :key="item.value" :value="item.value" :label="item.label" />
    </Select>
  </FormItem>

  <!-- 可搜索 -->
  <FormItem label="选择项">
    <Select v-model="value" filterable placeholder="请输入关键词搜索">
      <Option v-for="item in options" :key="item.value" :value="item.value" :label="item.label" />
    </Select>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 多选选择器 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">多选选择器</h2>
      <p class="text-gray-600 mb-6">
        多选选择器允许从一个选项列表中选择多个值。
      </p>

      <div class="form-group">
        <label class="label">多选示例</label>
        <div class="select-wrapper">
          <select class="form-control" multiple size="4">
            <option value="1">选项一</option>
            <option value="2" selected>选项二</option>
            <option value="3" selected>选项三</option>
            <option value="4">选项四</option>
          </select>
        </div>
        <span class="help-text">按住Ctrl键可多选</span>
      </div>

      <div class="mt-6">
        <div class="form-group">
          <label class="label">标签式多选</label>
          <div class="relative">
            <div class="form-control flex flex-wrap items-center px-2 py-1 min-h-[42px]">
              <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm mr-2 mb-1 flex items-center">
                选项二
                <button class="ml-1 text-blue-600 hover:text-blue-800">
                  <i class="ri-close-line"></i>
                </button>
              </div>
              <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm mr-2 mb-1 flex items-center">
                选项三
                <button class="ml-1 text-blue-600 hover:text-blue-800">
                  <i class="ri-close-line"></i>
                </button>
              </div>
              <input type="text" class="outline-none border-none px-1 py-0 flex-grow min-w-[100px]" placeholder="请选择">
            </div>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 多选选择器示例 -->
<template>
  <!-- 多选选择器 -->
  <FormItem label="多选项">
    <MultiSelect v-model="values" placeholder="请选择">
      <Option v-for="item in options" :key="item.value" :value="item.value" :label="item.label" />
    </MultiSelect>
  </FormItem>

  <!-- 带标签的多选 -->
  <FormItem label="兴趣爱好">
    <TagSelect v-model="hobbies" placeholder="请选择兴趣爱好">
      <Option v-for="item in hobbyOptions" :key="item.value" :value="item.value" :label="item.label" />
    </TagSelect>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 单选框 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">单选框</h2>
      <p class="text-gray-600 mb-6">
        单选框允许用户从一组选项中选择一个选项，适用于选项较少且需要明确展示所有选项的场景。
      </p>

      <div class="space-y-4 mb-6">
        <div class="form-group">
          <label class="label">基础单选框</label>
          <div class="space-y-2">
            <div class="flex items-center">
              <input type="radio" id="radio-1" name="radio-group" class="radio mr-2">
              <label for="radio-1">选项一</label>
            </div>
            <div class="flex items-center">
              <input type="radio" id="radio-2" name="radio-group" class="radio mr-2" checked>
              <label for="radio-2">选项二</label>
            </div>
            <div class="flex items-center">
              <input type="radio" id="radio-3" name="radio-group" class="radio mr-2" disabled>
              <label for="radio-3" class="text-gray-400">选项三 (禁用)</label>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4 text-gray-700">按钮样式单选</h3>
        <div class="inline-flex rounded-md shadow-sm">
          <button class="px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100">
            选项一
          </button>
          <button class="px-4 py-2 text-sm font-medium bg-blue-600 text-white border border-blue-600">
            选项二
          </button>
          <button class="px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 opacity-50 cursor-not-allowed">
            选项三
          </button>
        </div>
      </div>

      <div class="code-block">
<!-- 单选框示例 -->
<template>
  <!-- 基础单选框 -->
  <FormItem label="性别">
    <RadioGroup v-model="gender">
      <Radio value="male">男</Radio>
      <Radio value="female">女</Radio>
      <Radio value="other" disabled>其他</Radio>
    </RadioGroup>
  </FormItem>

  <!-- 按钮样式单选 -->
  <FormItem label="付款方式">
    <RadioButton v-model="paymentMethod">
      <RadioButtonOption value="alipay">支付宝</RadioButtonOption>
      <RadioButtonOption value="wechat">微信支付</RadioButtonOption>
      <RadioButtonOption value="bank" disabled>银行转账</RadioButtonOption>
    </RadioButton>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 复选框 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">复选框</h2>
      <p class="text-gray-600 mb-6">
        复选框允许用户从一组选项中选择多个选项，也可用于单个选项的切换。
      </p>

      <div class="space-y-4 mb-6">
        <div class="form-group">
          <label class="label">基础复选框</label>
          <div class="space-y-2">
            <div class="flex items-center">
              <input type="checkbox" id="checkbox-1" class="checkbox mr-2">
              <label for="checkbox-1">选项一</label>
            </div>
            <div class="flex items-center">
              <input type="checkbox" id="checkbox-2" class="checkbox mr-2" checked>
              <label for="checkbox-2">选项二</label>
            </div>
            <div class="flex items-center">
              <input type="checkbox" id="checkbox-3" class="checkbox mr-2" disabled>
              <label for="checkbox-3" class="text-gray-400">选项三 (禁用)</label>
            </div>
          </div>
        </div>

        <div class="form-group">
          <div class="flex items-center">
            <input type="checkbox" id="checkbox-all" class="checkbox mr-2">
            <label for="checkbox-all" class="font-medium">全选</label>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 复选框示例 -->
<template>
  <!-- 基础复选框 -->
  <FormItem label="兴趣爱好">
    <CheckboxGroup v-model="hobbies">
      <Checkbox value="reading">阅读</Checkbox>
      <Checkbox value="sports">运动</Checkbox>
      <Checkbox value="music">音乐</Checkbox>
      <Checkbox value="travel" disabled>旅行</Checkbox>
    </CheckboxGroup>
  </FormItem>

  <!-- 全选功能 -->
  <FormItem>
    <Checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAllChange">
      全选
    </Checkbox>
  </FormItem>
</template>
      </div>
    </div>

    <!-- 开关 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">开关</h2>
      <p class="text-gray-600 mb-6">
        开关用于表示两种状态之间的切换，通常用于表示启用/禁用的设置选项。
      </p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group">
          <label class="label">基础开关</label>
          <div class="flex items-center">
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>
        <div class="form-group">
          <label class="label">带文字描述</label>
          <div class="flex items-center">
            <label class="switch mr-2">
              <input type="checkbox" checked>
              <span class="slider"></span>
            </label>
            <span class="text-green-600">开启</span>
          </div>
        </div>
        <div class="form-group">
          <label class="label">禁用状态</label>
          <div class="flex items-center">
            <label class="switch opacity-50 cursor-not-allowed">
              <input type="checkbox" disabled>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div class="code-block">
<!-- 开关示例 -->
<template>
  <!-- 基础开关 -->
  <FormItem label="状态">
    <Switch v-model="status" />
  </FormItem>

  <!-- 带文字描述 -->
  <FormItem label="通知">
    <div class="flex items-center">
      <Switch v-model="notification" />
      <span class="ml-2">{{ notification ? '已开启' : '已关闭' }}</span>
    </div>
  </FormItem>

  <!-- 禁用状态 -->
  <FormItem label="高级功能">
    <Switch v-model="advancedFeature" disabled />
  </FormItem>
</template>
      </div>
    </div>

    <!-- 应用场景 -->
    <div class="component-container bg-white shadow-sm">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">应用场景示例</h2>
      <p class="text-gray-600 mb-6">
        以下是选择器控件在表单场景中的应用示例。
      </p>

      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4 text-gray-700">设置面板</h3>
        <div class="bg-white p-6 rounded-lg shadow">
          <h4 class="text-xl font-semibold mb-6">个人设置</h4>
          <div class="space-y-6">
            <div class="flex items-center justify-between">
              <div>
                <h5 class="font-medium">接收通知</h5>
                <p class="text-sm text-gray-500">开启后将接收系统通知</p>
              </div>
              <label class="switch">
                <input type="checkbox" checked>
                <span class="slider"></span>
              </label>
            </div>
            <hr />
            <div class="flex items-center justify-between">
              <div>
                <h5 class="font-medium">夜间模式</h5>
                <p class="text-sm text-gray-500">自动切换为暗色主题</p>
              </div>
              <label class="switch">
                <input type="checkbox">
                <span class="slider"></span>
              </label>
            </div>
            <hr />
            <div>
              <h5 class="font-medium mb-3">语言设置</h5>
              <div class="select-wrapper max-w-xs">
                <select class="form-control">
                  <option value="zh-CN">简体中文</option>
                  <option value="en-US">English (US)</option>
                  <option value="ja-JP">日本語</option>
                </select>
              </div>
            </div>
            <hr />
            <div>
              <h5 class="font-medium mb-3">接收邮件类型</h5>
              <div class="space-y-2">
                <div class="flex items-center">
                  <input type="checkbox" id="email-updates" class="checkbox mr-2" checked>
                  <label for="email-updates">产品更新</label>
                </div>
                <div class="flex items-center">
                  <input type="checkbox" id="email-news" class="checkbox mr-2" checked>
                  <label for="email-news">行业资讯</label>
                </div>
                <div class="flex items-center">
                  <input type="checkbox" id="email-marketing" class="checkbox mr-2">
                  <label for="email-marketing">营销邮件</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-12 mb-8">
      <a href="../index.html" class="text-blue-600 hover:text-blue-800 font-medium">
        <i class="ri-arrow-left-line mr-1"></i> 返回组件总览
      </a>
    </div>
  </div>
</body>
</html>