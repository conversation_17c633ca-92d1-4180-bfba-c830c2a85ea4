<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发指南 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        pre {
            background-color: #f7fafc;
            border-radius: 0.375rem;
            padding: 1rem;
            overflow-x: auto;
        }
        code {
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-size: 0.875rem;
        }
        .code-sample {
            background-color: #f7fafc;
            border-radius: 0.375rem;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                使用指引
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">开发指南</span>
        </div>

        <div class="bg-white rounded-lg shadow-md p-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">开发指南</h1>
            <p class="text-gray-600 mb-8">
                本指南提供了使用 DataScope UI 组件库进行开发的最佳实践和建议。遵循这些指导原则将帮助您创建一致、高效且易于维护的应用程序。
            </p>

            <div class="space-y-12">
                <!-- 组件使用 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                            <i class="ri-layout-masonry-line text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">组件使用</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        正确使用组件是构建一致用户界面的基础。每个组件都设计用于特定的用例和交互模式。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">最佳实践：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>优先使用标准组件而不是创建自定义元素</li>
                            <li>遵循组件文档中的用法指南</li>
                            <li>保持一致的组件用法和样式</li>
                            <li>避免过度定制组件，可能导致不一致的用户体验</li>
                        </ul>
                    </div>

                    <div class="mt-6">
                        <h3 class="font-medium text-gray-900 mb-3">基本用法示例：</h3>
                        <div class="code-sample">
                            <pre><code>&lt;!-- 使用按钮组件 --&gt;
&lt;button class="ds-btn ds-btn-primary"&gt;
    &lt;i class="ri-save-line mr-1"&gt;&lt;/i&gt; 保存
&lt;/button&gt;

&lt;!-- 使用表格组件 --&gt;
&lt;table class="ds-table"&gt;
    &lt;thead&gt;
        &lt;tr&gt;
            &lt;th&gt;ID&lt;/th&gt;
            &lt;th&gt;名称&lt;/th&gt;
            &lt;th&gt;操作&lt;/th&gt;
        &lt;/tr&gt;
    &lt;/thead&gt;
    &lt;tbody&gt;
        &lt;tr&gt;
            &lt;td&gt;1&lt;/td&gt;
            &lt;td&gt;示例数据&lt;/td&gt;
            &lt;td&gt;
                &lt;button class="ds-btn ds-btn-sm ds-btn-text"&gt;编辑&lt;/button&gt;
            &lt;/td&gt;
        &lt;/tr&gt;
    &lt;/tbody&gt;
&lt;/table&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- 组件定制 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white">
                            <i class="ri-brush-fill text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">组件定制</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        虽然我们鼓励使用标准组件，但有时您可能需要定制组件以满足特定需求。在定制时，请保持组件的核心功能和可访问性。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">最佳实践：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>使用提供的修饰符和变体类进行定制</li>
                            <li>在定制组件外观时保持其核心功能不变</li>
                            <li>记录任何自定义变体或扩展</li>
                            <li>确保定制后的组件在所有设备上仍然可用</li>
                        </ul>
                    </div>

                    <div class="mt-6">
                        <h3 class="font-medium text-gray-900 mb-3">定制示例：</h3>
                        <div class="code-sample">
                            <pre><code>&lt;!-- 自定义按钮样式 --&gt;
&lt;button class="ds-btn ds-btn-primary ds-btn-rounded ds-btn-lg"&gt;
    自定义大号圆角按钮
&lt;/button&gt;

&lt;!-- 使用辅助类定制卡片 --&gt;
&lt;div class="ds-card ds-shadow-lg ds-border-top ds-border-primary"&gt;
    &lt;div class="ds-card-header"&gt;自定义卡片标题&lt;/div&gt;
    &lt;div class="ds-card-body"&gt;
        卡片内容区域
    &lt;/div&gt;
    &lt;div class="ds-card-footer ds-bg-gray-50"&gt;
        自定义底部样式
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- 组件组合 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white">
                            <i class="ri-puzzle-fill text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">组件组合</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        将基础组件组合在一起可以创建复杂的界面元素和交互。组件组合是构建丰富用户界面的关键。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">最佳实践：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>创建可重用的组件组合</li>
                            <li>保持组件之间的逻辑关系清晰</li>
                            <li>为复杂组合创建包装组件</li>
                            <li>使用一致的间距和对齐方式</li>
                        </ul>
                    </div>

                    <div class="mt-6">
                        <h3 class="font-medium text-gray-900 mb-3">组合示例：</h3>
                        <div class="code-sample">
                            <pre><code>&lt;!-- 搜索栏组合 --&gt;
&lt;div class="ds-input-group"&gt;
    &lt;input type="text" class="ds-input" placeholder="搜索..."&gt;
    &lt;button class="ds-btn ds-btn-primary"&gt;
        &lt;i class="ri-search-line"&gt;&lt;/i&gt;
    &lt;/button&gt;
&lt;/div&gt;

&lt;!-- 数据过滤器组合 --&gt;
&lt;div class="ds-filter-bar"&gt;
    &lt;div class="ds-filter-item"&gt;
        &lt;label class="ds-label"&gt;状态：&lt;/label&gt;
        &lt;select class="ds-select"&gt;
            &lt;option value="all"&gt;全部&lt;/option&gt;
            &lt;option value="active"&gt;活跃&lt;/option&gt;
            &lt;option value="inactive"&gt;停用&lt;/option&gt;
        &lt;/select&gt;
    &lt;/div&gt;
    &lt;div class="ds-filter-item"&gt;
        &lt;label class="ds-label"&gt;日期：&lt;/label&gt;
        &lt;input type="date" class="ds-input"&gt;
    &lt;/div&gt;
    &lt;button class="ds-btn ds-btn-outline"&gt;
        &lt;i class="ri-filter-line mr-1"&gt;&lt;/i&gt; 筛选
    &lt;/button&gt;
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- 响应式开发 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-yellow-500 text-white">
                            <i class="ri-responsive-line text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">响应式开发</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        开发响应式界面确保您的应用在各种设备上都能提供良好的用户体验。DataScope UI 组件库内置了响应式设计支持。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">最佳实践：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>使用提供的响应式网格系统</li>
                            <li>优先使用相对单位而非固定像素</li>
                            <li>测试界面在各种屏幕尺寸上的表现</li>
                            <li>为移动端考虑触摸交互和更大的点击区域</li>
                        </ul>
                    </div>

                    <div class="mt-6">
                        <h3 class="font-medium text-gray-900 mb-3">响应式示例：</h3>
                        <div class="code-sample">
                            <pre><code>&lt;!-- 响应式网格布局 --&gt;
&lt;div class="ds-grid ds-grid-cols-1 ds-grid-cols-md-2 ds-grid-cols-lg-4 ds-gap-4"&gt;
    &lt;div class="ds-card"&gt;卡片 1&lt;/div&gt;
    &lt;div class="ds-card"&gt;卡片 2&lt;/div&gt;
    &lt;div class="ds-card"&gt;卡片 3&lt;/div&gt;
    &lt;div class="ds-card"&gt;卡片 4&lt;/div&gt;
&lt;/div&gt;

&lt;!-- 响应式隐藏和显示 --&gt;
&lt;div class="ds-hidden ds-block-md"&gt;
    在中等及以上屏幕显示
&lt;/div&gt;
&lt;div class="ds-block ds-hidden-md"&gt;
    只在小屏幕显示
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-12 border-t border-gray-200 pt-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">开发资源</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <a href="./vue3.html" class="block group">
                        <div class="bg-gray-50 p-5 rounded-lg group-hover:bg-gray-100 transition duration-150">
                            <div class="flex items-center mb-3">
                                <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md bg-green-500 text-white">
                                    <i class="ri-vuejs-line text-xl"></i>
                                </div>
                                <h3 class="ml-3 font-medium text-gray-900">Vue 3 集成指南</h3>
                            </div>
                            <p class="text-gray-600">了解如何在 Vue 3 项目中集成和使用 DataScope UI 组件。</p>
                        </div>
                    </a>
                    
                    <a href="./changelog.html" class="block group">
                        <div class="bg-gray-50 p-5 rounded-lg group-hover:bg-gray-100 transition duration-150">
                            <div class="flex items-center mb-3">
                                <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md bg-blue-500 text-white">
                                    <i class="ri-history-line text-xl"></i>
                                </div>
                                <h3 class="ml-3 font-medium text-gray-900">更新日志</h3>
                            </div>
                            <p class="text-gray-600">查看组件库的最新变更和历史版本信息。</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <div class="text-center mt-12 mb-6">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回使用指引
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>