<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计原则 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                使用指引
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">设计原则</span>
        </div>

        <div class="bg-white rounded-lg shadow-md p-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">设计原则</h1>
            <p class="text-gray-600 mb-8">
                DataScope UI 的设计原则旨在帮助团队创建一致、直观且易用的用户界面。这些原则是我们设计决策的基础，确保用户体验始终保持高质量和一致性。
            </p>

            <div class="space-y-12">
                <!-- 一致性 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                            <i class="ri-layout-grid-line text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">一致性</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        一致的设计和体验让用户在整个应用中感到熟悉和舒适。通过重复使用组件和保持交互模式的一致性，我们可以降低用户的认知负担。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">关键点：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>在整个应用中使用相同的组件做相同的事情</li>
                            <li>保持视觉层次和布局的一致性</li>
                            <li>使用一致的交互模式和反馈机制</li>
                            <li>建立清晰的设计系统和模式库</li>
                        </ul>
                    </div>
                </div>

                <!-- 简洁性 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white">
                            <i class="ri-focus-3-line text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">简洁性</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        简洁的设计关注于必要的元素和功能，移除不必要的复杂性。通过精心设计每个界面元素，我们可以创建更加清晰、高效的用户体验。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">关键点：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>减少界面中的视觉噪音</li>
                            <li>优先展示关键信息和功能</li>
                            <li>将复杂任务分解为简单步骤</li>
                            <li>使用清晰、简洁的文案</li>
                        </ul>
                    </div>
                </div>

                <!-- 可用性 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white">
                            <i class="ri-user-smile-line text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">可用性</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        可用性意味着界面易于学习、高效且令人满意。我们的设计应该让用户能够轻松完成任务，无论他们的技能水平或使用环境如何。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">关键点：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>设计直观的交互模式</li>
                            <li>提供清晰的反馈和提示</li>
                            <li>考虑不同用户的需求和习惯</li>
                            <li>支持键盘操作和辅助技术</li>
                            <li>允许用户撤销操作和纠正错误</li>
                        </ul>
                    </div>
                </div>

                <!-- 响应式设计 -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-yellow-500 text-white">
                            <i class="ri-responsive-line text-xl"></i>
                        </div>
                        <h2 class="ml-4 text-2xl font-bold text-gray-900">响应式设计</h2>
                    </div>
                    <p class="text-gray-600 mb-4">
                        我们的界面需要在各种设备和屏幕尺寸上提供良好的体验，从手机到桌面显示器。响应式设计确保用户无论使用何种设备，都能得到最佳体验。
                    </p>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="font-medium text-gray-900 mb-3">关键点：</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>使用灵活的布局和网格系统</li>
                            <li>考虑不同的交互方式（触摸、鼠标、键盘）</li>
                            <li>优化加载性能和资源使用</li>
                            <li>在不同设备上测试用户体验</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="mt-12 border-t border-gray-200 pt-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">应用这些原则</h2>
                <p class="text-gray-600 mb-4">
                    将这些设计原则应用到你的项目中：
                </p>
                <ol class="list-decimal list-inside text-gray-600 space-y-2 mb-6">
                    <li>在设计开始阶段使用这些原则作为决策框架</li>
                    <li>在评审过程中使用这些原则作为评估标准</li>
                    <li>将原则融入到你的设计文档和讨论中</li>
                    <li>定期回顾这些原则，确保你的设计保持一致</li>
                </ol>
                <p class="text-gray-600">
                    记住，这些原则不是僵硬的规则，而是指导方向。根据具体情况和用户需求，可能需要在不同原则之间找到平衡。
                </p>
            </div>
        </div>

        <div class="text-center mt-12 mb-6">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回使用指引
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>