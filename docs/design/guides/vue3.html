<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 3 使用示例 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Courier New', Courier, monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="./index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="./guide.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                使用指南
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">Vue 3 使用示例</span>
        </div>

        <div class="mb-12">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">Vue 3 使用示例</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                基于 Vue 3 和 Composition API 使用 DataScope UI 组件库的示例和最佳实践。本文档展示了如何在 Vue 3 项目中集成和使用组件库。
            </p>
        </div>

        <!-- 基本设置 -->
        <section class="mb-12 bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-bold mb-6">基本设置</h3>
            
            <div class="mb-8">
                <h4 class="text-lg font-semibold mb-4">安装依赖</h4>
                <div class="code-block">
<pre><code class="language-bash"># 使用 npm
npm install datascope-ui

# 使用 yarn
yarn add datascope-ui</code></pre>
                </div>
            </div>
            
            <div class="mb-8">
                <h4 class="text-lg font-semibold mb-4">全局注册</h4>
                <p class="text-gray-600 mb-4">在 main.js 中全局注册组件库：</p>
                <div class="code-block">
<pre><code class="language-javascript">import { createApp } from 'vue'
import App from './App.vue'
import DataScopeUI from 'datascope-ui'
import 'datascope-ui/dist/style.css'

const app = createApp(App)
app.use(DataScopeUI)
app.mount('#app')</code></pre>
                </div>
            </div>
            
            <div>
                <h4 class="text-lg font-semibold mb-4">按需引入组件</h4>
                <p class="text-gray-600 mb-4">如果只需要使用部分组件，可以按需引入：</p>
                <div class="code-block">
<pre><code class="language-javascript">import { createApp } from 'vue'
import App from './App.vue'
import { DsButton, DsInput, DsTable } from 'datascope-ui'
import 'datascope-ui/dist/style.css'

const app = createApp(App)
app.component('DsButton', DsButton)
app.component('DsInput', DsInput)
app.component('DsTable', DsTable)
app.mount('#app')</code></pre>
                </div>
            </div>
        </section>

        <!-- 基础组件使用 -->
        <section class="mb-12 bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-bold mb-6">基础组件使用</h3>
            
            <div class="border-b border-gray-200 mb-6">
                <div class="flex space-x-6">
                    <button class="pb-2 px-1 border-b-2 border-indigo-600 text-indigo-600 font-medium tab-button active" data-tab="setup">使用 setup 语法</button>
                    <button class="pb-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-900 tab-button" data-tab="options">使用 Options API</button>
                </div>
            </div>
            
            <div id="setup" class="tab-content active">
                <div class="code-block">
<pre><code class="language-html">&lt;template&gt;
  &lt;div class="p-6"&gt;
    &lt;h1 class="text-2xl font-bold mb-4"&gt;{{ pageTitle }}&lt;/h1&gt;
    
    &lt;div class="mb-6"&gt;
      &lt;h2 class="text-lg font-medium mb-2"&gt;按钮示例&lt;/h2&gt;
      &lt;div class="flex space-x-2"&gt;
        &lt;ds-button type="primary" @click="handlePrimaryClick"&gt;{{ primaryButtonText }}&lt;/ds-button&gt;
        &lt;ds-button type="secondary" @click="resetCounter"&gt;重置&lt;/ds-button&gt;
        &lt;ds-button type="danger" :disabled="isCounterZero"&gt;危险操作&lt;/ds-button&gt;
      &lt;/div&gt;
      &lt;p class="mt-2 text-sm text-gray-600"&gt;点击次数: {{ counter }}&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class="mb-6"&gt;
      &lt;h2 class="text-lg font-medium mb-2"&gt;表单示例&lt;/h2&gt;
      &lt;ds-form @submit="handleSubmit"&gt;
        &lt;ds-form-item label="用户名" required&gt;
          &lt;ds-input 
            v-model="form.username" 
            placeholder="请输入用户名"
            prefix-icon="user"
            :error="formErrors.username"
          /&gt;
        &lt;/ds-form-item&gt;
        
        &lt;ds-form-item label="选择角色"&gt;
          &lt;ds-select 
            v-model="form.role" 
            :options="roleOptions"
            placeholder="请选择角色"
          /&gt;
        &lt;/ds-form-item&gt;
        
        &lt;ds-form-item label="状态"&gt;
          &lt;ds-switch v-model="form.active" /&gt;
          &lt;span class="ml-2 text-sm"&gt;{{ form.active ? '激活' : '禁用' }}&lt;/span&gt;
        &lt;/ds-form-item&gt;
        
        &lt;ds-form-item&gt;
          &lt;ds-button type="primary" native-type="submit"&gt;提交表单&lt;/ds-button&gt;
          &lt;ds-button type="secondary" class="ml-2" @click="resetForm"&gt;重置表单&lt;/ds-button&gt;
        &lt;/ds-form-item&gt;
      &lt;/ds-form&gt;
    &lt;/div&gt;
    
    &lt;div class="mb-6"&gt;
      &lt;h2 class="text-lg font-medium mb-2"&gt;数据表格示例&lt;/h2&gt;
      &lt;ds-table 
        :data="tableData" 
        :columns="tableColumns" 
        :loading="tableLoading"
        @row-click="handleRowClick"
      &gt;
        &lt;template #status="{ row }"&gt;
          &lt;ds-tag 
            :type="row.status === 'active' ? 'success' : 'warning'"
          &gt;
            {{ row.status === 'active' ? '激活' : '禁用' }}
          &lt;/ds-tag&gt;
        &lt;/template&gt;
        
        &lt;template #actions="{ row }"&gt;
          &lt;ds-button size="small" @click.stop="editItem(row)"&gt;编辑&lt;/ds-button&gt;
          &lt;ds-button type="danger" size="small" class="ml-2" @click.stop="removeItem(row)"&gt;删除&lt;/ds-button&gt;
        &lt;/template&gt;
      &lt;/ds-table&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, reactive, computed, onMounted } from 'vue'

// 页面标题
const pageTitle = ref('DataScope UI 示例页面')

// 按钮示例相关
const counter = ref(0)
const primaryButtonText = computed(() => `点击次数 ${counter.value}`)
const isCounterZero = computed(() => counter.value === 0)

const handlePrimaryClick = () => {
  counter.value++
}

const resetCounter = () => {
  counter.value = 0
}

// 表单示例相关
const form = reactive({
  username: '',
  role: '',
  active: false
})

const formErrors = reactive({
  username: ''
})

const roleOptions = [
  { value: 'admin', label: '管理员' },
  { value: 'editor', label: '编辑者' },
  { value: 'viewer', label: '访问者' }
]

const handleSubmit = () => {
  // 表单验证
  if (!form.username) {
    formErrors.username = '用户名不能为空'
    return
  }
  
  formErrors.username = ''
  console.log('提交表单数据', form)
  
  // 显示成功消息
  // dsMessage.success('表单提交成功')
}

const resetForm = () => {
  form.username = ''
  form.role = ''
  form.active = false
  formErrors.username = ''
}

// 表格示例相关
const tableData = ref([])
const tableLoading = ref(false)

const tableColumns = [
  { key: 'id', title: 'ID' },
  { key: 'name', title: '名称' },
  { key: 'status', title: '状态', slot: 'status' },
  { key: 'actions', title: '操作', slot: 'actions', width: '180px' }
]

onMounted(() => {
  loadTableData()
})

const loadTableData = () => {
  tableLoading.value = true
  // 模拟API请求
  setTimeout(() => {
    tableData.value = [
      { id: 1, name: '数据项 1', status: 'active' },
      { id: 2, name: '数据项 2', status: 'inactive' },
      { id: 3, name: '数据项 3', status: 'active' }
    ]
    tableLoading.value = false
  }, 1000)
}

const handleRowClick = (row) => {
  console.log('行点击', row)
}

const editItem = (row) => {
  console.log('编辑', row)
}

const removeItem = (row) => {
  console.log('删除', row)
  // 确认删除
  // dsDialog.confirm({
  //   title: '确认删除',
  //   content: `确定要删除 "${row.name}" 吗？`,
  //   onConfirm: () => {
  //     tableData.value = tableData.value.filter(item => item.id !== row.id)
  //     dsMessage.success('删除成功')
  //   }
  // })
}
&lt;/script&gt;</code></pre>
                </div>
            </div>
            
            <div id="options" class="tab-content">
                <div class="code-block">
<pre><code class="language-html">&lt;template&gt;
  &lt;div class="p-6"&gt;
    &lt;h1 class="text-2xl font-bold mb-4"&gt;{{ pageTitle }}&lt;/h1&gt;
    
    &lt;div class="mb-6"&gt;
      &lt;h2 class="text-lg font-medium mb-2"&gt;按钮示例&lt;/h2&gt;
      &lt;div class="flex space-x-2"&gt;
        &lt;ds-button type="primary" @click="handlePrimaryClick"&gt;{{ primaryButtonText }}&lt;/ds-button&gt;
        &lt;ds-button type="secondary" @click="resetCounter"&gt;重置&lt;/ds-button&gt;
        &lt;ds-button type="danger" :disabled="isCounterZero"&gt;危险操作&lt;/ds-button&gt;
      &lt;/div&gt;
      &lt;p class="mt-2 text-sm text-gray-600"&gt;点击次数: {{ counter }}&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;!-- 其他内容与 setup 版本相同 --&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ExamplePage',
  
  data() {
    return {
      pageTitle: 'DataScope UI 示例页面',
      counter: 0,
      form: {
        username: '',
        role: '',
        active: false
      },
      formErrors: {
        username: ''
      },
      roleOptions: [
        { value: 'admin', label: '管理员' },
        { value: 'editor', label: '编辑者' },
        { value: 'viewer', label: '访问者' }
      ],
      tableData: [],
      tableLoading: false,
      tableColumns: [
        { key: 'id', title: 'ID' },
        { key: 'name', title: '名称' },
        { key: 'status', title: '状态', slot: 'status' },
        { key: 'actions', title: '操作', slot: 'actions', width: '180px' }
      ]
    }
  },
  
  computed: {
    primaryButtonText() {
      return `点击次数 ${this.counter}`
    },
    isCounterZero() {
      return this.counter === 0
    }
  },
  
  mounted() {
    this.loadTableData()
  },
  
  methods: {
    handlePrimaryClick() {
      this.counter++
    },
    
    resetCounter() {
      this.counter = 0
    },
    
    handleSubmit() {
      // 表单验证
      if (!this.form.username) {
        this.formErrors.username = '用户名不能为空'
        return
      }
      
      this.formErrors.username = ''
      console.log('提交表单数据', this.form)
    },
    
    resetForm() {
      this.form.username = ''
      this.form.role = ''
      this.form.active = false
      this.formErrors.username = ''
    },
    
    loadTableData() {
      this.tableLoading = true
      // 模拟API请求
      setTimeout(() => {
        this.tableData = [
          { id: 1, name: '数据项 1', status: 'active' },
          { id: 2, name: '数据项 2', status: 'inactive' },
          { id: 3, name: '数据项 3', status: 'active' }
        ]
        this.tableLoading = false
      }, 1000)
    },
    
    handleRowClick(row) {
      console.log('行点击', row)
    },
    
    editItem(row) {
      console.log('编辑', row)
    },
    
    removeItem(row) {
      console.log('删除', row)
    }
  }
})
&lt;/script&gt;</code></pre>
                </div>
            </div>
        </section>

        <!-- 高级用法 -->
        <section class="mb-12 bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-bold mb-6">高级用法</h3>
            
            <div class="mb-8">
                <h4 class="text-lg font-semibold mb-4">组合式函数封装业务逻辑</h4>
                <p class="text-gray-600 mb-4">使用 Vue 3 的组合式函数（Composables）封装业务逻辑，提高代码复用性：</p>
                <div class="code-block">
<pre><code class="language-javascript">// useUserForm.js
import { ref, reactive, computed } from 'vue'

export function useUserForm() {
  const form = reactive({
    username: '',
    email: '',
    role: '',
    active: false
  })

  const errors = reactive({
    username: '',
    email: ''
  })

  const isFormValid = computed(() => {
    return !errors.username && !errors.email && form.username && form.email
  })

  const roleOptions = [
    { value: 'admin', label: '管理员' },
    { value: 'editor', label: '编辑者' },
    { value: 'viewer', label: '访问者' }
  ]

  const validateUsername = () => {
    if (!form.username) {
      errors.username = '用户名不能为空'
      return false
    }
    errors.username = ''
    return true
  }

  const validateEmail = () => {
    if (!form.email) {
      errors.email = '邮箱不能为空'
      return false
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(form.email)) {
      errors.email = '请输入有效的邮箱地址'
      return false
    }
    errors.email = ''
    return true
  }

  const validateForm = () => {
    const usernameValid = validateUsername()
    const emailValid = validateEmail()
    return usernameValid && emailValid
  }

  const resetForm = () => {
    form.username = ''
    form.email = ''
    form.role = ''
    form.active = false
    errors.username = ''
    errors.email = ''
  }

  const saveUser = async () => {
    if (!validateForm()) {
      return false
    }
    
    try {
      // 实际项目中这里会调用API
      console.log('保存用户数据', form)
      return true
    } catch (error) {
      console.error('保存用户失败', error)
      return false
    }
  }

  return {
    form,
    errors,
    isFormValid,
    roleOptions,
    validateUsername,
    validateEmail,
    validateForm,
    resetForm,
    saveUser
  }
}

// 在组件中使用
/*
import { useUserForm } from './composables/useUserForm'

const {
  form,
  errors,
  isFormValid,
  roleOptions,
  validateUsername,
  validateEmail,
  resetForm,
  saveUser
} = useUserForm()

const handleSubmit = async () => {
  const success = await saveUser()
  if (success) {
    dsMessage.success('用户保存成功')
    resetForm()
  }
}
*/</code></pre>
                </div>
            </div>
            
            <div>
                <h4 class="text-lg font-semibold mb-4">自定义主题</h4>
                <p class="text-gray-600 mb-4">在 Vue 3 中自定义 DataScope UI 组件库的主题：</p>
                <div class="code-block">
<pre><code class="language-javascript">// theme.js
export const customTheme = {
  colors: {
    primary: '#3B82F6', // 蓝色
    secondary: '#64748B', // 灰色
    success: '#10B981', // 绿色
    warning: '#F59E0B', // 黄色
    danger: '#EF4444', // 红色
    info: '#3B82F6', // 蓝色
    light: '#F1F5F9', // 浅灰色
    dark: '#1E293B' // 深灰色
  },
  radius: {
    small: '0.25rem',
    default: '0.375rem',
    large: '0.5rem',
    round: '9999px'
  },
  shadows: {
    small: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    default: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
    medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
    large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)'
  }
}

// main.js
import { createApp } from 'vue'
import App from './App.vue'
import DataScopeUI from 'datascope-ui'
import { customTheme } from './theme'
import 'datascope-ui/dist/style.css'

const app = createApp(App)
app.use(DataScopeUI, {
  theme: customTheme
})
app.mount('#app')</code></pre>
                </div>
            </div>
        </section>

        <div class="text-center mb-12">
            <a href="./guide.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回使用指南
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="./changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 标签切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('border-indigo-600');
                        btn.classList.remove('text-indigo-600');
                        btn.classList.add('border-transparent');
                        btn.classList.add('text-gray-500');
                    });
                    
                    // 设置当前为活动状态
                    this.classList.add('active');
                    this.classList.add('border-indigo-600');
                    this.classList.add('text-indigo-600');
                    this.classList.remove('border-transparent');
                    this.classList.remove('text-gray-500');
                    
                    // 显示对应内容
                    const tabId = this.getAttribute('data-tab');
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>