<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 按钮组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .btn-demo {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../feedback/message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="../feedback/notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="../feedback/dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="../feedback/alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="../feedback/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        <i class="ri-home-line"></i>
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        组件
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                        基础组件
                    </a>
                    <span class="text-gray-400 mx-2">/</span>
                    <span class="text-gray-900 font-medium">按钮</span>
                </div>

                <div class="mb-8">
                    <h2 class="text-3xl font-bold mb-4 text-gray-900">DataScope 按钮组件</h2>
                    <p class="text-lg text-gray-600 max-w-4xl">
                        按钮用于触发操作或事件，是用户与界面交互的重要元素。
                    </p>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">按钮变体</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">主要按钮 (Primary)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        主要按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">次要按钮 (Secondary)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        次要按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">成功按钮 (Success)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <span class="mr-2">
                            <i class="ri-checkbox-circle-line"></i>
                        </span>
                        成功按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">危险按钮 (Danger)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <span class="mr-2">
                            <i class="ri-delete-bin-line"></i>
                        </span>
                        危险按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">警告按钮 (Warning)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                        <span class="mr-2">
                            <i class="ri-error-warning-line"></i>
                        </span>
                        警告按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">信息按钮 (Info)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-blue-500 text-white hover:bg-blue-600 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="mr-2">
                            <i class="ri-information-line"></i>
                        </span>
                        信息按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">文本按钮 (Text)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-transparent text-indigo-600 hover:bg-gray-100 hover:text-indigo-800">
                        文本按钮
                    </button>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">按钮尺寸</h2>
            <div class="flex flex-wrap items-center">
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">超小 (XS)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-2 py-1 text-xs bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        超小按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">小 (SM)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-3 py-1.5 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        小按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">中 (MD) - 默认</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        中按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">大 (LG)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-5 py-2.5 text-base bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        大按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">超大 (XL)</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-6 py-3 text-lg bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        超大按钮
                    </button>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">按钮状态</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">正常状态</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        正常按钮
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">加载状态</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 opacity-50 cursor-not-allowed">
                        <svg class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        加载中...
                    </button>
                </div>
                
                <div class="btn-demo">
                    <p class="text-sm text-gray-500 mb-2">禁用状态</p>
                    <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 opacity-50 cursor-not-allowed" disabled>
                        禁用按钮
                    </button>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">图标按钮</h2>
            <div class="preview-section">
                <h3 class="text-lg font-medium mb-4">前置图标按钮</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <span class="mr-2">
                                <i class="ri-add-line"></i>
                            </span>
                            添加
                        </button>
                    </div>
                    
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            <span class="mr-2">
                                <i class="ri-refresh-line"></i>
                            </span>
                            刷新
                        </button>
                    </div>
                    
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <span class="mr-2">
                                <i class="ri-download-line"></i>
                            </span>
                            下载
                        </button>
                    </div>
                    
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <span class="mr-2">
                                <i class="ri-delete-bin-line"></i>
                            </span>
                            删除
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="preview-section">
                <h3 class="text-lg font-medium mb-4">后置图标按钮</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            前往详情
                            <span class="ml-2">
                                <i class="ri-arrow-right-line"></i>
                            </span>
                        </button>
                    </div>
                    
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-blue-500 text-white hover:bg-blue-600 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查看文档
                            <span class="ml-2">
                                <i class="ri-external-link-line"></i>
                            </span>
                        </button>
                    </div>
                    
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            更多选项
                            <span class="ml-2">
                                <i class="ri-arrow-down-s-line"></i>
                            </span>
                        </button>
                    </div>
                    
                    <div class="btn-demo">
                        <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            返回
                            <span class="ml-2">
                                <i class="ri-arrow-left-line"></i>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>