<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 带图标输入框组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .input-demo {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="components.html" class="mr-4 text-indigo-600 hover:text-indigo-800">
                <i class="ri-arrow-left-line"></i>
                返回组件库
            </a>
            <h1 class="text-3xl font-bold">DataScope 输入框组件</h1>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">前置图标输入框</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">搜索输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-search-line text-gray-400"></i>
                        </div>
                        <input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="搜索...">
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">用户名输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-user-line text-gray-400"></i>
                        </div>
                        <input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入用户名">
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">邮箱输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-mail-line text-gray-400"></i>
                        </div>
                        <input type="email" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入邮箱地址">
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">密码输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-lock-line text-gray-400"></i>
                        </div>
                        <input type="password" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入密码">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">后置图标输入框</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">网址输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <input type="text" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入网址">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="ri-link-line text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">日期选择框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <input type="text" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="YYYY-MM-DD">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="ri-calendar-line text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">成功验证的输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <input type="text" class="block w-full pl-3 pr-10 py-2 border border-green-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm" style="border: 2px solid #86efac;" value="有效输入">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="ri-checkbox-circle-line text-green-500"></i>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">错误验证的输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <input type="text" class="block w-full pl-3 pr-10 py-2 border border-red-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm" style="border: 2px solid #f87171;" value="无效输入">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="ri-error-warning-line text-red-500"></i>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-red-600">输入内容不符合要求</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">带交互图标的输入框</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">密码显示/隐藏</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input type="password" class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入密码">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">可清除的输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" value="搜索关键词">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                <i class="ri-close-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">带复制按钮的输入框</p>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <input type="text" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" value="https://example.com/shared-link" readonly>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                <i class="ri-clipboard-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">带搜索按钮的输入框</p>
                    <div class="mt-1 flex rounded-md shadow-sm">
                        <div class="relative flex-grow focus-within:z-10">
                            <input type="text" class="block w-full rounded-none rounded-l-md pl-3 py-2 border border-gray-300 shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db; border-right: none;" placeholder="搜索关键词">
                        </div>
                        <button class="-ml-px relative inline-flex items-center px-4 py-2 border border-indigo-600 text-sm font-medium rounded-r-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">复合输入框</h2>
            <div class="grid grid-cols-1 gap-4">
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">搜索框带筛选</p>
                    <div class="flex rounded-md shadow-sm">
                        <div class="relative flex-grow focus-within:z-10">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" class="block w-full rounded-none rounded-l-md pl-10 pr-3 py-2 border border-gray-300 shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db; border-right: none;" placeholder="搜索...">
                        </div>
                        <div class="relative inline-flex">
                            <select class="rounded-none rounded-r-md border-gray-300 bg-gray-50 pl-3 pr-8 py-2 text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" style="border: 2px solid #d1d5db; border-left: none;">
                                <option>全部</option>
                                <option>名称</option>
                                <option>描述</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                                <i class="ri-arrow-down-s-line text-xs"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">前缀后缀输入框</p>
                    <div class="flex rounded-md shadow-sm">
                        <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm" style="border: 2px solid #d1d5db; border-right: none;">
                            https://
                        </span>
                        <input type="text" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none border border-gray-300 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db; border-left: none; border-right: none;" placeholder="example.com">
                        <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm" style="border: 2px solid #d1d5db; border-left: none;">
                            .com
                        </span>
                    </div>
                </div>
                
                <div class="input-demo">
                    <p class="text-sm text-gray-500 mb-2">价格输入框</p>
                    <div class="flex rounded-md shadow-sm">
                        <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm" style="border: 2px solid #d1d5db; border-right: none;">
                            ¥
                        </span>
                        <input type="number" min="0" step="0.01" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db; border-left: none;" placeholder="0.00">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">应用场景</h2>
            <div class="preview-section">
                <h3 class="text-lg font-medium mb-4">搜索表单</h3>
                <div class="bg-white p-4 border border-gray-200 rounded-lg">
                    <div class="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
                        <div class="flex-1">
                            <label for="search-input" class="block text-sm font-medium text-gray-700 mb-1">关键词</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="search-input" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="搜索...">
                            </div>
                        </div>
                        
                        <div class="flex-1">
                            <label for="date-input" class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <input type="text" id="date-input" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="YYYY-MM-DD">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-end">
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span class="mr-2">
                                    <i class="fas fa-search"></i>
                                </span>
                                搜索
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="preview-section">
                <h3 class="text-lg font-medium mb-4">登录表单</h3>
                <div class="bg-white p-4 border border-gray-200 rounded-lg">
                    <div class="max-w-sm mx-auto">
                        <div class="mb-4">
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input type="text" id="username" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入用户名">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input type="password" id="password" class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" style="border: 2px solid #d1d5db;" placeholder="请输入密码">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button class="inline-flex items-center justify-center rounded-md focus:outline-none transition-colors px-4 py-2 text-sm bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full">
                                登录
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">如何使用</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;div class="form-item"&gt;
    &lt;label v-if="label" :for="id" class="block text-sm font-medium text-gray-700 mb-1"&gt;{{ label }}&lt;/label&gt;
    &lt;div class="mt-1 relative rounded-md shadow-sm"&gt;
      &lt;div v-if="prefixIcon" class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"&gt;
        &lt;i :class="`ri-${prefixIcon} text-gray-400`"&gt;&lt;/i&gt;
      &lt;/div&gt;
      
      &lt;input
        :id="id"
        :type="type"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="[
          'block w-full border rounded-md shadow-sm focus:outline-none focus:ring-2 sm:text-sm',
          prefixIcon ? 'pl-10' : 'pl-3',
          suffixIcon || clearable ? 'pr-10' : 'pr-3',
          sizeClasses,
          statusClasses,
        ]"
        style="border: 2px solid #d1d5db;"
      /&gt;
      
      &lt;div v-if="suffixIcon || clearable" class="absolute inset-y-0 right-0 pr-3 flex items-center"&gt;
        &lt;button 
          v-if="clearable && modelValue" 
          type="button" 
          @click="$emit('update:modelValue', '')"
          class="text-gray-400 hover:text-gray-500 focus:outline-none"
        &gt;
          &lt;i class="ri-close-line"&gt;&lt;/i&gt;
        &lt;/button&gt;
        &lt;i v-else-if="suffixIcon" :class="`ri-${suffixIcon} text-gray-400`"&gt;&lt;/i&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;p v-if="errorMessage" class="mt-1 text-xs text-red-600"&gt;{{ errorMessage }}&lt;/p&gt;
    &lt;p v-else-if="helpText" class="mt-1 text-xs text-gray-500"&gt;{{ helpText }}&lt;/p&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: String,
  placeholder: String,
  type: {
    type: String,
    default: 'text'
  },
  id: {
    type: String,
    default: () => `input-${Math.random().toString(36).substring(2, 9)}`
  },
  prefixIcon: String,
  suffixIcon: String,
  clearable: Boolean,
  disabled: Boolean,
  readonly: Boolean,
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  status: {
    type: String,
    validator: (value) => ['success', 'error', 'warning', ''].includes(value)
  },
  errorMessage: String,
  helpText: String
});

defineEmits(['update:modelValue']);

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'small': return 'py-1 text-xs';
    case 'large': return 'py-3 text-base';
    default: return 'py-2 text-sm';
  }
});

const statusClasses = computed(() => {
  switch (props.status) {
    case 'success': return 'border-green-300 focus:ring-green-500';
    case 'error': return 'border-red-300 focus:ring-red-500';
    case 'warning': return 'border-yellow-300 focus:ring-yellow-500';
    default: return 'border-gray-300 focus:ring-indigo-500';
  }
});
&lt;/script&gt;
                </pre>
            </div>
        </div>
    </div>
</body>
</html>