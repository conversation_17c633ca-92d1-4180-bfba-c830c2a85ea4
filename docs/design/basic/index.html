<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础组件 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .component-card {
            transition: all 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">基础组件</span>
        </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">基础组件</h2>
            <p class="text-lg text-gray-600">
                基础组件是构建用户界面的基础元素，包括按钮、输入框、标签等常用组件。这些组件具有一致的设计风格和交互模式，便于快速构建界面。
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- 按钮组件 -->
            <a href="./buttons.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="ri-radio-button-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">按钮组件</h3>
                    </div>
                    <p class="text-gray-600 mb-4">提供多种样式和状态的按钮，包括主要按钮、次要按钮、文本按钮和图标按钮等。</p>
                    
                    <div class="mt-4 flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-indigo-600 text-white text-sm rounded-md">主要按钮</span>
                        <span class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded-md">次要按钮</span>
                        <span class="px-3 py-1 text-indigo-600 text-sm">文本按钮</span>
                    </div>
                </div>
            </a>
            
            <!-- 输入框组件 -->
            <a href="./inputs.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-blue-600 mr-4">
                            <i class="ri-input-cursor-move text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">输入框组件</h3>
                    </div>
                    <p class="text-gray-600 mb-4">各种文本输入框组件，支持不同状态、图标和样式，适应各种用户输入场景。</p>
                    
                    <div class="mt-4">
                        <div class="border border-gray-300 rounded-md px-3 py-2 flex items-center">
                            <i class="ri-search-line text-gray-400 mr-2"></i>
                            <input type="text" placeholder="搜索..." class="w-full focus:outline-none text-sm">
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 标签组件 -->
            <a href="./badges.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 text-green-600 mr-4">
                            <i class="ri-price-tag-3-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">标签组件</h3>
                    </div>
                    <p class="text-gray-600 mb-4">用于展示状态、标记和分类的标签组件，支持多种颜色和大小。</p>
                    
                    <div class="mt-4 flex flex-wrap gap-2">
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">成功</span>
                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">错误</span>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">警告</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">信息</span>
                    </div>
                </div>
            </a>

            <!-- 图标按钮 -->
            <a href="./icon-buttons.html" class="block">
                <div class="p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-blue-600">
                            <i class="ri-cpu-line text-xl"></i>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">图标按钮</h3>
                    </div>
                    <p class="text-gray-600">以图标为主要视觉元素的操作按钮</p>
                </div>
            </a>
        </div>

        <div class="text-center mb-12">
            <a href="../components.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回组件列表
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>