<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope 状态标签组件</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .badge-demo {
            margin: 10px;
            padding: 10px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
        }
        .preview-section {
            margin-bottom: 30px;
        }
        .component-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eaeaea;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">DataScope 状态标签组件</h1>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">基本状态标签</h2>
            <div class="flex flex-wrap">
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">成功状态 (Success)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                        成功
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">警告状态 (Warning)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800">
                        警告
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">错误状态 (Error)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-red-100 text-red-800">
                        错误
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">信息状态 (Info)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800">
                        信息
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">默认状态 (Default)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800">
                        默认
                    </span>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">带图标的状态标签</h2>
            <div class="flex flex-wrap">
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">成功状态 (Success)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-check mr-1"></i>
                        成功
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">警告状态 (Warning)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        警告
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">错误状态 (Error)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-red-100 text-red-800">
                        <i class="fas fa-times-circle mr-1"></i>
                        错误
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">信息状态 (Info)</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-info-circle mr-1"></i>
                        信息
                    </span>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">使用场景</h2>
            
            <div class="preview-section">
                <h3 class="text-lg font-medium mb-4">数据状态标识</h3>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">数据源1</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>
                                    正常
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">数据源2</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                    警告
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">数据源3</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    错误
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="preview-section">
                <h3 class="text-lg font-medium mb-4">任务状态</h3>
                <div class="space-y-2">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <span>导入任务</span>
                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                            完成
                        </span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <span>数据同步</span>
                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800">
                            进行中
                        </span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <span>数据备份</span>
                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800">
                            未开始
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="component-title">不同尺寸</h2>
            <div class="flex flex-wrap items-center">
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">小尺寸</p>
                    <span class="inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                        成功
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">默认尺寸</p>
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                        成功
                    </span>
                </div>
                
                <div class="badge-demo">
                    <p class="text-sm text-gray-500 mb-2">大尺寸</p>
                    <span class="inline-flex items-center rounded-md px-2.5 py-1.5 text-sm font-medium bg-green-100 text-green-800">
                        成功
                    </span>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="component-title">如何使用</h2>
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm overflow-x-auto">
&lt;template&gt;
  &lt;StatusBadge 
    status="success" 
    text="正常" 
  /&gt;
  
  &lt;StatusBadge status="warning"&gt;
    &lt;i class="fas fa-exclamation-triangle mr-1"&gt;&lt;/i&gt;
    警告
  &lt;/StatusBadge&gt;
&lt;/template&gt;

&lt;script setup&gt;
import StatusBadge from '@/components/common/StatusBadge.vue';
&lt;/script&gt;
                </pre>
            </div>
        </div>
    </div>
</body>
</html>