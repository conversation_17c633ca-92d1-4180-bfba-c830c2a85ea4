<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站地图 - DataScope UI组件库</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .site-section {
            margin-bottom: 2rem;
        }
        .site-section h3 {
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #4F46E5;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 0.5rem;
        }
        .link-group {
            margin-bottom: 1rem;
        }
        .link-group h4 {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #1F2937;
        }
        .link-description {
            font-size: 0.875rem;
            color: #6B7280;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <img class="h-8 w-auto" src="https://via.placeholder.com/40x40?text=DS" alt="DataScope Logo">
                        <span class="ml-2 text-lg font-semibold">DataScope UI</span>
                    </div>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="./components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div class="mb-10">
            <h1 class="text-3xl font-bold text-gray-900">网站地图</h1>
            <p class="mt-2 text-lg text-gray-600">DataScope UI 组件库的完整页面结构索引，帮助您快速找到所需资源。</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 主要页面 -->
            <div class="site-section">
                <h3>主要页面</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./index.html" class="text-indigo-600 hover:text-indigo-800 font-medium">首页</a>
                        <p class="link-description">组件库的主入口，提供整体概览和快速导航</p>
                    </li>
                    <li>
                        <a href="./components.html" class="text-indigo-600 hover:text-indigo-800 font-medium">组件总览</a>
                        <p class="link-description">所有组件的分类汇总，便于快速查找</p>
                    </li>

                    <li>
                        <a href="./guides/changelog.html" class="text-indigo-600 hover:text-indigo-800 font-medium">更新日志</a>
                        <p class="link-description">组件库的版本更新历史和变更记录</p>
                    </li>
                </ul>
            </div>

            <!-- 基本组件 -->
            <div class="site-section">
                <h3>基本组件</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./basic/index.html" class="text-indigo-600 hover:text-indigo-800 font-medium">基本组件概览</a>
                        <p class="link-description">基础UI元素的汇总页面</p>
                    </li>
                    <li>
                        <a href="./basic/buttons.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                            <i class="ri-radio-button-line text-indigo-600 mr-3"></i>
                            <span>按钮组件</span>
                        </a>
                    </li>
                    <li>
                        <a href="./basic/icon-buttons.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                            <i class="ri-cpu-line text-indigo-600 mr-3"></i>
                            <span>图标按钮</span>
                        </a>
                    </li>
                    <li>
                        <a href="./basic/inputs.html" class="flex items-center py-2 px-3 text-gray-700 hover:bg-gray-50 rounded-md nav-link">
                            <i class="ri-input-cursor-move text-indigo-600 mr-3"></i>
                            <span>输入框组件</span>
                        </a>
                    </li>
                    <li>
                        <a href="./basic/icons.html" class="text-indigo-600 hover:text-indigo-800 font-medium">图标</a>
                        <p class="link-description">系统提供的图标库和使用方法</p>
                    </li>
                    <li>
                        <a href="./basic/typography.html" class="text-indigo-600 hover:text-indigo-800 font-medium">排版</a>
                        <p class="link-description">文本样式和排版规范</p>
                    </li>
                    <li>
                        <a href="./basic/colors.html" class="text-indigo-600 hover:text-indigo-800 font-medium">颜色</a>
                        <p class="link-description">设计系统的颜色规范和变量</p>
                    </li>
                </ul>
            </div>

            <!-- 表单组件 -->
            <div class="site-section">
                <h3>表单组件</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./forms/index.html" class="text-indigo-600 hover:text-indigo-800 font-medium">表单组件概览</a>
                        <p class="link-description">表单相关组件的汇总页面</p>
                    </li>
                    <li>
                        <a href="./forms/input.html" class="text-indigo-600 hover:text-indigo-800 font-medium">输入框</a>
                        <p class="link-description">文本输入控件和变体</p>
                    </li>
                    <li>
                        <a href="./forms/select.html" class="text-indigo-600 hover:text-indigo-800 font-medium">选择器</a>
                        <p class="link-description">下拉选择组件和多选控件</p>
                    </li>
                    <li>
                        <a href="./forms/checkbox.html" class="text-indigo-600 hover:text-indigo-800 font-medium">复选框</a>
                        <p class="link-description">复选框和单选按钮组件</p>
                    </li>
                    <li>
                        <a href="./forms/date-picker.html" class="text-indigo-600 hover:text-indigo-800 font-medium">日期选择器</a>
                        <p class="link-description">日期和时间选择控件</p>
                    </li>
                </ul>
            </div>

            <!-- 容器组件 -->
            <div class="site-section">
                <h3>容器组件</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./container/index.html" class="text-indigo-600 hover:text-indigo-800 font-medium">容器组件概览</a>
                        <p class="link-description">容器类组件的汇总页面</p>
                    </li>
                    <li>
                        <a href="./container/card.html" class="text-indigo-600 hover:text-indigo-800 font-medium">卡片</a>
                        <p class="link-description">内容卡片和信息展示容器</p>
                    </li>
                    <li>
                        <a href="./container/modal.html" class="text-indigo-600 hover:text-indigo-800 font-medium">模态框</a>
                        <p class="link-description">弹出对话框和提示窗口</p>
                    </li>
                    <li>
                        <a href="./container/tabs.html" class="text-indigo-600 hover:text-indigo-800 font-medium">标签页</a>
                        <p class="link-description">选项卡切换组件</p>
                    </li>
                    <li>
                        <a href="./container/collapse.html" class="text-indigo-600 hover:text-indigo-800 font-medium">折叠面板</a>
                        <p class="link-description">可折叠的内容区域</p>
                    </li>
                </ul>
            </div>

            <!-- 数据展示 -->
            <div class="site-section">
                <h3>数据展示</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./display/index.html" class="text-indigo-600 hover:text-indigo-800 font-medium">数据展示概览</a>
                        <p class="link-description">数据展示类组件的汇总页面</p>
                    </li>
                    <li>
                        <a href="./display/table.html" class="text-indigo-600 hover:text-indigo-800 font-medium">数据表格</a>
                        <p class="link-description">结构化数据的表格展示</p>
                    </li>
                    <li>
                        <a href="./display/pagination.html" class="text-indigo-600 hover:text-indigo-800 font-medium">分页</a>
                        <p class="link-description">分页控件和页码导航</p>
                    </li>
                    <li>
                        <a href="./display/tags.html" class="text-indigo-600 hover:text-indigo-800 font-medium">标签</a>
                        <p class="link-description">分类标签和状态标识</p>
                    </li>
                    <li>
                        <a href="./display/action-buttons.html" class="text-indigo-600 hover:text-indigo-800 font-medium">操作图标</a>
                        <p class="link-description">数据操作的图标按钮规范</p>
                    </li>
                </ul>
            </div>

            <!-- 动画效果 -->
            <div class="site-section">
                <h3>动画效果</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./animations/loading.html" class="text-indigo-600 hover:text-indigo-800 font-medium">加载动画</a>
                        <p class="link-description">数据加载状态的动画效果</p>
                    </li>
                    <li>
                        <a href="./animations/transitions.html" class="text-indigo-600 hover:text-indigo-800 font-medium">过渡动画</a>
                        <p class="link-description">元素状态变化的过渡效果</p>
                    </li>
                </ul>
            </div>

            <!-- 使用指引 -->
            <div class="site-section">
                <h3>使用指引</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="./guides/index.html" class="text-indigo-600 hover:text-indigo-800 font-medium">使用指引概览</a>
                        <p class="link-description">组件库使用文档的汇总页面</p>
                    </li>
                    <li>
                        <a href="./guides/vue3.html" class="text-indigo-600 hover:text-indigo-800 font-medium">Vue 3 示例</a>
                        <p class="link-description">组件在Vue 3项目中的使用示例</p>
                    </li>
                    <li>
                        <a href="./guides/theme.html" class="text-indigo-600 hover:text-indigo-800 font-medium">主题定制</a>
                        <p class="link-description">如何自定义组件库的主题样式</p>
                    </li>
                </ul>
            </div>
        </div>
    </main>

    <footer class="bg-white border-t border-gray-200 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-500">© 2023 DataScope UI. 保留所有权利</p>
                </div>
                <div class="flex space-x-4">
                    <a href="./guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="#" class="text-gray-900 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>