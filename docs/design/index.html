<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataScope UI 组件库</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .category-card {
            transition: all 0.3s ease;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .component-pill {
            transition: all 0.2s ease;
        }
        .component-pill:hover {
            background-color: #4f46e5;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="./index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="./components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="./guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold mb-4 text-gray-900">DataScope UI 组件库</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">专为数据分析平台设计的高效组件库，提供一致的视觉体验和交互模式</p>
            <div class="flex justify-center mt-8 space-x-4">
                <a href="./components.html" class="px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition duration-300">浏览组件</a>
                <a href="./guides/index.html" class="px-6 py-3 bg-white text-indigo-600 font-medium rounded-lg border border-indigo-600 hover:bg-indigo-50 transition duration-300">快速开始</a>
            </div>
        </div>

        <!-- 组件概览 -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">组件概览</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <!-- 基础组件 -->
                <a href="./basic/index.html" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                        <i class="ri-layout-2-line text-xl text-blue-600"></i>
                    </div>
                    <span class="text-gray-900 font-medium">基础组件</span>
                </a>
                
                <!-- 表单组件 -->
                <a href="./forms/index.html" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-3">
                        <i class="ri-file-list-3-line text-xl text-green-600"></i>
                    </div>
                    <span class="text-gray-900 font-medium">表单组件</span>
                </a>
                
                <!-- 容器组件 -->
                <a href="./container/index.html" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-3">
                        <i class="ri-layout-masonry-line text-xl text-purple-600"></i>
                    </div>
                    <span class="text-gray-900 font-medium">容器组件</span>
                </a>
                
                <!-- 数据展示 -->
                <a href="./display/index.html" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mb-3">
                        <i class="ri-table-line text-xl text-yellow-600"></i>
                    </div>
                    <span class="text-gray-900 font-medium">数据展示</span>
                </a>
                
                <!-- 反馈组件 -->
                <a href="./feedback/index.html" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-3">
                        <i class="ri-notification-3-line text-xl text-indigo-600"></i>
                    </div>
                    <span class="text-gray-900 font-medium">反馈组件</span>
                </a>
                
                <!-- 动画效果 -->
                <a href="./animations/index.html" class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mb-3">
                        <i class="ri-slideshow-3-line text-xl text-pink-600"></i>
                    </div>
                    <span class="text-gray-900 font-medium">动画效果</span>
                </a>
            </div>
        </div>
        
        <!-- 特色功能区块 -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">热门组件</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 数据表格 -->
                <a href="./display/data-table.html" class="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition duration-300">
                    <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                        <i class="ri-table-line text-xl text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="font-medium text-gray-900">数据表格</h3>
                        <p class="text-sm text-gray-600">支持排序、筛选和分页功能</p>
                    </div>
                </a>
                
                <!-- 消息提示 -->
                <a href="./feedback/message.html" class="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition duration-300">
                    <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <i class="ri-message-2-line text-xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="font-medium text-gray-900">消息提示</h3>
                        <p class="text-sm text-gray-600">轻量级反馈通知组件</p>
                    </div>
                </a>
                
                <!-- 对话框 -->
                <a href="./feedback/dialog.html" class="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition duration-300">
                    <div class="flex-shrink-0 w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center">
                        <i class="ri-question-answer-line text-xl text-indigo-600"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="font-medium text-gray-900">对话框</h3>
                        <p class="text-sm text-gray-600">模态弹窗，用于重要操作的确认</p>
                    </div>
                </a>
            </div>
        </div>
            
        <!-- 设计指南 -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold mb-4">设计与开发指南</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-medium mb-4 flex items-center">
                        <i class="ri-palette-line text-indigo-600 mr-2"></i> 设计原则
                    </h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-0.5 mr-2">
                                <span class="text-xs font-medium text-indigo-700">01</span>
                            </div>
                            <p class="text-gray-700">一致性 - 组件在各个平台和产品上保持一致的外观和行为</p>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-0.5 mr-2">
                                <span class="text-xs font-medium text-indigo-700">02</span>
                            </div>
                            <p class="text-gray-700">易用性 - 组件的设计和交互方式符合用户习惯和预期</p>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-0.5 mr-2">
                                <span class="text-xs font-medium text-indigo-700">03</span>
                            </div>
                            <p class="text-gray-700">灵活性 - 组件支持丰富的定制化选项，适应不同场景</p>
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-medium mb-4 flex items-center">
                        <i class="ri-code-s-slash-line text-indigo-600 mr-2"></i> 开发指南
                    </h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-0.5 mr-2">
                                <span class="text-xs font-medium text-indigo-700">01</span>
                            </div>
                            <p class="text-gray-700">模块化 - 组件设计遵循单一职责原则，易于组合和复用</p>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-0.5 mr-2">
                                <span class="text-xs font-medium text-indigo-700">02</span>
                            </div>
                            <p class="text-gray-700">类型安全 - 提供完善的 TypeScript 类型定义，提升开发效率</p>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center mt-0.5 mr-2">
                                <span class="text-xs font-medium text-indigo-700">03</span>
                            </div>
                            <p class="text-gray-700">树摘优化 - 组件支持按需导入，最小化打包体积</p>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-6 flex justify-center">
                <a href="./guides/index.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none">
                    查看完整指南 <i class="ri-arrow-right-line ml-2"></i>
                </a>
            </div>
        </div>
        </div>

        <!-- 设计与开发指南 -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold mb-4">设计与开发指南</h2>
            <p class="mb-6 text-gray-600">DataScope UI组件库提供了一套完整的设计规范和开发指引，帮助您构建一致、美观、易用的用户界面。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <a href="./guides/design-principles.html" class="block group">
                    <div class="bg-gray-50 p-5 rounded-lg group-hover:bg-gray-100 transition duration-150">
                        <div class="flex items-center mb-3">
                            <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md bg-blue-500 text-white">
                                <i class="ri-palette-line"></i>
                            </div>
                            <h3 class="ml-3 font-medium text-gray-900">设计原则</h3>
                        </div>
                        <p class="text-gray-600">了解我们的核心设计原则，包括一致性、简洁性、可用性和响应式设计的要点。</p>
                    </div>
                </a>
                
                <a href="./guides/development.html" class="block group">
                    <div class="bg-gray-50 p-5 rounded-lg group-hover:bg-gray-100 transition duration-150">
                        <div class="flex items-center mb-3">
                            <div class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md bg-green-500 text-white">
                                <i class="ri-code-s-slash-line"></i>
                            </div>
                            <h3 class="ml-3 font-medium text-gray-900">开发指南</h3>
                        </div>
                        <p class="text-gray-600">获取组件开发的最佳实践，包括组件的使用、定制和扩展方法。</p>
                    </div>
                </a>
            </div>
            
            <div class="mt-6 text-center">
                <a href="./guides/index.html" class="inline-flex items-center px-5 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    查看所有指南 <i class="ri-arrow-right-line ml-1"></i>
                </a>
            </div>
        </div>

        <!-- 最近更新 -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold">最近更新</h2>
                <a href="./guides/changelog.html" class="text-indigo-600 hover:text-indigo-800 text-sm">查看全部更新</a>
            </div>
            
            <div class="space-y-4">
                <div class="border-l-4 border-green-500 pl-4 py-1">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="font-medium">新增组件：日期选择器和文件上传</h3>
                        <span class="text-sm text-gray-500">2023-04-06</span>
                    </div>
                    <p class="text-sm text-gray-600">新增了日期选择器和文件上传组件，支持更复杂的表单场景</p>
                </div>
                
                <div class="border-l-4 border-blue-500 pl-4 py-1">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="font-medium">动画效果组件库</h3>
                        <span class="text-sm text-gray-500">2023-04-05</span>
                    </div>
                    <p class="text-sm text-gray-600">新增了过渡动画和加载动画组件，提升交互体验</p>
                </div>
                
                <div class="border-l-4 border-gray-500 pl-4 py-1">
                    <div class="flex justify-between items-center mb-1">
                        <h3 class="font-medium">容器组件优化</h3>
                        <span class="text-sm text-gray-500">2023-04-04</span>
                    </div>
                    <p class="text-sm text-gray-600">优化了卡片和面板组件，提供更多的布局选项</p>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-8 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="./guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="./sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>