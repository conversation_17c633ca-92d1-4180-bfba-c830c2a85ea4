<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息提示 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .message {
            position: fixed;
            top: 16px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1010;
            background: white;
            padding: 8px 16px;
            border-radius: 4px;
            box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
            pointer-events: all;
            display: flex;
            align-items: center;
        }
        
        .demo-trigger {
            transition: all 0.3s;
        }
        
        .demo-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./message.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">消息提示</a>
                            </li>
                            <li>
                                <a href="./notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="./dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1">对话框</a>
                            </li>
                            <li>
                                <a href="./alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="container mx-auto px-4 py-8">
        <!-- 导航面包屑 -->
        <div class="mb-8">
            <div class="flex items-center mb-3">
                <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                    <i class="ri-home-line"></i>
                </a>
                <span class="text-gray-400 mx-2">/</span>
                <a href="../components.html" class="text-gray-500 hover:text-indigo-600">组件</a>
                <span class="text-gray-400 mx-2">/</span>
                <a href="./index.html" class="text-gray-500 hover:text-indigo-600">反馈组件</a>
                <span class="text-gray-400 mx-2">/</span>
                <span class="text-gray-800">消息提示</span>
            </div>
            
            <!-- 当前位置指示器 -->
            <nav class="flex bg-gray-50 text-gray-700 border border-gray-200 py-3 px-5 rounded-lg mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <i class="ri-notification-3-line text-indigo-600 mr-2"></i>
                        <span class="text-gray-500">反馈组件</span>
                        <i class="ri-arrow-right-s-line mx-2 text-gray-400"></i>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium">消息提示 Message</span>
                        </div>
                    </li>
                </ol>
            </nav>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">消息提示</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                轻量级的消息反馈组件，在顶部居中显示，并自动消失。用于操作后的简短反馈提示，如成功、警告或错误提示。
            </p>
        </div>

        <!-- 基础用法 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">基础用法</h3>
            <p class="text-gray-600 mb-6">
                最简单的消息提示用法，支持四种不同类型的消息：常规信息、成功、警告和错误。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                        <i class="ri-information-line text-blue-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                        信息提示
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-3">
                        <i class="ri-check-line text-green-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">
                        成功提示
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mb-3">
                        <i class="ri-alert-line text-yellow-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition">
                        警告提示
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mb-3">
                        <i class="ri-close-circle-line text-red-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition">
                        错误提示
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-4">
                <div class="message">
                    <i class="ri-information-line text-blue-600 mr-2"></i>
                    <span class="text-gray-800">这是一条消息提示</span>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// Vue 3 示例代码
import { Message } from 'datascope-ui'

// 基础用法
Message.info('这是一条消息提示')
Message.success('操作成功')
Message.warning('警告提示内容')
Message.error('错误提示内容')
                </pre>
            </div>
        </div>
        
        <!-- 自定义时长和加载中状态 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">自定义时长和加载中状态</h3>
            <p class="text-gray-600 mb-6">
                可以自定义消息提示的显示时长，或者显示加载中状态的消息。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">自定义显示时长</h4>
                    <div class="mb-4">
                        <div class="message">
                            <i class="ri-check-line text-green-600 mr-2"></i>
                            <span class="text-gray-800">这个消息会显示 10 秒</span>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition">
                        显示 10 秒
                    </button>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">加载中状态</h4>
                    <div class="mb-4">
                        <div class="message">
                            <svg class="animate-spin h-5 w-5 text-indigo-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="text-gray-800">正在加载中...</span>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition">
                        显示加载中
                    </button>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">更新消息内容</h4>
                    <div class="mb-4">
                        <div class="message">
                            <i class="ri-check-line text-green-600 mr-2"></i>
                            <span class="text-gray-800">加载完成！</span>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition">
                        更新消息
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 自定义显示时长（单位：秒）
Message.success('这个消息会显示 10 秒', 10)

// 加载中状态
const loadingMessage = Message.loading('正在加载中...')

// 3 秒后更新消息
setTimeout(() => {
  loadingMessage.update('加载完成！', 'success')
}, 3000)

// 或者手动关闭
// loadingMessage.close()
                </pre>
            </div>
        </div>

        <!-- 自定义样式和内容 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">自定义样式和内容</h3>
            <p class="text-gray-600 mb-6">
                除了标准样式外，还可以自定义消息的样式和内容。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">带图标的自定义样式</h4>
                    <div class="mb-4">
                        <div class="message" style="background: linear-gradient(to right, #4f46e5, #818cf8); color: white; padding: 10px 16px;">
                            <i class="ri-star-line mr-2"></i>
                            <span>这是一条带渐变背景的消息</span>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition">
                        自定义样式
                    </button>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">复杂内容</h4>
                    <div class="mb-4">
                        <div class="message px-4 py-3" style="min-width: 300px;">
                            <div>
                                <div class="flex items-center mb-1">
                                    <i class="ri-upload-2-line text-indigo-600 mr-2"></i>
                                    <span class="font-medium">文件上传完成</span>
                                </div>
                                <div class="text-sm text-gray-500">
                                    文件 "data-report.xlsx" 已成功上传
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition">
                        复杂内容
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 自定义样式
Message.open({
  content: '这是一条带渐变背景的消息',
  icon: 'star',
  className: 'custom-message',
  style: {
    background: 'linear-gradient(to right, #4f46e5, #818cf8)',
    color: 'white',
    padding: '10px 16px'
  }
})

// 复杂内容
Message.open({
  content: h => h('div', [
    h('div', { class: 'flex items-center mb-1' }, [
      h('i', { class: 'ri-upload-2-line text-indigo-600 mr-2' }),
      h('span', { class: 'font-medium' }, '文件上传完成')
    ]),
    h('div', { class: 'text-sm text-gray-500' }, '文件 "data-report.xlsx" 已成功上传')
  ]),
  duration: 5
})
                </pre>
            </div>
        </div>
        
        <!-- API参考 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">API参考</h3>
            
            <div class="mb-6">
                <h4 class="font-medium mb-3">Message方法</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">方法名</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">说明</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">参数</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Message.info(content, [duration])</td>
                                <td class="px-4 py-3 text-sm text-gray-500">显示信息提示</td>
                                <td class="px-4 py-3 text-sm text-gray-500">content: 内容, duration: 展示时间(秒)</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Message.success(content, [duration])</td>
                                <td class="px-4 py-3 text-sm text-gray-500">显示成功提示</td>
                                <td class="px-4 py-3 text-sm text-gray-500">content: 内容, duration: 展示时间(秒)</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Message.warning(content, [duration])</td>
                                <td class="px-4 py-3 text-sm text-gray-500">显示警告提示</td>
                                <td class="px-4 py-3 text-sm text-gray-500">content: 内容, duration: 展示时间(秒)</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Message.error(content, [duration])</td>
                                <td class="px-4 py-3 text-sm text-gray-500">显示错误提示</td>
                                <td class="px-4 py-3 text-sm text-gray-500">content: 内容, duration: 展示时间(秒)</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Message.loading(content, [duration])</td>
                                <td class="px-4 py-3 text-sm text-gray-500">显示加载中提示</td>
                                <td class="px-4 py-3 text-sm text-gray-500">content: 内容, duration: 展示时间(秒)</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">Message.open(config)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">打开一个自定义消息</td>
                                <td class="px-4 py-3 text-sm text-gray-500">config: 配置对象</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium mb-3">配置参数</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">参数</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">说明</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">类型</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">默认值</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">content</td>
                                <td class="px-4 py-3 text-sm text-gray-500">消息内容</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string | VNode | (() => VNode)</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">duration</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自动关闭的延时，单位秒，设为 0 不自动关闭</td>
                                <td class="px-4 py-3 text-sm text-gray-500">number</td>
                                <td class="px-4 py-3 text-sm text-gray-500">3</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">type</td>
                                <td class="px-4 py-3 text-sm text-gray-500">消息类型</td>
                                <td class="px-4 py-3 text-sm text-gray-500">info | success | warning | error | loading</td>
                                <td class="px-4 py-3 text-sm text-gray-500">info</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">icon</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义图标</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string | VNode</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">className</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义 CSS 类名</td>
                                <td class="px-4 py-3 text-sm text-gray-500">string</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">style</td>
                                <td class="px-4 py-3 text-sm text-gray-500">自定义内联样式</td>
                                <td class="px-4 py-3 text-sm text-gray-500">CSSProperties</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">onClose</td>
                                <td class="px-4 py-3 text-sm text-gray-500">关闭时的回调函数</td>
                                <td class="px-4 py-3 text-sm text-gray-500">() => void</td>
                                <td class="px-4 py-3 text-sm text-gray-500">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回反馈组件列表
            </a>
        </div>
    </div>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>