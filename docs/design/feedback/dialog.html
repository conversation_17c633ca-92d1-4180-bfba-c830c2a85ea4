<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话框 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .dialog-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .dialog {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .demo-trigger {
            transition: all 0.3s;
        }
        
        .demo-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="flex min-h-screen">
        <!-- 左侧导航菜单 -->
        <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">组件分类</h3>
                
                <div class="space-y-4">
                    <!-- 基础组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-2-line mr-2 text-blue-600"></i>
                            <span>基础组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../basic/buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">按钮</a>
                            </li>
                            <li>
                                <a href="../basic/icon-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">图标按钮</a>
                            </li>
                            <li>
                                <a href="../basic/inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">输入框</a>
                            </li>
                            <li>
                                <a href="../basic/badges.html" class="block text-gray-700 hover:text-indigo-600 py-1">状态标签</a>
                            </li>
                            <li>
                                <a href="../basic/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部基础组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 表单组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-file-list-3-line mr-2 text-green-600"></i>
                            <span>表单组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../forms/basic-inputs.html" class="block text-gray-700 hover:text-indigo-600 py-1">基础输入</a>
                            </li>
                            <li>
                                <a href="../forms/selectors.html" class="block text-gray-700 hover:text-indigo-600 py-1">选择器</a>
                            </li>
                            <li>
                                <a href="../forms/toggles.html" class="block text-gray-700 hover:text-indigo-600 py-1">开关控件</a>
                            </li>
                            <li>
                                <a href="../forms/date-picker.html" class="block text-gray-700 hover:text-indigo-600 py-1">日期选择器</a>
                            </li>
                            <li>
                                <a href="../forms/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部表单组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 容器组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-layout-masonry-line mr-2 text-purple-600"></i>
                            <span>容器组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../container/cards.html" class="block text-gray-700 hover:text-indigo-600 py-1">卡片容器</a>
                            </li>
                            <li>
                                <a href="../container/panels.html" class="block text-gray-700 hover:text-indigo-600 py-1">面板容器</a>
                            </li>
                            <li>
                                <a href="../container/boxes.html" class="block text-gray-700 hover:text-indigo-600 py-1">盒子容器</a>
                            </li>
                            <li>
                                <a href="../container/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部容器组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 数据展示 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-table-line mr-2 text-yellow-600"></i>
                            <span>数据展示</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../display/data-table.html" class="block text-gray-700 hover:text-indigo-600 py-1">数据表格</a>
                            </li>
                            <li>
                                <a href="../display/action-buttons.html" class="block text-gray-700 hover:text-indigo-600 py-1">操作按钮</a>
                            </li>
                            <li>
                                <a href="../display/page-header.html" class="block text-gray-700 hover:text-indigo-600 py-1">页面标题</a>
                            </li>
                            <li>
                                <a href="../display/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部数据展示</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 反馈组件 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-notification-3-line mr-2 text-indigo-600"></i>
                            <span>反馈组件</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="./message.html" class="block text-gray-700 hover:text-indigo-600 py-1">消息提示</a>
                            </li>
                            <li>
                                <a href="./notification.html" class="block text-gray-700 hover:text-indigo-600 py-1">通知提醒</a>
                            </li>
                            <li>
                                <a href="./dialog.html" class="block text-gray-700 hover:text-indigo-600 py-1 font-medium text-indigo-600">对话框</a>
                            </li>
                            <li>
                                <a href="./alert.html" class="block text-gray-700 hover:text-indigo-600 py-1">警告提示</a>
                            </li>
                            <li>
                                <a href="./index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部反馈组件</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- 动画效果 -->
                    <div>
                        <h4 class="flex items-center text-base font-medium text-gray-900 mb-2">
                            <i class="ri-slideshow-3-line mr-2 text-pink-600"></i>
                            <span>动画效果</span>
                        </h4>
                        <ul class="ml-6 space-y-1">
                            <li>
                                <a href="../animations/transitions.html" class="block text-gray-700 hover:text-indigo-600 py-1">过渡动画</a>
                            </li>
                            <li>
                                <a href="../animations/loading.html" class="block text-gray-700 hover:text-indigo-600 py-1">加载动画</a>
                            </li>
                            <li>
                                <a href="../animations/index.html" class="block text-gray-700 hover:text-indigo-600 py-1">全部动画效果</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容区 -->
        <div class="flex-1 p-6 overflow-y-auto">
            <div class="container mx-auto px-4 py-8">
        <!-- 导航面包屑 -->
        <div class="mb-8">
            <div class="flex items-center mb-3">
                <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                    <i class="ri-home-line"></i>
                </a>
                <span class="text-gray-400 mx-2">/</span>
                <a href="../components.html" class="text-gray-500 hover:text-indigo-600">组件</a>
                <span class="text-gray-400 mx-2">/</span>
                <a href="./index.html" class="text-gray-500 hover:text-indigo-600">反馈组件</a>
                <span class="text-gray-400 mx-2">/</span>
                <span class="text-gray-800">对话框</span>
            </div>
            
            <!-- 当前位置指示器 -->
            <nav class="flex bg-gray-50 text-gray-700 border border-gray-200 py-3 px-5 rounded-lg mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <i class="ri-notification-3-line text-indigo-600 mr-2"></i>
                        <span class="text-gray-500">反馈组件</span>
                        <i class="ri-arrow-right-s-line mx-2 text-gray-400"></i>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <span class="text-gray-800 font-medium">对话框 Dialog</span>
                        </div>
                    </li>
                </ol>
            </nav>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">对话框</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                模态对话框，在当前页面打开一个浮层，用于重要信息的展示、交互操作的反馈和简单的表单输入。
            </p>
        </div>

        <!-- 基础用法 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">基础用法</h3>
            <p class="text-gray-600 mb-6">
                最简单的对话框用法，包含标题、内容和操作按钮。点击遮罩或取消按钮可关闭对话框。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                        <i class="ri-information-line text-blue-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                        信息对话框
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-3">
                        <i class="ri-check-line text-green-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition">
                        成功对话框
                    </button>
                </div>
                
                <div class="demo-trigger bg-white border border-gray-200 rounded-md p-4 flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mb-3">
                        <i class="ri-close-circle-line text-red-600"></i>
                    </div>
                    <button class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition">
                        错误对话框
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-4">
                <div class="max-w-md mx-auto bg-white shadow-lg rounded-lg overflow-hidden border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">对话框标题</h3>
                            <button class="text-gray-400 hover:text-gray-500">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <p class="text-gray-600">这是一个简单的对话框示例，用于展示重要信息或请求用户确认操作。</p>
                    </div>
                    <div class="px-6 py-3 bg-gray-50 text-right">
                        <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mr-2">
                            取消
                        </button>
                        <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            确定
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// Vue 3 示例代码
import { Dialog } from 'datascope-ui'

// 基础用法
Dialog.open({
  title: '对话框标题',
  content: '这是一个简单的对话框示例，用于展示重要信息或请求用户确认操作。',
  onOk: () => {
    console.log('点击确定')
  },
  onCancel: () => {
    console.log('点击取消')
  }
})
                </pre>
            </div>
        </div>
        
        <!-- 确认对话框 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">确认对话框</h3>
            <p class="text-gray-600 mb-6">
                用于在进行某些操作前请求用户进行确认，不提供取消按钮则为纯信息展示。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">删除确认对话框</h4>
                    <div class="mb-4">
                        <div class="max-w-sm bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
                            <div class="px-4 py-3 border-b border-gray-200">
                                <h3 class="text-base font-medium text-red-600">确认删除</h3>
                            </div>
                            <div class="px-4 py-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="ri-error-warning-line text-red-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-600">您确定要删除这个项目吗？删除后不可恢复。</p>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-3 bg-gray-50 text-right">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50 mr-2">
                                    取消
                                </button>
                                <button class="px-3 py-1 border border-transparent rounded-md text-sm text-white bg-red-600 hover:bg-red-700">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition">
                        显示删除确认
                    </button>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-md p-4">
                    <h4 class="font-medium mb-3">信息提示对话框</h4>
                    <div class="mb-4">
                        <div class="max-w-sm bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
                            <div class="px-4 py-3 border-b border-gray-200">
                                <h3 class="text-base font-medium text-blue-600">信息提示</h3>
                            </div>
                            <div class="px-4 py-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="ri-information-line text-blue-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-600">您的订单已成功提交，将在1-2工作日内处理。</p>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-3 bg-gray-50 text-right">
                                <button class="px-3 py-1 border border-transparent rounded-md text-sm text-white bg-blue-600 hover:bg-blue-700">
                                    知道了
                                </button>
                            </div>
                        </div>
                    </div>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                        显示信息提示
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-100 p-4 rounded">
                <pre class="text-sm text-gray-800 overflow-x-auto">
// 删除确认对话框
Dialog.confirm({
  title: '确认删除',
  content: '您确定要删除这个项目吗？删除后不可恢复。',
  okText: '删除',
  okButtonProps: { type: 'danger' },
  onOk: () => {
    console.log('执行删除操作')
  }
})

// 信息提示对话框
Dialog.info({
  title: '信息提示',
  content: '您的订单已成功提交，将在1-2工作日内处理。',
  okText: '知道了',
  cancelButtonProps: { style: { display: 'none' } }
})
                </pre>
            </div>
        <div class="text-center mb-12">
            <a href="./index.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回反馈组件列表
            </a>
        </div>
    </div>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>