<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈组件 - DataScope UI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .component-card {
            transition: all 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50">
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">DataScope UI</h1>
                    </a>
                </div>
                <nav class="flex items-center space-x-4">
                    <a href="../index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                    <a href="../components.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">组件</a>
                    <a href="../guides/index.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">使用指引</a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">网站地图</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="flex items-center mb-8">
            <a href="../index.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                <i class="ri-home-line"></i>
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <a href="../components.html" class="text-gray-500 hover:text-indigo-600 mr-2">
                组件
            </a>
            <span class="text-gray-400 mx-2">/</span>
            <span class="text-gray-900 font-medium">反馈组件</span>
        </div>

        <div class="mb-8">
            <h2 class="text-3xl font-bold mb-4 text-gray-900">反馈组件</h2>
            <p class="text-lg text-gray-600 max-w-4xl">
                反馈组件用于向用户提供操作结果、状态更新和需要关注的信息。这些组件帮助用户了解系统状态，增强用户体验和交互流畅度。
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- 消息提示 -->
            <a href="./message.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-blue-600 mr-4">
                            <i class="ri-message-2-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">消息提示</h3>
                    </div>
                    <p class="text-gray-600 mb-4">轻量级的反馈提示，用于操作后的结果展示。支持成功、警告、错误等不同状态。</p>
                    
                    <div class="flex flex-col space-y-2 mt-4">
                        <div class="px-4 py-2 bg-green-100 text-green-800 rounded flex items-center">
                            <i class="ri-check-line mr-2"></i> 操作成功完成
                        </div>
                        <div class="px-4 py-2 bg-blue-100 text-blue-800 rounded flex items-center">
                            <i class="ri-information-line mr-2"></i> 这是一条信息提示
                        </div>
                        <div class="px-4 py-2 bg-yellow-100 text-yellow-800 rounded flex items-center">
                            <i class="ri-alert-line mr-2"></i> 请注意此操作可能有风险
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 通知提醒 -->
            <a href="./notification.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 text-purple-600 mr-4">
                            <i class="ri-notification-3-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">通知提醒</h3>
                    </div>
                    <p class="text-gray-600 mb-4">右上角弹出的通知框，用于系统全局通知和较为重要的提醒信息。</p>
                    
                    <div class="mt-4 border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="ri-notification-3-line text-purple-500"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-gray-900">系统通知</h4>
                                <p class="mt-1 text-sm text-gray-500">您的分析任务已完成，可以查看结果。</p>
                                <div class="mt-2 flex">
                                    <button class="mr-2 text-xs text-indigo-600">查看详情</button>
                                    <button class="text-xs text-gray-500">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 对话框 -->
            <a href="./dialog.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="ri-question-answer-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">对话框</h3>
                    </div>
                    <p class="text-gray-600 mb-4">模态对话框，用于重要操作的确认、信息展示和简单的表单输入。</p>
                    
                    <div class="mt-4 border border-gray-200 rounded-lg p-4 bg-white shadow flex flex-col">
                        <div class="mb-3 font-medium">确认删除</div>
                        <p class="text-sm text-gray-600 mb-4">您确定要删除此项目吗？此操作不可逆。</p>
                        <div class="flex justify-end space-x-2">
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">取消</button>
                            <button class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600">删除</button>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 警告提示 -->
            <a href="./alert.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                            <i class="ri-alert-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">警告提示</h3>
                    </div>
                    <p class="text-gray-600 mb-4">用于页面内的警告提示信息，支持不同类型和可关闭功能。</p>
                    
                    <div class="mt-4">
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-2">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-alert-line text-yellow-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        请注意，此操作将会更新所有关联记录。
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-red-50 border-l-4 border-red-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="ri-error-warning-line text-red-500"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700">
                                        访问受限，您没有执行此操作的权限。
                                    </p>
                                </div>
                                <div class="ml-auto pl-3">
                                    <div class="-mx-1.5 -my-1.5">
                                        <button class="text-red-500 hover:text-red-600 focus:outline-none p-1.5">
                                            <i class="ri-close-line"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 进度指示器 -->
            <a href="./progress.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 text-green-600 mr-4">
                            <i class="ri-loader-2-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">进度指示器</h3>
                    </div>
                    <p class="text-gray-600 mb-4">展示操作进度状态，包括直线进度条、环形进度条和步骤进度。</p>
                    
                    <div class="mt-4 space-y-4">
                        <div>
                            <div class="mb-1 flex justify-between">
                                <span class="text-xs text-gray-600">上传进度</span>
                                <span class="text-xs text-gray-600">75%</span>
                            </div>
                            <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div class="bg-green-500 h-full rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between">
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-green-500 text-white">
                                <i class="ri-check-line"></i>
                            </div>
                            <div class="flex-grow mx-2 h-1 bg-green-500 self-center"></div>
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-green-500 text-white">
                                <i class="ri-check-line"></i>
                            </div>
                            <div class="flex-grow mx-2 h-1 bg-blue-500 self-center"></div>
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 text-white">
                                <span class="text-xs">3</span>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
            
            <!-- 结果页 -->
            <a href="./result.html" class="block">
                <div class="bg-white rounded-lg shadow-md component-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-blue-600 mr-4">
                            <i class="ri-file-list-3-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">结果页</h3>
                    </div>
                    <p class="text-gray-600 mb-4">用于反馈一个流程操作的结果，如成功、失败、完成等。</p>
                    
                    <div class="mt-4 flex flex-col items-center text-center p-4">
                        <div class="w-16 h-16 flex items-center justify-center rounded-full bg-green-100 text-green-600 mb-4">
                            <i class="ri-check-line text-3xl"></i>
                        </div>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">操作成功</h4>
                        <p class="text-sm text-gray-500 mb-4">您的数据已成功提交，我们将在1-3个工作日内进行处理。</p>
                        <div class="space-x-2">
                            <button class="px-4 py-2 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700">返回列表</button>
                            <button class="px-4 py-2 border border-gray-300 rounded text-sm hover:bg-gray-50">查看详情</button>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">设计规范</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-semibold mb-2">反馈类型</h4>
                <ul class="list-disc pl-6 space-y-2 text-gray-700">
                    <li><strong>操作反馈</strong>：用户执行操作后立即给予的反馈，如消息提示、通知提醒</li>
                    <li><strong>状态反馈</strong>：展示系统或操作的当前状态，如进度指示器、警告提示</li>
                    <li><strong>结果反馈</strong>：完整流程结束后的结果展示，如结果页</li>
                    <li><strong>交互反馈</strong>：需要用户进一步确认或输入的交互式反馈，如对话框</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h4 class="text-lg font-semibold mb-2">反馈级别</h4>
                <ul class="list-disc pl-6 space-y-2 text-gray-700">
                    <li><strong>信息（Info）</strong>：提供中性的信息，蓝色，图标通常为 <i class="ri-information-line text-blue-500"></i></li>
                    <li><strong>成功（Success）</strong>：表示操作成功，绿色，图标通常为 <i class="ri-check-line text-green-500"></i></li>
                    <li><strong>警告（Warning）</strong>：提醒用户注意，黄色，图标通常为 <i class="ri-alert-line text-yellow-500"></i></li>
                    <li><strong>错误（Error）</strong>：表示操作失败或出错，红色，图标通常为 <i class="ri-error-warning-line text-red-500"></i></li>
                </ul>
            </div>
            
            <div>
                <h4 class="text-lg font-semibold mb-2">反馈时机</h4>
                <ul class="list-disc pl-6 space-y-2 text-gray-700">
                    <li><strong>即时反馈</strong>：用户操作后立即触发，如提交表单、点击按钮</li>
                    <li><strong>状态变化</strong>：系统状态发生变化时，如任务完成、新消息到达</li>
                    <li><strong>重要操作前</strong>：执行重要或不可逆操作前，如删除、支付</li>
                    <li><strong>异常情况</strong>：系统出现异常或错误时，如网络错误、权限不足</li>
                </ul>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-12">
            <h3 class="text-xl font-bold mb-4">最佳实践</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-2">适当使用反馈</h4>
                    <ul class="list-disc pl-6 space-y-1 text-gray-700">
                        <li>根据操作的重要性选择合适的反馈类型</li>
                        <li>避免过度使用反馈，以免造成用户疲劳</li>
                        <li>对同类操作保持一致的反馈风格</li>
                        <li>考虑用户操作环境和上下文，提供有意义的反馈</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-2">清晰准确的信息</h4>
                    <ul class="list-disc pl-6 space-y-1 text-gray-700">
                        <li>使用简洁明了的语言描述反馈内容</li>
                        <li>提供具体的错误原因和解决方案</li>
                        <li>避免使用技术术语和代码错误</li>
                        <li>为用户提供后续操作的指导或建议</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-2">视觉与交互设计</h4>
                    <ul class="list-disc pl-6 space-y-1 text-gray-700">
                        <li>使用一致的颜色和图标体系表达不同状态</li>
                        <li>考虑反馈组件的位置和展示方式</li>
                        <li>为重要的反馈信息提供足够的展示时间</li>
                        <li>提供适当的动画效果增强用户感知</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-2">可访问性考虑</h4>
                    <ul class="list-disc pl-6 space-y-1 text-gray-700">
                        <li>确保色彩对比度符合WCAG标准</li>
                        <li>不仅依赖颜色传达信息，同时使用图标和文字</li>
                        <li>为屏幕阅读器提供适当的ARIA标签</li>
                        <li>考虑键盘操作和焦点管理</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="text-center mb-12">
            <a href="../components.html" class="inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="ri-arrow-left-line mr-1"></i> 返回组件概览
            </a>
        </div>
    </div>
    
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-500">© 2023 DataScope UI 组件库</p>
                </div>
                <div class="flex space-x-4">
                    <a href="../guides/changelog.html" class="text-gray-500 hover:text-indigo-600">
                        更新日志
                    </a>
                    <a href="../sitemap.html" class="text-gray-500 hover:text-indigo-600">
                        网站地图
                    </a>
                    <a href="https://github.com/yourusername/datascope-ui" target="_blank" class="text-gray-500 hover:text-indigo-600">
                        <i class="ri-github-fill text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>