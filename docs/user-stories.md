# DataScope - 用户故事

## 状态标记说明

- ✅ 已实现
- 🔄 开发中
- 📅 计划中

## 数据源管理

1. **作为**系统管理员，**我希望**能够将新的数据源（MySQL/DB2）添加到系统中，**以便**用户可以访问和查询这些数据源中的数据。 ✅
  - 验收标准：
    - 支持MySQL和DB2数据库类型
    - 能够提供连接详情（主机、端口、凭据等）
    - 安全存储连接凭据并进行加密（使用加盐和AES加密）
    - 保存前提供测试连接功能
    - 可以添加备注说明
    - 设计良好的扩展机制，便于后续添加更多数据源类型

2. **作为**系统管理员，**我希望**查看所有已配置的数据源，**以便**我可以管理和监控它们。 ✅
  - 验收标准：
    - 列表视图显示所有数据源及其关键信息
    - 状态指示器显示连接健康状况
    - 搜索和筛选功能
    - 支持分页和排序

3. **作为**系统管理员，**我希望**编辑或删除现有数据源，**以便**我可以维护准确的连接信息。 ✅
  - 验收标准：
    - 编辑所有连接参数（除数据源类型外）
    - 可以禁用数据源而不删除它
    - 永久删除前需要确认
    - 保留修改历史记录

4. **作为**系统管理员，**我希望**触发数据源的元数据同步，**以便**系统拥有关于模式、表和列的最新信息。 ✅
  - 验收标准：
    - 手动同步触发选项
    - 同步过程中的进度指示器
    - 同步过程中所做更改的详细日志
    - 支持增量更新
    - 记录同步历史
    - 提供元数据差异比较功能

5. **作为**系统管理员，**我希望**安排自动元数据同步，**以便**元数据无需手动干预即可保持最新状态。 🔄
  - 验收标准：
    - 可配置的计划（每日、每周等）
    - 同步失败的电子邮件通知
    - 每个数据源可启用/禁用计划同步的选项
    - 支持自定义同步策略

## 智能数据发现和查询

6. **作为**用户，**我希望**浏览可用的数据源、模式、表和列，**以便**我可以了解有哪些数据可用。 ✅
  - 验收标准：
    - 层次导航界面
    - 跨所有元数据的搜索功能
    - 显示列数据类型和描述
    - 主键和索引的指示
    - 支持表和列的标签和注释

7. **作为**用户，**我希望**针对选定的数据源创建SQL查询，**以便**我可以检索特定数据。 ✅
  - 验收标准：
    - 具有语法高亮的SQL编辑器
    - 模式/表/列自动完成
    - 查询执行并预览结果
    - 执行前的查询验证
    - 30秒后查询超时
    - 支持查询性能优化建议
    - 提供SQL模板库

8. **作为**用户，**我希望**使用自然语言描述我的数据需求，**以便**我可以在不编写SQL的情况下获取结果。 🔄
  - 验收标准：
    - 自然语言输入字段（支持中文）
    - 生成的SQL预览
    - 能够根据结果优化自然语言查询
    - 与LLM服务（OpenRouter）集成
    - 可以调整和优化生成的查询语句
    - 学习用户反馈改进转换质量

9. **作为**用户，**我希望**保存查询以供将来使用，**以便**我不必重新创建它们。 ✅
  - 验收标准：
    - 使用名称和描述保存查询
    - 将查询组织到文件夹/类别中
    - 查询的版本控制
    - 与其他用户共享查询
    - 支持查询模板和参数化

10. **作为**用户，**我希望**查看我的查询历史记录，**以便**我可以参考以前的工作。 ✅
  - 验收标准：
    - 已执行查询的时间顺序列表
    - 按日期范围、数据源、成功/失败筛选
    - 能够重新运行历史查询
    - 保存历史查询的选项
    - 可以标记收藏
    - 支持搜索历史记录
    - 可以复制历史查询
    - 提供执行统计和趋势分析

11. **作为**用户，**我希望**将查询结果下载为CSV，**以便**我可以在其他工具中处理数据。 ✅
  - 验收标准：
    - 查询结果的CSV导出选项
    - 每次下载限制50,000行
    - 大型下载的进度指示器
    - 在CSV格式中正确处理各种数据类型
    - 可以选择导出的列
    - 记录导出历史
    - 支持导出任务的后台处理

12. **作为**用户，**我希望**定义表之间的关系，**以便**系统可以生成更好的查询。 ✅
  - 验收标准：
    - 定义外键关系的界面
    - 已定义关系的可视化表示
    - 编辑或删除关系的能力
    - 关系定义的导入/导出
    - 支持复杂关系类型（多对多等）

13. **作为**用户，**我希望**系统自动建议表之间的关系，**以便**我不必手动定义所有关系。 ✅
  - 验收标准：
    - 基于命名约定自动检测潜在关系
    - 从查询模式中学习以建议关系
    - 建议关系的置信度评分
    - 接受或拒绝建议的选项
    - 保存推断的表关系以便后续直接使用
    - 通过数据分析自动发现非显式关系

14. **作为**用户，**我希望**收藏常用查询，**以便**我可以快速访问它们。 ✅
  - 验收标准：
    - 添加/移除收藏功能
    - 收藏查询的组织和分类
    - 收藏查询的快速访问界面
    - 收藏查询的导出和共享功能
    - 支持收藏夹的个性化排序

## 低代码集成和应用程序开发

15. **作为**开发人员，**我希望**为我的查询创建API端点，**以便**它们可以被其他应用程序使用。 ✅
  - 验收标准：
    - 从保存的查询生成API
    - 动态查询的参数映射
    - API文档生成
    - API的版本控制
    - 速率限制配置
    - 提供RESTful API
    - 支持多种认证方式
    - 提供API使用统计和监控

16. **作为**开发人员，**我希望**配置查询参数和结果的显示方式，**以便**它们能与低代码平台良好集成。 ✅
  - 验收标准：
    - 查询参数的表单配置
    - 结果显示配置（列、格式等）
    - 基于数据类型支持不同的UI组件类型
    - 配置的表单和结果预览
    - 提供标准的集成接口
    - 支持配置的导入导出
    - 可以复用查询模板
    - 支持JSON格式的交互协议

17. **作为**开发人员，**我希望**在查询结果中屏蔽敏感数据，**以便**我可以保护私人信息。 ✅
  - 验收标准：
    - 列级别的屏蔽配置
    - 多种屏蔽模式（部分显示、完全屏蔽等）
    - 所有界面中一致的屏蔽
    - 屏蔽数据访问的审计日志
    - 支持基于角色的屏蔽规则

18. **作为**开发人员，**我希望**系统根据数据类型建议显示配置，**以便**我可以快速创建有效的界面。 🔄
  - 验收标准：
    - 数据类型到UI组件的自动映射
    - 智能字段分组建议
    - 基于字段关系的布局建议
    - 覆盖建议的能力
    - 支持AI辅助的界面优化建议

19. **作为**用户，**我希望**配置显示哪些列以及如何格式化它们，**以便**我可以根据需要自定义视图。 ✅
  - 验收标准：
    - 列可见性切换
    - 列顺序配置
    - 不同数据类型的格式设置
    - 保存个人视图偏好
    - 配置操作列及其功能
    - 可以设置列宽和对齐方式
    - 支持列的排序和筛选
    - 敏感数据的掩码能力

20. **作为**用户，**我希望**配置哪些查询参数是必需的，哪些是可选的，**以便**我可以控制查询行为。 ✅
  - 验收标准：
    - 每个参数的必需/可选设置
    - 默认值配置
    - 参数依赖规则
    - 不常用参数的"更多选项"部分
    - 可以设置字段校验规则
    - 支持条件显示逻辑

21. **作为**用户，**我希望**系统记住我经常使用的查询参数，**以便**常用筛选器易于访问。 🔄
  - 验收标准：
    - 跟踪参数使用频率
    - 自动提升常用参数
    - 用户特定的参数偏好
    - 使用统计重置选项
    - 自动隐藏不经常使用的查询条件
    - 提供"更多条件"选项以显示隐藏的条件
    - 记住常用的查询条件值

22. **作为**用户，**我希望**系统记录我的显示属性设置，**以便**在新建查询界面时智能推断默认的显示属性。 🔄
  - 验收标准：
    - 记录用户对每种数据类型的显示偏好
    - 基于历史设置推荐新查询的显示属性
    - 允许用户覆盖推荐的设置
    - 提供重置为系统默认值的选项
    - 支持导出个人配置
    - 可以导入配置模板
    - 学习用户行为持续优化推荐

23. **作为**用户，**我希望**系统能够根据数据库列数据类型自动选择合适的界面显示元素，**以便**提供一致的用户体验。 ✅
  - 验收标准：
    - 数据库datetime列对应前端时间选择器组件
    - 数字类型对应数字输入框或滑块
    - 枚举类型对应下拉选择框
    - 布尔类型对应复选框
    - 支持自定义映射规则
    - 提供映射预览和测试功能

24. **作为**用户，**我希望**能够以不同的形式查看查询结果，**以便**更好地理解和分析数据。 ✅
  - 验收标准：
    - 表格视图（默认）
    - 图表视图（柱状图、饼图、折线图等）
    - 数据透视表视图
    - 地图视图（对于地理数据）
    - 视图之间的无缝切换
    - 支持视图配置的保存

## 系统管理

25. **作为**系统管理员，**我希望**监控系统性能，**以便**我可以识别和解决瓶颈。 🔄
  - 验收标准：
    - 具有关键性能指标的仪表板
    - 查询执行时间统计
    - 资源利用率图表
    - 性能阈值的警报配置
    - 支持历史性能数据分析

26. **作为**系统管理员，**我希望**设置每个用户的查询速率限制，**以便**单个用户不会使系统过载。 ✅
  - 验收标准：
    - 可配置的查询速率限制
    - 用户特定的限制覆盖
    - 达到限制时的明确反馈
    - 渐进节流而非硬性切断
    - 支持基于时间段的限制策略

27. **作为**系统管理员，**我希望**查看系统活动日志，**以便**我可以排除问题。 ✅
  - 验收标准：
    - 可搜索的日志界面
    - 按日志级别、组件、用户等筛选
    - 导出日志以进行外部分析
    - 保留策略配置
    - 支持实时日志查看

28. **作为**系统管理员，**我希望**设置查询超时时间，**以便**防止长时间运行的查询占用系统资源。 ✅
  - 验收标准：
    - 全局默认超时设置（30秒）
    - 特定数据源或查询的超时覆盖
    - 超时时的优雅处理和通知
    - 超时查询的日志和分析
    - 支持查询熔断机制

## 新增用户故事

29. **作为**系统管理员，**我希望**能够配置和管理LLM服务连接参数，**以便**优化自然语言转SQL功能。 📅
  - 验收标准：
    - 提供界面配置LLM服务的URL、API密钥和模型选择
    - 支持添加多个LLM服务提供商并设置优先级
    - 提供测试LLM连接的功能
    - 记录LLM服务调用日志和性能统计
    - 支持设置请求超时和重试策略
    - 可以为不同类型的查询配置不同的LLM模型

30. **作为**用户，**我希望**能够以图形化方式查看表关系，**以便**更直观地理解数据结构。 📅
  - 验收标准：
    - 提供表关系的图形化视图
    - 支持缩放、平移和调整图形布局
    - 使用不同颜色和线型表示不同类型的关系
    - 支持点击表节点查看表详情
    - 支持筛选和搜索关系图中的表
    - 可以导出关系图为图片或PDF

31. **作为**开发人员，**我希望**能够回滚到页面配置的历史版本，**以便**在配置出错时能够快速恢复。 📅
  - 验收标准：
    - 显示页面配置的完整历史版本列表
    - 提供版本之间的差异比较
    - 支持一键回滚到任意历史版本
    - 回滚操作有确认机制
    - 回滚后创建新版本而非覆盖历史
    - 记录所有回滚操作的审计日志

32. **作为**系统管理员，**我希望**查看慢查询日志和性能瓶颈分析，**以便**针对性地优化系统性能。 📅
  - 验收标准：
    - 自动记录超过阈值的慢查询
    - 提供慢查询的详细信息（SQL、执行时间、频率等）
    - 针对慢查询提供优化建议
    - 支持按数据源、用户、时间段筛选慢查询
    - 提供性能趋势图表
    - 支持设置慢查询警报

33. **作为**系统管理员，**我希望**比较元数据同步前后的差异，**以便**了解数据库结构变化。 📅
  - 验收标准：
    - 显示同步前后新增、修改和删除的表和列
    - 高亮显示数据类型、长度等变更
    - 支持过滤只查看特定类型的变更
    - 提供变更的历史记录
    - 支持导出变更报告
    - 可以针对重要变更设置通知

## 实施进度总结

- **已实现功能 (✅)**: 21个用户故事已完全实现，包括核心的数据源管理、查询执行、低代码集成和表关系管理功能。
- **开发中功能 (🔄)**: 7个用户故事正在开发中，主要包括自然语言查询、AI辅助功能和一些用户体验优化。
- **计划中功能 (📅)**: 5个用户故事处于计划阶段，包括LLM服务管理、关系可视化、配置版本回滚和系统管理增强功能。

总体而言，项目实现进度良好，核心功能已经完成，正在进行高级功能的开发，并计划增加更多增强功能。代码实现与项目进度基本一致，符合预期的开发计划。
