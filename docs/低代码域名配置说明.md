# 低代码域名配置说明

## 问题背景

之前的代码中，低代码预览链接的域名是通过 `import.meta.env.PROD` 来判断的：
- 开发环境（`PROD=false`）：使用 `qaboss.yeepay.com`（测试域名）
- 生产环境（`PROD=true`）：使用 `boss.yeepay.com`（生产域名）

这导致了一个问题：当前端发布到测试环境时，如果使用生产构建模式（`NODE_ENV=production`），会错误地使用生产域名。

## 解决方案

引入新的环境变量 `VITE_LOWCODE_DOMAIN` 来明确指定低代码平台的域名，不再依赖构建环境判断。

### 配置优先级

1. **环境变量优先**：如果设置了 `VITE_LOWCODE_DOMAIN`，则使用该值
2. **构建环境判断**：如果未设置环境变量，则根据 `import.meta.env.PROD` 判断

### 环境配置

#### 开发环境 (.env.development)
```bash
VITE_LOWCODE_DOMAIN=qaboss.yeepay.com
```

#### 测试环境 (.env.test)
```bash
VITE_LOWCODE_DOMAIN=qaboss.yeepay.com
```

#### 生产环境 (.env.production)
```bash
VITE_LOWCODE_DOMAIN=boss.yeepay.com
```

## 部署说明

### 测试环境部署

1. **使用 .env.test 配置文件**：
   ```bash
   # 构建时指定使用测试环境配置
   npm run build -- --mode test
   ```

2. **或者在构建时设置环境变量**：
   ```bash
   # 直接设置环境变量
   VITE_LOWCODE_DOMAIN=qaboss.yeepay.com npm run build
   ```

3. **或者在 CI/CD 中设置**：
   ```yaml
   # 在 GitLab CI 或其他 CI/CD 工具中设置
   variables:
     VITE_LOWCODE_DOMAIN: "qaboss.yeepay.com"
   ```

### 生产环境部署

生产环境可以使用默认的 `.env.production` 配置，或者不设置 `VITE_LOWCODE_DOMAIN`（会自动使用生产域名）。

## 影响的文件

以下文件已更新以支持新的域名配置：

1. `src/utils/config.ts` - 低代码配置和回调URL
2. `src/components/integration/modals/PublishLowcodeModal.vue` - 发布弹窗中的预览链接
3. `src/views/integration/IntegrationList.vue` - 集成列表中的预览功能

## 验证方法

1. **本地验证**：
   - 设置 `VITE_LOWCODE_DOMAIN=qaboss.yeepay.com`
   - 运行 `npm run build`
   - 检查生成的预览链接是否使用测试域名

2. **测试环境验证**：
   - 部署到测试环境后，发布低代码集成
   - 检查生成的预览链接域名是否为 `qaboss.yeepay.com`

3. **生产环境验证**：
   - 部署到生产环境后，发布低代码集成
   - 检查生成的预览链接域名是否为 `boss.yeepay.com`
