<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <meta name="description" content="DataScope 数据视野 - 数据查询和管理平台" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="theme-color" content="#ffffff" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <link rel="icon" type="image/svg+xml" href="/src/assets/images/logo.png" />
    <title>DataScope - 数据视野</title>

    <!-- 解决Vite HMR双重注入问题 -->
    <script>
      // 检查并清除已存在的变量，避免重复声明错误
      if (window.__vite__injectQuery) delete window.__vite__injectQuery;
      if (window.__vite__injectMode) delete window.__vite__injectMode;
      if (window.__vite__baseUrl) delete window.__vite__baseUrl;

      // 主动设置这些变量，避免Vite客户端再次声明它们
      window.__vite__injectQuery = '';
      window.__vite__injectMode = '';
      window.__vite__baseUrl = '';

      // 完全禁用HMR
      window.HMR_WEBSOCKET_CLIENT = { enabled: false };

      // 防止Vite客户端尝试重连
      window.__vite__retry = false;
      window.__vite__connect = false;
    </script>

    <!-- 使用本地Tailwind CSS -->
    <link href="/assets/css/tailwind.min.css" rel="stylesheet" />
    <!-- SQL编辑器修复脚本 - 解决编辑器内容回显问题 -->
    <script src="/assets/js/sql-editor-fix.js"></script>
  </head>
  <body class="bg-gray-100">
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
