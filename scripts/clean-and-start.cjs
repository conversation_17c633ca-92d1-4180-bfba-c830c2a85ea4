#!/usr/bin/env node

/**
 * 开发环境启动辅助脚本
 * 执行清理缓存和端口检查任务，并启动Vite服务器
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 强制释放端口函数
function killPort(port) {
  console.log(`[Vite] 检查端口 ${port} 占用情况`);
  try {
    if (process.platform === 'win32') {
      execSync(`netstat -ano | findstr :${port}`);
      execSync(`taskkill /F /pid $(netstat -ano | findstr :${port} | awk '{print $5}')`, { stdio: 'inherit' });
    } else {
      execSync(`lsof -i :${port} -t | xargs kill -9`, { stdio: 'inherit' });
    }
    console.log(`[Vite] 已释放端口 ${port}`);
  } catch (e) {
    console.log(`[Vite] 端口 ${port} 未被占用或无法释放`);
  }
}

// 强制清理Vite缓存
function cleanViteCache() {
  console.log('[Vite] 清理依赖缓存');
  const cachePaths = [
    './node_modules/.vite',
    './node_modules/.vite_*',
    './.vite',
    './dist',
    './tmp',
    './.temp'
  ];
  
  cachePaths.forEach(cachePath => {
    try {
      if (fs.existsSync(cachePath)) {
        if (fs.lstatSync(cachePath).isDirectory()) {
          execSync(`rm -rf ${cachePath}`);
        } else {
          fs.unlinkSync(cachePath);
        }
        console.log(`[Vite] 已删除: ${cachePath}`);
      }
    } catch (e) {
      console.log(`[Vite] 无法删除: ${cachePath}`, e);
    }
  });
}

// 删除缓存的配置文件
function removeTimestampFiles() {
  console.log('[Vite] 删除时间戳配置文件');
  try {
    const files = fs.readdirSync('.');
    const timestampFiles = files.filter(file => file.startsWith('vite.config.ts.timestamp-'));
    
    timestampFiles.forEach(file => {
      fs.unlinkSync(file);
      console.log(`[Vite] 已删除: ${file}`);
    });
  } catch (e) {
    console.log(`[Vite] 删除时间戳文件失败:`, e);
  }
}

// 获取命令行参数
const args = process.argv.slice(2);
const useMock = args.includes('--mock');
const port = args.includes('--port') ? args[args.indexOf('--port') + 1] : 8082;

// 执行清理任务
console.log('[启动脚本] 开始清理...');
cleanViteCache();
removeTimestampFiles();
killPort(port);

// 设置环境变量
process.env.VITE_USE_MOCK_API = useMock ? 'true' : 'false';
process.env.VITE_API_BASE_URL = '/api';

// 启动命令
const command = `node --inspect ./node_modules/vite/bin/vite.js --config vite.fresh.js --port ${port}`;

// 执行启动命令
console.log('[启动脚本] 准备启动Vite...');
console.log('[启动脚本] Mock模式:', useMock ? '启用' : '禁用');
console.log('[启动脚本] 端口:', port);
console.log('[启动脚本] 执行命令:', command);

try {
  execSync(command, { stdio: 'inherit' });
} catch (error) {
  console.error('[启动脚本] 启动失败:', error);
  process.exit(1);
}