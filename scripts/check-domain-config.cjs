#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('=== 域名配置检查工具 ===\n');

// 读取环境配置文件
function readEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    return env;
  } catch (error) {
    return null;
  }
}

// 检查环境配置
function checkEnvironment(envName, envFile) {
  console.log(`📋 检查 ${envName} 环境配置:`);
  
  const env = readEnvFile(envFile);
  if (!env) {
    console.log(`  ❌ 无法读取 ${envFile}`);
    return false;
  }
  
  const domain = env.VITE_LOWCODE_DOMAIN;
  if (!domain) {
    console.log(`  ⚠️  未设置 VITE_LOWCODE_DOMAIN，将使用默认逻辑`);
    console.log(`  📝 建议设置: VITE_LOWCODE_DOMAIN=${envName.includes('生产') ? 'boss.yeepay.com' : 'qaboss.yeepay.com'}`);
    return true;
  }
  
  // 验证域名格式
  const expectedDomains = ['boss.yeepay.com', 'qaboss.yeepay.com'];
  if (expectedDomains.includes(domain)) {
    console.log(`  ✅ VITE_LOWCODE_DOMAIN = ${domain}`);
    return true;
  } else {
    console.log(`  ❌ VITE_LOWCODE_DOMAIN = ${domain} (不是预期的域名)`);
    return false;
  }
}

// 检查代码文件中的域名配置
function checkCodeFiles() {
  console.log('\n🔍 检查代码文件中的域名配置:');
  
  const filesToCheck = [
    'src/utils/config.ts',
    'src/components/integration/modals/PublishLowcodeModal.vue',
    'src/views/integration/IntegrationList.vue'
  ];
  
  let allGood = true;
  
  filesToCheck.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查是否使用了新的环境变量
      if (content.includes('VITE_LOWCODE_DOMAIN')) {
        console.log(`  ✅ ${file} - 已更新使用 VITE_LOWCODE_DOMAIN`);
      } else {
        console.log(`  ❌ ${file} - 未使用 VITE_LOWCODE_DOMAIN`);
        allGood = false;
      }
      
      // 检查是否还有硬编码的域名判断
      const hardcodedPattern = /import\.meta\.env\.PROD\s*\?\s*['"]boss\.yeepay\.com['"].*['"]qaboss\.yeepay\.com['"]/;
      if (hardcodedPattern.test(content) && !content.includes('VITE_LOWCODE_DOMAIN')) {
        console.log(`  ⚠️  ${file} - 仍有硬编码的域名判断`);
      }
      
    } catch (error) {
      console.log(`  ❌ 无法读取 ${file}`);
      allGood = false;
    }
  });
  
  return allGood;
}

// 检查构建脚本
function checkBuildScripts() {
  console.log('\n🔧 检查构建脚本:');
  
  // 检查 package.json 中的构建脚本
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts;
    
    if (scripts['build:test']) {
      console.log('  ✅ 存在 build:test 脚本');
    } else {
      console.log('  ❌ 缺少 build:test 脚本');
    }
    
    if (scripts['build:stable']) {
      console.log('  ✅ 存在 build:stable 脚本');
    } else {
      console.log('  ❌ 缺少 build:stable 脚本');
    }
  } catch (error) {
    console.log('  ❌ 无法读取 package.json');
  }
  
  // 检查构建脚本文件
  if (fs.existsSync('build.sh')) {
    console.log('  ✅ 存在 build.sh 脚本');
  } else {
    console.log('  ❌ 缺少 build.sh 脚本');
  }
  
  if (fs.existsSync('build-qa.sh')) {
    console.log('  ✅ 存在 build-qa.sh 脚本（QA专用）');
  } else {
    console.log('  ⚠️  缺少 build-qa.sh 脚本（QA专用）');
  }
}

// 提供解决方案建议
function provideSolutions() {
  console.log('\n💡 解决方案建议:');
  console.log('');
  console.log('1. 🏠 本地开发环境:');
  console.log('   - 使用 .env.development 配置');
  console.log('   - 设置 VITE_LOWCODE_DOMAIN=qaboss.yeepay.com');
  console.log('   - 运行: npm run dev');
  console.log('');
  console.log('2. 🧪 QA测试环境:');
  console.log('   - 使用 .env.test 配置');
  console.log('   - 设置 VITE_LOWCODE_DOMAIN=qaboss.yeepay.com');
  console.log('   - 构建: npm run build:test 或使用 build-qa.sh');
  console.log('   - 或设置环境变量: BUILD_ENV=test');
  console.log('');
  console.log('3. 🚀 生产环境:');
  console.log('   - 使用 .env.production 配置');
  console.log('   - 设置 VITE_LOWCODE_DOMAIN=boss.yeepay.com');
  console.log('   - 构建: npm run build:stable');
  console.log('');
  console.log('4. 🔧 CI/CD 配置建议:');
  console.log('   - QA环境: 使用 build-qa.sh 或设置 BUILD_ENV=test');
  console.log('   - 生产环境: 使用 build.sh 或设置 BUILD_ENV=production');
}

// 主函数
function main() {
  const environments = [
    ['开发环境', '.env.development'],
    ['测试环境', '.env.test'],
    ['生产环境', '.env.production']
  ];
  
  let allEnvGood = true;
  environments.forEach(([name, file]) => {
    const result = checkEnvironment(name, file);
    if (!result) allEnvGood = false;
    console.log('');
  });
  
  const codeGood = checkCodeFiles();
  checkBuildScripts();
  
  console.log('\n📊 检查结果总结:');
  if (allEnvGood && codeGood) {
    console.log('✅ 所有配置检查通过！');
  } else {
    console.log('❌ 发现配置问题，请参考下面的解决方案');
  }
  
  provideSolutions();
}

main(); 