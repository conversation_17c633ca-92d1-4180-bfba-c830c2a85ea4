#!/usr/bin/env node

/**
 * 验证域名配置的脚本
 * 用于检查不同环境下的域名配置是否正确
 */

import fs from 'fs';
import path from 'path';

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 读取环境文件
function readEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, value] = line.split('=');
        if (key && value) {
          env[key.trim()] = value.trim();
        }
      }
    });
    
    return env;
  } catch (error) {
    return null;
  }
}

// 验证环境配置
function verifyEnvironment(envName, envFile) {
  log(`\n检查 ${envName} 环境配置:`, 'blue');
  
  const env = readEnvFile(envFile);
  if (!env) {
    log(`  ❌ 无法读取 ${envFile}`, 'red');
    return false;
  }
  
  const domain = env.VITE_LOWCODE_DOMAIN;
  if (!domain) {
    log(`  ⚠️  未设置 VITE_LOWCODE_DOMAIN，将使用默认逻辑`, 'yellow');
    return true;
  }
  
  // 验证域名格式
  const expectedDomains = ['boss.yeepay.com', 'qaboss.yeepay.com'];
  if (expectedDomains.includes(domain)) {
    log(`  ✅ VITE_LOWCODE_DOMAIN = ${domain}`, 'green');
    return true;
  } else {
    log(`  ❌ VITE_LOWCODE_DOMAIN = ${domain} (不是预期的域名)`, 'red');
    return false;
  }
}

// 检查代码文件中的域名配置
function checkCodeFiles() {
  log('\n检查代码文件中的域名配置:', 'blue');
  
  const filesToCheck = [
    'src/utils/config.ts',
    'src/components/integration/modals/PublishLowcodeModal.vue',
    'src/views/integration/IntegrationList.vue'
  ];
  
  let allGood = true;
  
  filesToCheck.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查是否使用了新的环境变量
      if (content.includes('VITE_LOWCODE_DOMAIN')) {
        log(`  ✅ ${file} - 已更新使用 VITE_LOWCODE_DOMAIN`, 'green');
      } else {
        log(`  ❌ ${file} - 未使用 VITE_LOWCODE_DOMAIN`, 'red');
        allGood = false;
      }
      
      // 检查是否还有硬编码的域名判断
      const hardcodedPattern = /import\.meta\.env\.PROD\s*\?\s*['"]boss\.yeepay\.com['"].*['"]qaboss\.yeepay\.com['"]/;
      if (hardcodedPattern.test(content) && !content.includes('VITE_LOWCODE_DOMAIN')) {
        log(`  ⚠️  ${file} - 仍有硬编码的域名判断`, 'yellow');
      }
      
    } catch (error) {
      log(`  ❌ 无法读取 ${file}`, 'red');
      allGood = false;
    }
  });
  
  return allGood;
}

// 主函数
function main() {
  log('🔍 验证低代码域名配置', 'blue');
  log('='.repeat(50), 'blue');
  
  let allPassed = true;
  
  // 检查环境文件
  const environments = [
    ['开发环境', '.env.development'],
    ['测试环境', '.env.test'],
    ['生产环境', '.env.production']
  ];
  
  environments.forEach(([name, file]) => {
    const result = verifyEnvironment(name, file);
    if (!result) allPassed = false;
  });
  
  // 检查代码文件
  const codeResult = checkCodeFiles();
  if (!codeResult) allPassed = false;
  
  // 输出总结
  log('\n' + '='.repeat(50), 'blue');
  if (allPassed) {
    log('✅ 所有检查通过！域名配置正确。', 'green');
    log('\n部署建议:', 'blue');
    log('  • 测试环境: npm run build:test', 'yellow');
    log('  • 生产环境: npm run build', 'yellow');
  } else {
    log('❌ 发现问题，请检查上述错误。', 'red');
    process.exit(1);
  }
}

// 运行验证
main();
