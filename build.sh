#!/bin/bash

# 检查环境变量，决定使用哪种构建模式
if [ "$BUILD_ENV" = "test" ] || [ "$NODE_ENV" = "test" ]; then
    echo "使用测试环境构建模式"
    pnpm install --no-frozen-lockfile
    pnpm run build:test
elif [ "$BUILD_ENV" = "production" ] || [ "$NODE_ENV" = "production" ]; then
    echo "使用生产环境构建模式"
    pnpm install --no-frozen-lockfile
    pnpm run build:stable
else
    echo "使用默认构建模式（生产）"
    pnpm install --no-frozen-lockfile
    pnpm run build
fi
