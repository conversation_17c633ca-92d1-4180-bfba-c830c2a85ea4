import { describe, it, expect, vi, beforeEach } from 'vitest'
import { dataSourceService } from '@/services/datasource'
import type { UpdateDataSourceParams, DataSource } from '@/types/datasource'

// Mock axios instance
vi.mock('@/utils/axios', () => ({
  default: {
    put: vi.fn(),
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn()
  }
}))

// Mock API utils
vi.mock('@/utils/api', () => ({
  getDataSourceApiUrl: vi.fn((action: string) => {
    const urls = {
      update: '/api/datasources/{id}',
      list: '/api/datasources',
      create: '/api/datasources'
    }
    return urls[action as keyof typeof urls] || '/api/datasources'
  })
}))

describe('数据源类型更新功能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确发送type字段到后端', async () => {
    // 准备测试数据
    const updateParams: UpdateDataSourceParams = {
      id: 'test-datasource-id',
      name: '测试数据源',
      description: '测试描述',
      type: 'db2',  // 从mysql改为db2
      host: 'localhost',
      port: 50000,
      databaseName: 'test_db',
      username: 'test_user',
      syncFrequency: 'daily'
    }

    // Mock axios response
    const mockResponse = {
      data: {
        success: true,
        data: {
          id: 'test-datasource-id',
          name: '测试数据源',
          description: '测试描述',
          type: 'db2',
          host: 'localhost',
          port: 50000,
          databaseName: 'test_db',
          username: 'test_user',
          syncFrequency: 'daily',
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      }
    }

    const axiosInstance = await import('@/utils/axios')
    vi.mocked(axiosInstance.default.put).mockResolvedValue(mockResponse)

    // 执行更新
    const result = await dataSourceService.updateDataSource(updateParams)

    // 验证axios.put被正确调用
    expect(axiosInstance.default.put).toHaveBeenCalledWith(
      '/api/datasources/test-datasource-id',
      expect.objectContaining({
        id: 'test-datasource-id',
        name: '测试数据源',
        description: '测试描述',
        type: 'db2',  // 验证type字段被正确发送
        host: 'localhost',
        port: 50000,
        databaseName: 'test_db',
        username: 'test_user',
        syncFrequency: 'daily'
      })
    )

    // 验证返回结果
    expect(result).toBeDefined()
    expect(result.id).toBe('test-datasource-id')
    expect(result.type).toBe('db2')
  })

  it('应该正确处理只更新type字段的情况', async () => {
    // 准备测试数据 - 只更新type字段
    const updateParams: UpdateDataSourceParams = {
      id: 'test-datasource-id',
      type: 'postgresql'  // 只更新类型
    }

    // Mock axios response
    const mockResponse = {
      data: {
        success: true,
        data: {
          id: 'test-datasource-id',
          name: '原有数据源名称',
          description: '原有描述',
          type: 'postgresql',  // 类型已更新
          host: 'localhost',
          port: 3306,
          databaseName: 'original_db',
          username: 'original_user',
          syncFrequency: 'manual',
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      }
    }

    const axiosInstance = await import('@/utils/axios')
    vi.mocked(axiosInstance.default.put).mockResolvedValue(mockResponse)

    // 执行更新
    const result = await dataSourceService.updateDataSource(updateParams)

    // 验证axios.put被正确调用，只包含id和type字段
    expect(axiosInstance.default.put).toHaveBeenCalledWith(
      '/api/datasources/test-datasource-id',
      expect.objectContaining({
        id: 'test-datasource-id',
        type: 'postgresql'
      })
    )

    // 验证返回结果
    expect(result).toBeDefined()
    expect(result.id).toBe('test-datasource-id')
    expect(result.type).toBe('postgresql')
  })

  it('应该正确过滤undefined和null值', async () => {
    // 准备测试数据 - 包含undefined和null值
    const updateParams: UpdateDataSourceParams = {
      id: 'test-datasource-id',
      name: '测试数据源',
      description: undefined,  // undefined值应该被过滤
      type: 'mysql',
      host: null as any,  // null值应该被过滤
      port: 3306,
      databaseName: 'test_db'
    }

    // Mock axios response
    const mockResponse = {
      data: {
        success: true,
        data: {
          id: 'test-datasource-id',
          name: '测试数据源',
          type: 'mysql',
          port: 3306,
          databaseName: 'test_db',
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      }
    }

    const axiosInstance = await import('@/utils/axios')
    vi.mocked(axiosInstance.default.put).mockResolvedValue(mockResponse)

    // 执行更新
    await dataSourceService.updateDataSource(updateParams)

    // 验证axios.put被调用时，undefined和null值被过滤掉
    const callArgs = vi.mocked(axiosInstance.default.put).mock.calls[0][1]
    expect(callArgs).not.toHaveProperty('description')  // undefined值被过滤
    expect(callArgs).not.toHaveProperty('host')  // null值被过滤
    expect(callArgs).toHaveProperty('id')
    expect(callArgs).toHaveProperty('name')
    expect(callArgs).toHaveProperty('type')
    expect(callArgs).toHaveProperty('port')
    expect(callArgs).toHaveProperty('databaseName')
  })

  it('应该正确处理connectionParams对象序列化', async () => {
    // 准备测试数据 - 包含connectionParams对象
    const updateParams: UpdateDataSourceParams = {
      id: 'test-datasource-id',
      name: '测试数据源',
      type: 'mysql',
      connectionParams: {
        connectionTimeout: '30',
        maxPoolSize: '10',
        useSSL: 'true'
      }
    }

    // Mock axios response
    const mockResponse = {
      data: {
        success: true,
        data: {
          id: 'test-datasource-id',
          name: '测试数据源',
          type: 'mysql',
          status: 'active',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      }
    }

    const axiosInstance = await import('@/utils/axios')
    vi.mocked(axiosInstance.default.put).mockResolvedValue(mockResponse)

    // 执行更新
    await dataSourceService.updateDataSource(updateParams)

    // 验证connectionParams被正确序列化为JSON字符串
    const callArgs = vi.mocked(axiosInstance.default.put).mock.calls[0][1]
    expect(callArgs.connectionParams).toBe(JSON.stringify({
      connectionTimeout: '30',
      maxPoolSize: '10',
      useSSL: 'true'
    }))
  })

  it('应该正确处理错误响应', async () => {
    // 准备测试数据
    const updateParams: UpdateDataSourceParams = {
      id: 'test-datasource-id',
      type: 'mysql'
    }

    // Mock axios error response
    const mockErrorResponse = {
      data: {
        success: false,
        message: '数据源不存在'
      }
    }

    const axiosInstance = await import('@/utils/axios')
    vi.mocked(axiosInstance.default.put).mockResolvedValue(mockErrorResponse)

    // 执行更新并期望抛出错误
    await expect(dataSourceService.updateDataSource(updateParams)).rejects.toThrow('数据源不存在')
  })

  it('应该正确处理网络错误', async () => {
    // 准备测试数据
    const updateParams: UpdateDataSourceParams = {
      id: 'test-datasource-id',
      type: 'mysql'
    }

    // Mock axios network error
    const axiosInstance = await import('@/utils/axios')
    vi.mocked(axiosInstance.default.put).mockRejectedValue(new Error('网络错误'))

    // 执行更新并期望抛出错误
    await expect(dataSourceService.updateDataSource(updateParams)).rejects.toThrow('网络错误')
  })
})
