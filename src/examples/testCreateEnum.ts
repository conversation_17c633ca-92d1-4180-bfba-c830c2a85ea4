import axios from 'axios';
import instance from "@/utils/axios";

// 低代码平台认证Token - 定期更新
const LOWCODE_AUTH_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJsb2dpbl90eXBlIjoiQUNDT1VOVCIsIm1vYmlsZSI6IjEzODEwMjg2OTM1IiwibWlncmF0ZV91c2VyX2lkIjoiMWIwNGQyNWEtNDZjOS00MjIwLTljMTQtNzMxN2M0ODNjYWJlIiwieC1pcCI6IjE3Mi4yNS4yNS4xMCIsInByaW5jaXBhbF9pZCI6IjMyNjU3IiwidG9rZW4iOiJmNzUzMDM2Zi04ODFhLTQ3YTUtYTdkNC1lODlmYzMxYTY3OTIiLCJsb2dpbl9uYW1lIjoiZ3VvY2hhby5zdW4iLCJ0d29fZmFjdG9yX3ZhbGlkIjpmYWxzZSwibG9naW5fdGltZSI6IjIwMjUtMDQtMTEgMTI6MzQ6MTMiLCJzY29wZSI6IiIsImNhbGxiYWNrIjoiaHR0cHM6Ly9xYWJvc3MueWVlcGF5LmNvbS9ib3NzLXllZXdvcmtzL2luZGV4Lmh0bWwiLCJzc290aWNrZXQiOiIzODIwODM1Zi1iYmQ4LTQ5ZGYtOGRlNy1jNTVlZGY5MGYyMGMiLCJleHAiOjE3NDQ0MzI0NTMsImlhdCI6MTc0NDM0NDI1MywiZW1haWwiOiJndW9jaGFvLnN1bkB5ZWVwYXkuY29tIiwidXNlcm5hbWUiOiLlrZnlm73otoUifQ.KDuUY7OPCmhzmB0dKY-9ZR_CIjYQ-woulnjVDv6lNLq7GGgAf0_s_BSWjpYWMSEqN2dhfmMDJsY53JCdcupVmw';

// 低代码平台请求头
const getLowcodeRequestHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${LOWCODE_AUTH_TOKEN}`,
    'accept': 'application/json, text/plain, */*',
    'systemcode': 'pmc-boss',
    'origin': 'https://qaboss.yeepay.com',
    'referer': 'https://qaboss.yeepay.com/low-code-editor/2_18_3/editor/23a615edb8aa46ad8a0065fe2da2bdca'
  };
};

/**
 * 直接通过API创建枚举值
 */
async function createEnum() {
  try {
    // 组装请求数据
    const enumData = {
      "projectCode": "APass-Test",
      "name": "TestEnum-" + new Date().getTime(),
      "code": "TEST_ENUM_" + new Date().getTime(),
      "content": [
        {
          "key": "option1",
          "value": "选项一",
          "hide": false
        },
        {
          "key": "option2",
          "value": "选项二",
          "hide": false
        },
        {
          "key": "option3",
          "value": "选项三",
          "hide": false
        }
      ]
    };

    // 发送请求
    const response = await instance.post(
      'https://qaboss.yeepay.com/yeeworks/modeling/rest/enumeration/create',
      enumData,
      { headers: getLowcodeRequestHeaders() }
    );

    // 输出结果
    console.log('创建枚举成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('创建枚举失败:', error);
    throw error;
  }
}

/**
 * 查询枚举列表
 */
async function listEnums(projectCode = 'APass-Test') {
  try {
    // 发送请求
    const response = await instance.get(
      `https://qaboss.yeepay.com/yeeworks/modeling/rest/enumeration/list?page=1&size=10&projectCode=${projectCode}`,
      { headers: getLowcodeRequestHeaders() }
    );

    // 输出结果
    console.log('查询枚举列表成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('查询枚举列表失败:', error);
    throw error;
  }
}

/**
 * 根据ID获取枚举详情
 */
async function getEnum(id: string, projectCode: string = 'APass-Test') {
  try {
    // 发送请求
    const response = await instance.get(
      `https://qaboss.yeepay.com/yeeworks/modeling/rest/enumeration/get?id=${id}&projectCode=${projectCode}`,
      { headers: getLowcodeRequestHeaders() }
    );

    // 输出结果
    console.log('获取枚举详情成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取枚举详情失败:', error);
    throw error;
  }
}

/**
 * 删除枚举
 */
async function deleteEnum(id: string, projectCode: string = 'APass-Test') {
  try {
    // 发送请求
    const response = await instance.post(
      `https://qaboss.yeepay.com/yeeworks/modeling/rest/enumeration/delete?id=${id}&projectCode=${projectCode}`,
      null,
      { headers: getLowcodeRequestHeaders() }
    );

    // 输出结果
    console.log('删除枚举成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('删除枚举失败:', error);
    throw error;
  }
}

// 执行测试
export async function runTest() {
  console.log('开始测试枚举API...');

  try {
    // 创建枚举
    const createdEnum = await createEnum();
    console.log('创建的枚举ID:', createdEnum.id);

    if (createdEnum.id) {
      // 获取枚举详情
      await getEnum(createdEnum.id, createdEnum.projectCode);

      // 可选：删除枚举
      // await deleteEnum(createdEnum.id, createdEnum.projectCode);
    }

    // 查询枚举列表
    await listEnums();

    console.log('测试完成!');
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 如果直接运行脚本，则执行测试
if (typeof require !== 'undefined' && require.main === module) {
  runTest();
}
