<template>
  <div class="p-6 max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">枚举标记功能测试</h1>
      
      <div class="space-y-6">
        <!-- 测试说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 class="text-lg font-semibold text-blue-900 mb-2">测试步骤</h2>
          <ol class="list-decimal list-inside text-blue-800 space-y-1">
            <li>点击"选择枚举"按钮打开枚举选择器</li>
            <li>选择一个枚举（如"银行卡类型"）</li>
            <li>再次点击"选择枚举"按钮</li>
            <li>查看之前选择的枚举是否显示"当前使用"标记</li>
          </ol>
        </div>

        <!-- 当前选择的枚举显示 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-lg font-medium text-gray-900 mb-3">当前选择的枚举</h3>
          <div v-if="currentEnum.enumName" class="space-y-2">
            <div><strong>名称:</strong> {{ currentEnum.enumName }}</div>
            <div><strong>代码:</strong> {{ currentEnum.enumCode }}</div>
            <div><strong>选项数量:</strong> {{ currentEnum.options.length }}</div>
            <div class="mt-3">
              <strong>选项列表:</strong>
              <div class="mt-2 flex flex-wrap gap-2">
                <span v-for="option in currentEnum.options" :key="option.value"
                      class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                  {{ option.label }} ({{ option.value }})
                </span>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-500">
            尚未选择枚举
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-4">
          <button
            @click="openEnumSelector"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            选择枚举
          </button>
          <button
            @click="clearSelection"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            清除选择
          </button>
        </div>
      </div>
    </div>

    <!-- 枚举选择器 -->
    <EnumSelector
      v-if="showEnumSelectorModal"
      :project-code="'DEFAULT'"
      :current-enum-code="currentEnum.enumCode || ''"
      @select="handleEnumSelect"
      @close="showEnumSelectorModal = false"
      @create-new="handleCreateNew"
      @edit="handleEdit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import EnumSelector from '@/components/integration/queryparams/enum/EnumSelector.vue';
import { message } from '@/services/message';

// 状态管理
const showEnumSelectorModal = ref(false);
const currentEnum = ref({
  enumName: '',
  enumCode: '',
  enumId: '',
  options: [] as Array<{label: string, value: string}>
});

// 打开枚举选择器
const openEnumSelector = () => {
  showEnumSelectorModal.value = true;
};

// 处理枚举选择
const handleEnumSelect = (result: { options: Array<{label: string, value: string}>; enumId: string; enumName: string; enumCode: string }) => {
  currentEnum.value = {
    enumName: result.enumName,
    enumCode: result.enumCode,
    enumId: result.enumId,
    options: result.options
  };
  
  showEnumSelectorModal.value = false;
  
  message.success(`已选择枚举 "${result.enumName}"，包含 ${result.options.length} 个选项`);
};

// 清除选择
const clearSelection = () => {
  currentEnum.value = {
    enumName: '',
    enumCode: '',
    enumId: '',
    options: []
  };
  
  message.info('已清除枚举选择');
};

// 处理创建新枚举
const handleCreateNew = () => {
  message.info('创建新枚举功能');
};

// 处理编辑枚举
const handleEdit = (enumeration: any) => {
  message.info(`编辑枚举: ${enumeration.name}`);
};
</script> 