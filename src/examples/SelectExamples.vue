<template>
  <div class="select-examples p-6">
    <h2 class="text-xl font-semibold mb-4">Select 选择器示例</h2>
    
    <div class="bg-white shadow-md rounded-lg p-6 space-y-6">
      <!-- 基本使用 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">基本使用</h3>
        <div class="max-w-md">
          <a-select
            v-model:value="selectedValue"
            placeholder="请选择"
            style="width: 100%"
            @change="handleChange"
          >
            <a-select-option v-for="item in options" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
          <div class="mt-2 text-sm text-gray-500">
            当前选择值: {{ selectedValue }}
          </div>
        </div>
      </div>
      
      <!-- 禁用状态 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">禁用状态</h3>
        <div class="max-w-md">
          <a-select
            v-model:value="selectedValue"
            disabled
            style="width: 100%"
          >
            <a-select-option v-for="item in options" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>
      
      <!-- 可搜索 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">可搜索</h3>
        <div class="max-w-md">
          <a-select
            v-model:value="searchValue"
            show-search
            placeholder="输入关键词搜索"
            :filter-option="filterOption"
            style="width: 100%"
            @change="handleChange"
          >
            <a-select-option v-for="item in options" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>
      
      <!-- 多选模式 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">多选模式</h3>
        <div class="max-w-md">
          <a-select
            v-model:value="multipleValues"
            mode="multiple"
            placeholder="请选择多个选项"
            style="width: 100%"
            @change="handleMultipleChange"
          >
            <a-select-option v-for="item in options" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
          <div class="mt-2 text-sm text-gray-500">
            当前选择值: {{ multipleValues }}
          </div>
        </div>
      </div>
      
      <!-- 带标签的选择器 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">带标签的选择器</h3>
        <div class="max-w-md">
          <a-select
            v-model:value="tagValue"
            mode="tags"
            placeholder="可输入自定义标签"
            style="width: 100%"
          >
            <a-select-option v-for="item in options" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>
      
      <!-- 自定义下拉菜单 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">自定义下拉菜单</h3>
        <div class="max-w-md">
          <a-select
            v-model:value="selectedValue"
            placeholder="请选择"
            style="width: 100%"
          >
            <template #dropdownRender="{ menuNode: menu }">
              <div>
                <div class="px-3 py-2 text-gray-500 text-xs">自定义头部内容</div>
                <a-divider style="margin: 4px 0" />
                <div>{{ menu }}</div>
                <a-divider style="margin: 4px 0" />
                <div class="px-3 py-2">
                  <a-button type="link" block @click="addCustomOption">
                    <span class="flex items-center">
                      <i class="fas fa-plus mr-1"></i> 添加选项
                    </span>
                  </a-button>
                </div>
              </div>
            </template>
            <a-select-option v-for="item in options" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>
      
      <!-- 不同大小 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-3">不同大小</h3>
        <div class="max-w-md space-y-4">
          <a-select size="large" placeholder="大尺寸" style="width: 100%">
            <a-select-option value="1">选项1</a-select-option>
            <a-select-option value="2">选项2</a-select-option>
          </a-select>
          
          <a-select placeholder="默认尺寸" style="width: 100%">
            <a-select-option value="1">选项1</a-select-option>
            <a-select-option value="2">选项2</a-select-option>
          </a-select>
          
          <a-select size="small" placeholder="小尺寸" style="width: 100%">
            <a-select-option value="1">选项1</a-select-option>
            <a-select-option value="2">选项2</a-select-option>
          </a-select>
        </div>
      </div>
    </div>
    
    <div class="mt-8 bg-white shadow-md rounded-lg p-6">
      <h3 class="text-lg font-medium mb-3">属性和API说明</h3>
      <div class="prose">
        <h4>常用属性</h4>
        <ul>
          <li><code>v-model:value</code>: 选中的值</li>
          <li><code>mode</code>: 'default'(单选, 默认) | 'multiple'(多选) | 'tags'(标签)</li>
          <li><code>placeholder</code>: 占位提示文字</li>
          <li><code>disabled</code>: 禁用状态</li>
          <li><code>show-search</code>: 是否可搜索</li>
          <li><code>filter-option</code>: 自定义搜索过滤函数</li>
          <li><code>allow-clear</code>: 是否允许清除</li>
          <li><code>size</code>: 'large' | 'default' | 'small'</li>
          <li><code>loading</code>: 加载状态</li>
        </ul>
        
        <h4>常用事件</h4>
        <ul>
          <li><code>@change</code>: 选择值变化时触发</li>
          <li><code>@focus</code>: 获取焦点时触发</li>
          <li><code>@blur</code>: 失去焦点时触发</li>
          <li><code>@search</code>: 搜索文本变化时触发</li>
        </ul>
        
        <h4>插槽</h4>
        <ul>
          <li><code>#dropdownRender</code>: 自定义下拉菜单</li>
          <li><code>#suffixIcon</code>: 自定义后缀图标</li>
          <li><code>#notFoundContent</code>: 无数据时显示内容</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 基础选项数据
const options = [
  { value: '1', label: '选项一' },
  { value: '2', label: '选项二' },
  { value: '3', label: '选项三' },
  { value: '4', label: '选项四' },
  { value: '5', label: '选项五' },
];

// 各种选择器的值
const selectedValue = ref('');
const searchValue = ref('');
const multipleValues = ref<string[]>([]);
const tagValue = ref<string[]>([]);

// 处理选择变化
const handleChange = (value: string) => {
  console.log(`选择的值: ${value}`);
};

// 处理多选变化
const handleMultipleChange = (values: string[]) => {
  console.log('选择的多个值:', values);
};

// 自定义筛选函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 添加自定义选项
const addCustomOption = () => {
  const newValue = `${options.length + 1}`;
  options.push({ 
    value: newValue,
    label: `新选项 ${newValue}`
  });
};
</script>

<style scoped>
.select-examples {
  color: #333;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-divider) {
  margin: 4px 0;
}
</style> 