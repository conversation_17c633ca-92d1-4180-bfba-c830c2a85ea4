<template>
  <div class="enum-example p-6">
    <h2 class="text-xl font-semibold mb-4">创建枚举值测试</h2>
    
    <div class="bg-white shadow-md rounded-lg p-6 space-y-4">
      <!-- 基本信息 -->
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">项目代码</label>
          <input 
            v-model="enumeration.projectCode" 
            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            placeholder="项目代码"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">枚举名称</label>
          <input 
            v-model="enumeration.name" 
            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            placeholder="枚举名称"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">枚举编码</label>
          <input 
            v-model="enumeration.code" 
            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            placeholder="枚举编码"
          />
        </div>
      </div>
      
      <!-- 选项列表 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">选项列表</label>
        <div class="space-y-2">
          <div v-for="(option, index) in enumeration.content" :key="index" class="flex items-center space-x-2">
            <input
              v-model="option.key"
              placeholder="选项KEY"
              class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
            <input
              v-model="option.value"
              placeholder="选项值"
              class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
            <button 
              @click="removeOption(index)"
              class="text-red-600 hover:text-red-900"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <button 
            @click="addOption"
            class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
          >
            <i class="fas fa-plus mr-1"></i> 添加选项
          </button>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button 
          @click="resetForm" 
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
        >
          重置
        </button>
        <button 
          @click="createEnum" 
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
          :disabled="isSaving"
        >
          <i v-if="isSaving" class="fas fa-spinner fa-spin mr-1"></i>
          {{ isSaving ? '创建中...' : '创建枚举' }}
        </button>
      </div>
    </div>
    
    <!-- 已创建的枚举值列表 -->
    <div class="mt-8">
      <h3 class="text-lg font-medium mb-4">枚举值列表</h3>
      
      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="flex items-center p-4 border-b border-gray-200">
          <input 
            v-model="searchProjectCode" 
            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block sm:text-sm border-gray-300 rounded-md mr-2"
            placeholder="输入项目代码搜索"
          />
          <button 
            @click="loadEnumerations" 
            class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
          >
            <i class="fas fa-search mr-1"></i> 搜索
          </button>
        </div>
        
        <div v-if="loading" class="flex justify-center items-center p-8">
          <i class="fas fa-spinner fa-spin text-indigo-500 text-2xl"></i>
          <span class="ml-2 text-gray-600">加载中...</span>
        </div>
        
        <div v-else-if="enumerations.length === 0" class="p-8 text-center text-gray-500">
          暂无枚举数据
        </div>
        
        <div v-else>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编码</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目代码</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选项数量</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="enumeration in enumerations" :key="enumeration.id">
                  <td class="px-6 py-4 whitespace-nowrap">{{ enumeration.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ enumeration.code }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ enumeration.projectCode }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ enumeration.content?.length || 0 }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <button 
                      @click="viewEnum(enumeration)" 
                      class="text-indigo-600 hover:text-indigo-900 mr-2"
                    >
                      <i class="fas fa-eye"></i>
                    </button>
                    <button 
                      @click="deleteEnum(enumeration.id, enumeration.projectCode)" 
                      class="text-red-600 hover:text-red-900"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- 分页 -->
          <div class="px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div class="text-sm">
              共 {{ total }} 条记录
            </div>
            <div class="flex space-x-2">
              <button 
                @click="currentPage > 1 && changePage(currentPage - 1)" 
                class="px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
                :disabled="currentPage <= 1"
              >
                上一页
              </button>
              <button 
                @click="currentPage < totalPages && changePage(currentPage + 1)" 
                class="px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
                :disabled="currentPage >= totalPages"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 查看枚举详情对话框 -->
    <div v-if="viewingEnumeration" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">
            枚举详情: {{ viewingEnumeration.name }}
          </h3>
          <button @click="closeViewDialog" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="px-6 py-4 max-h-[60vh] overflow-y-auto">
          <div class="space-y-4">
            <div>
              <p class="text-sm font-medium text-gray-700">项目代码</p>
              <p class="mt-1">{{ viewingEnumeration.projectCode }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">枚举名称</p>
              <p class="mt-1">{{ viewingEnumeration.name }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">枚举编码</p>
              <p class="mt-1">{{ viewingEnumeration.code }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">选项列表</p>
              <div class="mt-1 border border-gray-200 rounded-md overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KEY</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">值</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="option in viewingEnumeration.content" :key="option.key">
                      <td class="px-6 py-4 whitespace-nowrap">{{ option.key }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ option.value }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
          <button 
            @click="closeViewDialog" 
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- API测试按钮 -->
  <EnumTestButton />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { 
  createEnumeration, 
  listEnumerations, 
  getEnumerationById, 
  deleteEnumeration,
  type Enumeration
} from '@/api/enumerationService';
import EnumTestButton from './EnumTestButton.vue';

// 枚举值表单
const enumeration = ref<Enumeration>({
  projectCode: 'APass-Test',
  name: '地区枚举-' + new Date().getTime(),
  code: 'REGION_ENUM_' + new Date().getTime(),
  content: [
    { key: 'BJ', value: '北京', hide: false },
    { key: 'SH', value: '上海', hide: false },
    { key: 'GZ', value: '广州', hide: false }
  ]
});

// 状态
const isSaving = ref(false);
const loading = ref(false);
const enumerations = ref<Enumeration[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchProjectCode = ref('');
const viewingEnumeration = ref<Enumeration | null>(null);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(total.value / pageSize.value);
});

// 添加选项
const addOption = () => {
  enumeration.value.content.push({
    key: '',
    value: '',
    hide: false
  });
};

// 移除选项
const removeOption = (index: number) => {
  enumeration.value.content.splice(index, 1);
};

// 重置表单
const resetForm = () => {
  enumeration.value = {
    projectCode: 'APass-Test',
    name: '新枚举-' + new Date().getTime(),
    code: 'NEW_ENUM_' + new Date().getTime(),
    content: []
  };
};

// 创建枚举
const createEnum = async () => {
  if (enumeration.value.content.length === 0) {
    alert('请至少添加一个选项');
    return;
  }

  try {
    isSaving.value = true;
    await createEnumeration(enumeration.value);
    
    // 创建成功后重置表单
    resetForm();
    
    // 刷新枚举列表
    loadEnumerations();
  } catch (error) {
    console.error('创建枚举失败:', error);
  } finally {
    isSaving.value = false;
  }
};

// 加载枚举列表
const loadEnumerations = async () => {
  loading.value = true;
  try {
    const result = await listEnumerations(
      searchProjectCode.value || undefined,
      currentPage.value,
      pageSize.value
    );
    
    enumerations.value = result.list;
    total.value = result.total;
  } catch (error) {
    console.error('加载枚举列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 切换页码
const changePage = (page: number) => {
  currentPage.value = page;
  loadEnumerations();
};

// 查看枚举详情
const viewEnum = async (enumItem: Enumeration) => {
  try {
    // 如果需要，可以重新获取完整的枚举信息
    if (enumItem.id) {
      const fullEnum = await getEnumerationById(enumItem.id, enumItem.projectCode);
      viewingEnumeration.value = fullEnum;
    } else {
      viewingEnumeration.value = { ...enumItem };
    }
  } catch (error) {
    console.error('获取枚举详情失败:', error);
  }
};

// 关闭详情对话框
const closeViewDialog = () => {
  viewingEnumeration.value = null;
};

// 删除枚举
const deleteEnum = async (id?: string, projectCode?: string) => {
  if (!id) return;
  
  if (!confirm('确认要删除此枚举值吗？')) return;
  
  try {
    // 确保有项目代码，默认使用APass-Test
    const enumProjectCode = projectCode || 'APass-Test';
    const success = await deleteEnumeration(id, enumProjectCode);
    if (success) {
      loadEnumerations();
    }
  } catch (error) {
    console.error('删除枚举失败:', error);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadEnumerations();
});
</script>

<style scoped>
.enum-example {
  max-width: 1200px;
  margin: 0 auto;
}
</style>