<template>
  <div class="enum-selector-example p-6">
    <h2 class="text-xl font-semibold mb-4">枚举选择器示例</h2>
    
    <div class="bg-white shadow-md rounded-lg p-6 space-y-6">
      <!-- 基本使用 -->
      <div>
        <h3 class="text-lg font-medium mb-3">基本使用</h3>
        <div class="max-w-md">
          <EnumSelector
            v-model="selectedEnum"
            :project-code="projectCode"
            enum-code="REGION_ENUM"
            placeholder="请选择地区"
            @change="handleChange"
          />
          <div class="mt-2 text-sm text-gray-500">
            当前选择值: {{ selectedEnum }}
          </div>
        </div>
      </div>
      
      <!-- 没有选项的情况 -->
      <div>
        <h3 class="text-lg font-medium mb-3">不存在的枚举</h3>
        <div class="max-w-md">
          <EnumSelector
            v-model="nonExistEnum"
            :project-code="projectCode"
            enum-code="NON_EXIST_ENUM"
            placeholder="不存在的枚举"
          />
          <div class="mt-2 text-sm text-gray-500">
            此示例使用一个不存在的枚举编码，展示空状态
          </div>
        </div>
      </div>
      
      <!-- 禁用状态 -->
      <div>
        <h3 class="text-lg font-medium mb-3">禁用状态</h3>
        <div class="max-w-md">
          <EnumSelector
            v-model="selectedEnum"
            :project-code="projectCode"
            enum-code="REGION_ENUM"
            placeholder="请选择地区"
            disabled
          />
        </div>
      </div>
      
      <!-- 性能测试 -->
      <div>
        <h3 class="text-lg font-medium mb-3">性能测试</h3>
        <div class="flex space-x-4 mb-4">
          <button 
            @click="loadTimes = 1; runPerformanceTest()"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none"
          >
            单次加载测试
          </button>
          <button 
            @click="loadTimes = 10; runPerformanceTest()"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none"
          >
            10次加载测试
          </button>
          <button 
            @click="clearCache"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none"
          >
            清除缓存
          </button>
        </div>
        <div v-if="performanceResults.length > 0" class="max-w-md">
          <div class="bg-gray-100 p-4 rounded-md">
            <h4 class="font-medium mb-2">测试结果:</h4>
            <div v-for="(result, index) in performanceResults" :key="index" class="mb-1 text-sm">
              <span>次数 #{{ index + 1 }}: </span>
              <span :class="result.fromCache ? 'text-green-600' : 'text-blue-600'">
                {{ result.time }}ms 
                {{ result.fromCache ? '(来自缓存)' : '(API请求)' }}
              </span>
            </div>
            <div class="mt-2 pt-2 border-t border-gray-300">
              <div class="font-medium">平均: {{ averageTime }}ms</div>
              <div>API请求: {{ apiRequests }}次</div>
              <div>缓存命中: {{ cacheHits }}次</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import EnumSelector from '@/components/common/EnumSelector.vue';
import { enumCache } from '@/utils/enumCache';
import { enumServiceConfig } from '@/utils/config';

// 状态
const projectCode = ref(enumServiceConfig.projectCode);
const selectedEnum = ref('');
const nonExistEnum = ref('');
const performanceResults = ref<Array<{time: number, fromCache: boolean}>>([]);
const loadTimes = ref(1);

// 枚举变更事件处理
const handleChange = (value: string) => {
  console.log('枚举值变更:', value);
};

// 性能测试
const runPerformanceTest = async () => {
  performanceResults.value = [];
  
  for (let i = 0; i < loadTimes.value; i++) {
    const startTime = performance.now();
    const fromCache = Boolean(enumCache.get(projectCode.value));
    
    // 创建一个临时的EnumSelector，触发加载
    const testSelector = document.createElement('div');
    document.body.appendChild(testSelector);
    
    // 等待加载完成
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 清理临时元素
    document.body.removeChild(testSelector);
    
    const endTime = performance.now();
    
    performanceResults.value.push({
      time: Math.round(endTime - startTime),
      fromCache
    });
    
    // 添加一点延迟，避免并发请求
    await new Promise(resolve => setTimeout(resolve, 50));
  }
};

// 清除缓存
const clearCache = () => {
  enumCache.clear(projectCode.value);
  console.log('已清除枚举缓存');
};

// 计算指标
const averageTime = computed(() => {
  if (performanceResults.value.length === 0) return 0;
  const total = performanceResults.value.reduce((sum, result) => sum + result.time, 0);
  return Math.round(total / performanceResults.value.length);
});

const apiRequests = computed(() => {
  return performanceResults.value.filter(result => !result.fromCache).length;
});

const cacheHits = computed(() => {
  return performanceResults.value.filter(result => result.fromCache).length;
});
</script>

<style scoped>
.enum-selector-example {
  max-width: 1200px;
  margin: 0 auto;
}
</style>