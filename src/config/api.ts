/**
 * API配置文件
 * 集中管理API相关的所有配置
 */

// 环境变量
const ENV = {
  // API基础URL - 从环境变量读取，默认为相对路径/api
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  
  // 是否使用Mock数据 - 从环境变量读取，默认为false
  USE_MOCK: import.meta.env.VITE_USE_MOCK_API === 'true',
  
  // API请求超时时间(毫秒)
  TIMEOUT: Number(import.meta.env.VITE_API_TIMEOUT) || 30000,
  
  // 开发环境标识
  IS_DEV: import.meta.env.DEV
};

// API路径配置
const API_PATHS = {
  // 数据源API
  DATASOURCE: {
    LIST: '/datasources',
    DETAIL: (id: string) => `/datasources/${id}`,
    CREATE: '/datasources',
    UPDATE: (id: string) => `/datasources/${id}`,
    DELETE: (id: string) => `/datasources/${id}`,
    TEST: (id: string) => `/datasources/${id}/test-connection`,
    TEST_CONNECTION: '/datasources/test-connection',
    SYNC: (id: string) => `/datasources/${id}/sync`,
    SCHEMAS: (id: string) => `/datasources/${id}/schemas`,
    TABLE_METADATA: (id: string, table: string) => `/datasources/${id}/tables/${table}`,
    STATUS: (id: string) => `/datasources/${id}/status`,
  },
  
  // 元数据API
  METADATA: {
    TABLES: (dataSourceId: string) => `/metadata/datasources/${dataSourceId}/tables`,
    TABLE_DETAIL: (dataSourceId: string, tableName: string) => 
      `/metadata/datasources/${dataSourceId}/tables/${tableName}`,
    COLUMNS: (dataSourceId: string, tableName: string) => 
      `/metadata/datasources/${dataSourceId}/tables/${tableName}/columns`,
    RELATIONSHIPS: (dataSourceId: string) => 
      `/metadata/datasources/${dataSourceId}/relationships`,
    SEARCH: (dataSourceId: string) => 
      `/metadata/datasources/${dataSourceId}/search`,
  },
  
  // 查询API
  QUERY: {
    LIST: '/queries',
    DETAIL: (id: string) => `/queries/${id}`,
    CREATE: '/queries',
    UPDATE: (id: string) => `/queries/${id}`,
    DELETE: (id: string) => `/queries/${id}`,
    EXECUTE: (id: string) => `/queries/${id}/execute`,
  },
  
  // 集成API
  INTEGRATION: {
    LIST: '/integrations',
    DETAIL: (id: string) => `/integrations/${id}`,
    CREATE: '/integrations',
    UPDATE: (id: string) => `/integrations/${id}`,
    DELETE: (id: string) => `/integrations/${id}`,
  }
};

// HTTP状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
};

// 导出配置
export const ApiConfig = {
  ENV,
  PATHS: API_PATHS,
  STATUS: HTTP_STATUS,
  
  // 获取完整API URL
  getUrl(path: string): string {
    // 如果已经是完整URL则直接返回
    if (path.startsWith('http')) {
      return path;
    }
    
    // 确保path以/开头
    const apiPath = path.startsWith('/') ? path : `/${path}`;
    
    // 构建完整URL
    const baseUrl = ENV.API_BASE_URL.endsWith('/')
      ? ENV.API_BASE_URL.slice(0, -1)
      : ENV.API_BASE_URL;
      
    return `${baseUrl}${apiPath}`;
  }
};

// 导出调试信息
console.log('[API配置] 环境信息:', {
  baseUrl: ENV.API_BASE_URL,
  useMock: ENV.USE_MOCK,
  timeout: ENV.TIMEOUT,
  isDev: ENV.IS_DEV
});

export default ApiConfig;