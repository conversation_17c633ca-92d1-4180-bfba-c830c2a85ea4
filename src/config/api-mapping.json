{"baseConfig": {"apiBaseUrl": "/api", "mockDataEnabled": false, "autoSwitchToMock": true, "connectionTimeout": 10000, "defaultTimeoutMs": 30000}, "datasource": {"list": "/datasources", "detail": "/datasources/{id}", "create": "/datasources", "update": "/datasources/{id}", "delete": "/datasources/{id}", "test": "/datasources/test-connection", "testExistingConnection": "/datasources/{id}/test-connection", "metadata": "/metadata/datasources/{dataSourceId}", "sync": "/metadata/datasources/{dataSourceId}/sync", "schemas": "/datasources/{id}/schemas", "tables": "/datasources/{id}/tables", "search": "/datasources/search"}, "metadata": {"schemas": "/metadata/datasources/{dataSourceId}/schemas", "tables": "/metadata/schemas/{schemaId}/tables", "columns": "/metadata/tables/{tableId}/columns", "sync": "/metadata/datasources/{dataSourceId}/sync", "table-details": "/metadata/tables/{tableId}/details", "table-data-by-id": "/metadata/tables/{tableId}/datas"}, "query": {"list": "/queries", "detail": "/queries/{id}", "create": "/queries", "update": "/queries/{id}", "delete": "/queries/{id}", "execute-sql": "/queries/execute-sql", "execute-version": "/queries/{id}/versions/{versionId}/execute", "execute-by-id": "/queries/{id}/execute", "cancel": "/queries/{id}/cancel", "history": "/queries/{id}/history", "search": "/queries/search", "favorite": "/queries/{id}/favorite", "favorites": "/queries/favorites", "nl-to-sql": "/queries/nl-to-sql", "nlq-execute": "/natural-language/query", "status": "/queries/{id}/status", "parameters": "/queries/{id}/parameters", "analyze-parameters": "/queries/analyze-parameters", "enable": "/queries/{id}/enable", "disable": "/queries/{id}/disable", "publish-version": "/queries/versions/{id}/publish", "activate-version": "/queries/{id}/versions/{versionId}/activate"}, "version": {"list": "/versions", "detail": "/versions/{id}", "create": "/versions", "update": "/versions/{id}", "delete": "/versions/{id}", "publish": "/versions/{id}/publish", "activate": "/versions/{id}/activate", "deprecate": "/versions/{id}/deprecate"}, "integration": {"list": "/integrations", "detail": "/integrations/{id}", "create": "/integrations", "update": "/integrations/{id}", "delete": "/integrations/{id}", "test": "/integrations/{id}/test", "sync": "/integrations/{id}/sync"}}