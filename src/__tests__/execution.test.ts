import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useQueryStore } from '../stores/query';
import axios from 'axios';

// 模拟axios
vi.mock('axios');

describe('查询执行测试', () => {
  let queryStore;
  
  beforeEach(() => {
    // 重置所有模拟
    vi.resetAllMocks();
    
    // 创建查询存储
    queryStore = useQueryStore();
    
    // 模拟instance.post返回值
    instance.post = vi.fn().mockResolvedValue({
      data: { 
        success: true, 
        data: { 
          rows: [{ id: 1, name: 'test' }], 
          columns: ['id', 'name'],
          rowCount: 1 
        } 
      }
    });
  });

  it('应该使用/api/queries/execute-sql接口执行SQL查询', async () => {
    // 执行查询
    await queryStore.executeQuery({
      dataSourceId: 'testDataSource',
      sql: 'SELECT * FROM test',
      queryType: 'SQL'
    });
    
    // 验证使用了正确的API端点
    expect(instance.post).toHaveBeenCalledWith(
      expect.stringContaining('/api/queries/execute-sql'),
      expect.objectContaining({
        dataSourceId: 'testDataSource',
        sql: 'SELECT * FROM test',
        queryType: 'SQL'
      })
    );
  });

  it('当传入versionId="default"时，仍应使用/api/queries/execute-sql接口', async () => {
    // 设置当前查询
    queryStore.currentQuery = { id: 'testQueryId' };
    
    // 执行查询，指定versionId为default
    await queryStore.executeQuery({
      dataSourceId: 'testDataSource',
      sql: 'SELECT * FROM test',
      queryType: 'SQL',
      versionId: 'default'
    });
    
    // 验证使用了正确的API端点（而不是version endpoint）
    expect(instance.post).toHaveBeenCalledWith(
      expect.stringContaining('/api/queries/execute-sql'),
      expect.objectContaining({
        dataSourceId: 'testDataSource',
        sql: 'SELECT * FROM test',
        queryType: 'SQL'
      })
    );
    
    // 验证没有包含versionId
    expect(instance.post.mock.calls[0][1]).not.toHaveProperty('versionId');
  });

  it('只有指定真实版本ID时才应使用executeQueryVersion接口', async () => {
    // 设置当前查询
    queryStore.currentQuery = { id: 'testQueryId' };
    
    // 模拟executeQueryVersion方法
    queryStore.executeQueryVersion = vi.fn().mockResolvedValue({
      rows: [{ id: 1, name: 'version test' }],
      columns: [{ field: 'id', label: 'ID', type: 'number' }, { field: 'name', label: 'Name', type: 'string' }]
    });
    
    // 执行查询，指定真实versionId
    await queryStore.executeQuery({
      dataSourceId: 'testDataSource',
      sql: 'SELECT * FROM test',
      queryType: 'SQL',
      versionId: 'v1'
    });
    
    // 验证调用了executeQueryVersion方法
    expect(queryStore.executeQueryVersion).toHaveBeenCalledWith(
      'testQueryId', 
      'v1',
      expect.objectContaining({
        parameters: {}
      })
    );
  });
}); 
