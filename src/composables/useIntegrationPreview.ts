import { ref, computed, ComputedRef, Ref } from 'vue';
import { useRoute } from 'vue-router';
import { useIntegrationStore } from '@/stores/integration';
import { message } from '@/services/message';
import { formatDate } from '@/utils/formatter';
import type { Integration, ChartType } from '@/types/integration';

// 延迟函数
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export function useIntegrationPreview(integrationId: ComputedRef<string>) {
  const route = useRoute();
  const integrationStore = useIntegrationStore();
  // 使用统一的消息服务
  
  // 状态
  const integration = ref<Integration | null>(null);
  const formValues = ref<Record<string, any>>({});
  const tableData = ref<any[]>([]);
  const tableLoading = ref(false);
  const chartData = ref<any[]>([]);
  const chartLoading = ref(false);
  const queryError = ref<string | null>(null);
  const isLoading = ref(false);

  // 集成类型
  const integrationType = computed(() => integration.value?.type || '');
  
  // 集成数据
  const integrationData = computed(() => {
    if (!integration.value) return [];
    
    if (integrationType.value === 'TABLE' || integrationType.value === 'SIMPLE_TABLE') {
      return tableData.value;
    } else if (integrationType.value === 'CHART') {
      return chartData.value;
    }
    return [];
  });

  // 计算查询条件列表
  const queryConditions = computed(() => {
    if (!integration.value) return [];
    
    // 如果存在集成配置中的表单条件，优先使用
    if (integration.value.formConfig?.conditions?.length) {
      return integration.value.formConfig.conditions
        .filter(condition => condition.visibility !== 'hidden')
        .map(condition => ({
          name: condition.field,
          type: condition.type,
          format: condition.type === 'SELECT' ? 'enum' : condition.type.toLowerCase(),
          formType: mapFormComponentType(condition.type),
          required: condition.required || false,
          defaultValue: condition.defaultValue,
          description: condition.label,
          displayOrder: condition.displayOrder,
          options: condition.componentProps?.options || [],
          advancedConfig: condition.componentProps?.advancedConfig
        }))
        .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
    }
    
    // 其次使用查询参数
    if (integration.value.queryParams?.length) {
      return integration.value.queryParams
        .filter(param => param.description && param.description.trim() !== '')
        .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
    }
    
    return [];
  });

  // 需要显示查询条件的集成类型
  const shouldShowQuery = computed(() => {
    // 优先使用URL查询参数的类型，其次使用集成对象的类型
    const typeParam = Array.isArray(route.query.type) 
      ? route.query.type[0] 
      : route.query.type as string;
    
    const type = typeParam || (integration.value?.type ?? 'TABLE');
    
    // 只有高级表格类型(TABLE)需要显示查询条件
    return type === 'TABLE';
  });

  // 计算表格列
  const tableColumns = computed(() => {
    if (!integration.value || 
        (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
        !integration.value.tableConfig) {
      return [];
    }
    
    return integration.value.tableConfig.columns
      .filter(column => column.visible)
      .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
  });

  // 表格分页配置
  const tablePagination = computed(() => {
    if (!integration.value || 
        (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
        !integration.value.tableConfig) {
      return {
        enabled: true,
        pageSize: 10,
        pageSizeOptions: [10, 20, 50, 100]
      };
    }
    
    return integration.value.tableConfig.pagination;
  });

  // 表格导出配置
  const tableExport = computed(() => {
    if (!integration.value || 
        (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
        !integration.value.tableConfig) {
      return {
        enabled: true,
        formats: ['CSV', 'EXCEL'],
        maxRows: 1000
      };
    }
    
    return integration.value.tableConfig.export;
  });

  // 图表配置
  const chartConfig = computed(() => {
    if (!integration.value || integration.value.type !== 'CHART' || !integration.value.chartConfig) {
      return {
        type: 'bar' as ChartType,
        title: '默认图表',
        dataMapping: {
          xField: 'category',
          yField: 'value'
        }
      };
    }
    
    return integration.value.chartConfig;
  });

  // 将FormComponentType映射到formType
  const mapFormComponentType = (componentType: string): string => {
    const typeMap: Record<string, string> = {
      'INPUT': 'input',
      'NUMBER': 'number',
      'DATE': 'date',
      'DATETIME': 'datetime',
      'SELECT': 'select',
      'MULTISELECT': 'multiselect',
      'CHECKBOX': 'checkbox',
      'RADIO': 'radio',
      'TEXTAREA': 'textarea'
    };
    return typeMap[componentType] || 'input';
  };

  // 加载集成
  const loadIntegration = async () => {
    try {
      isLoading.value = true;
      
      if (!integrationId.value) {
        message.error({
          content: '缺少集成ID',
          description: '无法加载集成预览，请提供有效的集成ID',
          duration: 5000
        });
        return;
      }
      
      // 获取类型参数(优先顺序: URL路径参数 > URL查询参数 > 默认类型)
      // 1. 检查URL路径参数，如 /preview/id/CHART
      const pathTypeParam = Array.isArray(route.params.type) 
        ? route.params.type[0] 
        : route.params.type as string;
        
      // 2. 检查URL查询参数，如 /preview/id?type=CHART
      const queryTypeParam = Array.isArray(route.query.type) 
        ? route.query.type[0] 
        : route.query.type as string;
        
      // 选择有效的类型参数
      const typeParam = pathTypeParam || queryTypeParam || 'TABLE';
      
      // 确保类型是有效的（TABLE, SIMPLE_TABLE, CHART）
      const validType = ['CHART', 'TABLE', 'SIMPLE_TABLE'].includes(typeParam) 
        ? typeParam as 'CHART' | 'TABLE' | 'SIMPLE_TABLE'
        : 'TABLE';
      
      try {
        const result = await integrationStore.fetchIntegrationById(integrationId.value);
      
        if (result) {
          // 创建集成数据副本
          integration.value = { ...result };
          
          // 如果URL中有指定类型，则优先使用URL中的类型
          if (typeParam && ['CHART', 'TABLE', 'SIMPLE_TABLE'].includes(typeParam)) {
            integration.value.type = typeParam as 'CHART' | 'TABLE' | 'SIMPLE_TABLE';
          }
          
          // 初始化表单值
          if (integration.value.formConfig?.conditions?.length) {
            // 如果有表单配置，优先使用表单配置中的条件
            initFormValuesFromConfig(integration.value.formConfig.conditions);
          } else if (integration.value.queryParams) {
            // 其次使用查询参数
            initFormValues(integration.value.queryParams);
          }
          
          // 根据集成类型决定加载何种数据
          if (integration.value.type === 'CHART') {
            await loadChartData();
          } else {
            await loadTableData();
          }
        }
      } catch (error) {
        console.error('从服务端获取集成数据失败:', error);
        
        // 错误处理和模拟数据生成逻辑...
        createMockIntegration(validType);
      }
    } catch (error) {
      console.error('加载集成数据失败:', error);
      message.error({
        content: '加载集成数据失败',
        description: '无法从服务器获取集成信息，请检查网络连接或稍后重试',
        duration: 5000
      });
    } finally {
      isLoading.value = false;
    }
  };

  // 从表单配置初始化值
  const initFormValuesFromConfig = (conditions: any[]) => {
    formValues.value = {};
    
    if (!conditions) return;
    
    conditions.forEach(condition => {
      if (condition.visibility !== 'hidden') {
        // 根据不同类型设置默认值
        let defaultValue = condition.defaultValue || '';
        
        // 根据组件类型处理默认值
        if (condition.type === 'DATE') {
          // 如果默认值为空，检查是否有设置使用当前日期
          if (!defaultValue && condition.componentProps?.advancedConfig?.useCurrentDate) {
            defaultValue = new Date().toISOString().split('T')[0];
          }
        } else if (condition.type === 'DATETIME') {
          // 处理日期时间类型
          if (!defaultValue && condition.componentProps?.advancedConfig?.useCurrentDate) {
            defaultValue = new Date().toISOString().slice(0, 16);
          }
        } else if (condition.type === 'NUMBER') {
          // 数字类型默认值处理
          defaultValue = defaultValue === '' ? '' : Number(defaultValue);
        } else if (condition.type === 'CHECKBOX' || condition.type === 'BOOLEAN') {
          // 布尔类型转换
          defaultValue = defaultValue === true || defaultValue === 'true';
        }
        
        formValues.value[condition.field] = defaultValue;
      }
    });
  };

  // 从查询参数初始化表单值
  const initFormValues = (params?: any[]) => {
    formValues.value = {};
    
    if (!params) return;
    
    params.forEach(param => {
      // 根据不同类型设置默认值
      let defaultValue = param.defaultValue || '';
      
      // 根据类型处理默认值
      if (param.formType === 'date' || param.type === 'DATE' || param.type === 'date') {
        // 如果默认值为空，且有设置当前日期为默认值的选项
        if (!defaultValue && param.useCurrentDate) {
          defaultValue = new Date().toISOString().split('T')[0];
        }
      } else if (param.formType === 'datetime' || param.type === 'DATETIME' || param.type === 'datetime') {
        // 处理日期时间类型
        if (!defaultValue && param.useCurrentDate) {
          defaultValue = new Date().toISOString().slice(0, 16);
        }
      } else if (param.formType === 'number' || param.type === 'NUMBER' || param.type === 'number') {
        // 数字类型默认值处理
        defaultValue = defaultValue === '' ? '' : Number(defaultValue);
      } else if (param.formType === 'checkbox' || param.formType === 'boolean' || 
                param.type === 'BOOLEAN' || param.type === 'boolean') {
        // 布尔类型转换
        defaultValue = defaultValue === true || defaultValue === 'true';
      }
      
      formValues.value[param.name] = defaultValue;
    });
  };

  // 创建模拟集成数据
  const createMockIntegration = (validType: 'CHART' | 'TABLE' | 'SIMPLE_TABLE') => {
    integration.value = {
      id: route.params.id as string,
      name: '订单系统集成',
      description: '与企业ERP系统的订单数据集成',
      type: validType,
      status: 'ACTIVE',
      queryId: 'query-001',
      // 其他模拟数据...

      // 确保添加的属性符合类型定义
      integrationPoint: {
        id: 'ip-001',
        name: '示例集成点',
        type: 'URL',
        urlConfig: {
          url: '/api/data',
          method: 'GET',
          headers: {}
        }
      },
      
      // 添加模拟表单配置
      formConfig: generateMockFormConfig(),
      
      // 添加查询参数定义
      queryParams: validType === 'TABLE' ? generateMockQueryParams() : [],
      
      // 添加表格配置
      tableConfig: generateMockTableConfig(),
      
      // 图表配置（仅在CHART类型时使用）
      chartConfig: validType === 'CHART' ? generateMockChartConfig() : undefined,
    };
    
    // 初始化表单值
    if (integration.value.formConfig?.conditions?.length) {
      // 如果有表单配置，优先使用表单配置
      initFormValuesFromConfig(integration.value.formConfig.conditions);
    } else if (integration.value.queryParams) {
      // 其次使用查询参数
      initFormValues(integration.value.queryParams);
    }
    
    // 根据集成类型决定加载何种数据
    if (integration.value.type === 'CHART') {
      loadChartData();
    } else {
      loadTableData();
    }
  };

  // 生成模拟表单配置
  const generateMockFormConfig = () => {
    return {
      layout: 'horizontal',
      conditions: [
        // 模拟表单条件...
        // 这里是示例条件，实际使用时需要根据类型定义调整
      ],
      buttons: [
        { type: 'submit', label: '查询', style: 'primary' },
        { type: 'reset', label: '重置', style: 'secondary' }
      ]
    };
  };

  // 生成模拟查询参数
  const generateMockQueryParams = () => {
    return [
      {
        name: 'product',
        type: 'STRING',
        format: 'string',
        formType: 'input',
        required: false,
        defaultValue: '',
        description: '产品名称',
        displayOrder: 1
      },
      {
        name: 'status',
        type: 'SELECT',
        format: 'enum',
        formType: 'select',
        required: false,
        defaultValue: '',
        description: '订单状态',
        displayOrder: 2,
        options: [
          { label: '全部', value: '' },
          { label: '已完成', value: '已完成' },
          { label: '进行中', value: '进行中' },
          { label: '已取消', value: '已取消' }
        ]
      },
      {
        name: 'dateRange',
        type: 'DATE',
        format: 'date',
        formType: 'date',
        required: false,
        defaultValue: '',
        description: '订单日期',
        displayOrder: 3
      }
    ];
  };

  // 生成模拟表格配置
  const generateMockTableConfig = () => {
    return {
      columns: [
        // 表格列定义...
      ],
      actions: [
        { type: 'link', label: '查看', handler: 'viewRecord', icon: 'eye', style: 'primary' },
        { type: 'link', label: '编辑', handler: 'editRecord', icon: 'edit', style: 'secondary' },
        { type: 'link', label: '删除', handler: 'deleteRecord', icon: 'trash', style: 'danger', confirm: true, confirmMessage: '确定要删除此记录吗？' }
      ],
      pagination: {
        enabled: true,
        pageSize: 10,
        pageSizeOptions: [10, 20, 50, 100]
      },
      export: {
        enabled: true,
        formats: ['CSV', 'EXCEL'],
        maxRows: 1000
      },
      batchActions: [],
      aggregation: {
        enabled: false,
        groupByFields: [],
        aggregationFunctions: []
      },
      advancedFilters: {
        enabled: false,
        defaultFilters: [],
        savedFilters: []
      }
    };
  };

  // 生成模拟图表配置
  const generateMockChartConfig = () => {
    return {
      type: 'bar' as ChartType,
      title: '销售分析图表',
      description: '按月度显示销售数据',
      height: 400,
      animation: true,
      showLegend: true,
      dataMapping: {
        xField: 'category',
        yField: 'value',
        seriesField: 'series'
      },
      styleOptions: {
        colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
      }
    };
  };

  // 加载表格数据
  const loadTableData = async () => {
    try {
      tableLoading.value = true;

      // 显示加载状态的延迟
      await sleep(300);
      
      // 确保表格列配置存在
      ensureTableColumnsExist();
      
      // 生成测试数据，确保每次都创建新数据
      tableData.value = generateMockTableData(50);
    } catch (error) {
      console.error('加载表格数据失败:', error);
      message.error({
        content: '加载表格数据失败',
        description: '无法获取表格数据，请检查查询参数或稍后重试',
        duration: 5000
      });
    } finally {
      tableLoading.value = false;
    }
  };

  // 确保表格列配置存在
  const ensureTableColumnsExist = () => {
    if (integration.value && (!integration.value.tableConfig?.columns || integration.value.tableConfig.columns.length === 0)) {
      // 深度克隆现有tableConfig以避免引用问题
      const currentConfig = integration.value.tableConfig ? JSON.parse(JSON.stringify(integration.value.tableConfig)) : {};
      
      // 设置默认列
      integration.value.tableConfig = {
        ...currentConfig,
        columns: [
          // 这里需要添加默认列配置...
          // 注意：根据类型定义调整属性名和类型
        ],
        // 确保其他必需属性存在
        actions: currentConfig.actions || [],
        // 设置其他必需属性...
      };
    }
  };

  // 生成模拟表格数据
  const generateMockTableData = (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      id: i + 1,
      date: formatDate(new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)),
      product: ['笔记本电脑', '智能手机', '平板电脑', '智能手表', '无线耳机'][Math.floor(Math.random() * 5)],
      quantity: Math.floor(Math.random() * 100) + 1,
      amount: (Math.random() * 10000 + 500).toFixed(2),
      status: ['已完成', '进行中', '已取消'][Math.floor(Math.random() * 3)],
      customer: ['张三', '李四', '王五', '赵六', '钱七'][Math.floor(Math.random() * 5)],
      department: ['销售部', '市场部', '技术部', '客服部', '财务部'][Math.floor(Math.random() * 5)]
    }));
  };

  // 生成图表数据
  const loadChartData = async () => {
    try {
      chartLoading.value = true;
      
      // 模拟网络延迟
      await sleep(1000);
      
      if (!integration.value || !integration.value.chartConfig) {
        console.warn('集成或图表配置为空，无法加载图表数据');
        chartLoading.value = false;
        return;
      }
      
      const chartType = integration.value.chartConfig.type || 'bar';
      const dataSize = 10; // 数据点数量
      
      // 生成符合图表需要的数据
      chartData.value = generateMockChartData(chartType, dataSize);
    } catch (error) {
      console.error('加载图表数据失败:', error);
    } finally {
      chartLoading.value = false;
    }
  };

  // 生成模拟图表数据
  const generateMockChartData = (chartType: string, dataSize: number) => {
    if (chartType === 'pie') {
      // 饼图数据
      return Array.from({ length: 5 }, (_, i) => ({
        category: `分类${i + 1}`,
        value: Math.floor(Math.random() * 1000)
      }));
    } else if (chartType === 'line' || chartType === 'bar') {
      // 折线图和柱状图数据
      const categories = Array.from({ length: dataSize }, (_, i) => `类别${i + 1}`);
      const series = ['系列A', '系列B', '系列C'];
      
      const result: any[] = [];
      
      categories.forEach(category => {
        series.forEach(series => {
          result.push({
            category,
            series,
            value: Math.floor(Math.random() * 1000)
          });
        });
      });
      
      return result;
    } else {
      // 其他类型图表
      return Array.from({ length: dataSize }, (_, i) => ({
        x: Math.random() * 100,
        y: Math.random() * 100,
        category: `分类${i % 3 + 1}`
      }));
    }
  };

  // 加载数据
  const loadData = async () => {
    isLoading.value = true;
    queryError.value = null;
    
    try {
      if (integrationType.value === 'TABLE' || integrationType.value === 'SIMPLE_TABLE') {
        await loadTableData();
      } else if (integrationType.value === 'CHART') {
        await loadChartData();
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      queryError.value = '数据加载失败，请稍后重试';
    } finally {
      isLoading.value = false;
    }
  };

  // 重置表单
  const resetForm = () => {
    formValues.value = {};
    loadData();
  };

  return {
    integration,
    formValues,
    tableData,
    chartData,
    isLoading,
    tableLoading,
    chartLoading,
    queryError,
    integrationType,
    integrationData,
    queryConditions,
    shouldShowQuery,
    tableColumns,
    tablePagination,
    tableExport,
    chartConfig,
    loadIntegration,
    loadData,
    resetForm
  };
}