import { message as antMessage } from 'ant-design-vue';

// 统一的消息提示服务
export const message = {
  success: (content: string, duration = 3) => {
    antMessage.success(content, duration);
  },
  error: (content: string, duration = 3) => {
    antMessage.error(content, duration);
  },
  warning: (content: string, duration = 3) => {
    antMessage.warning(content, duration);
  },
  info: (content: string, duration = 3) => {
    antMessage.info(content, duration);
  },
  loading: (content: string, duration = 3) => {
    return antMessage.loading(content, duration);
  }
};

export default message;