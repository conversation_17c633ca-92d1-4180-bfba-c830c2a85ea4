/**
 * 将数据类型转换为参数类型
 * @param type 原始数据类型
 * @returns 转换后的参数类型
 */
export function convertDataTypeToParamType(type: string): string {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();
  
  switch (lowerType) {
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
    case 'number':
      return 'number';
      
    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';
      
    case 'boolean':
    case 'bool':
      return 'boolean';
      
    case 'string':
    case 'varchar':
    case 'char':
    case 'text':
    default:
      return 'string';
  }
}

/**
 * 根据参数类型获取表单类型
 * @param type 参数类型
 * @returns 对应的表单类型
 */
export function getFormTypeFromParamType(type: string): string {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();
  
  switch (lowerType) {
    case 'number':
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
      return 'number';
      
    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';
      
    case 'boolean':
    case 'bool':
      return 'checkbox';
      
    case 'select':
    case 'enum':
      return 'select';
      
    case 'textarea':
    case 'text_area':
      return 'textarea';
      
    default:
      return 'text';
  }
}

/**
 * 根据集成类型获取对应的图标
 * @param type 集成类型
 * @returns 图标类名
 */
export function getIntegrationTypeIcon(type: string, iconMap: Record<string, any>): string {
  return (iconMap[type] || { icon: 'fas fa-cog' }).icon;
}

/**
 * 根据集成类型获取对应的背景样式
 * @param type 集成类型
 * @returns 背景样式类名
 */
export function getIntegrationTypeIconBg(type: string, iconMap: Record<string, any>): string {
  return (iconMap[type] || { bgClass: 'bg-gray-100 text-gray-600' }).bgClass;
}