import { message } from '@/services/message';
import type { DataSource, PageResponse } from '@/types/datasource';

/**
 * API响应统一结构
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    statusCode: number;
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * API响应处理配置选项
 */
export interface ResponseHandlerOptions {
  // 是否显示成功消息
  showSuccessMessage?: boolean;
  // 是否显示错误消息
  showErrorMessage?: boolean;
  // 成功消息内容
  successMessage?: string;
  // 错误消息内容
  errorMessage?: string;
  // 成功消息显示时间(毫秒)
  successDuration?: number;
  // 错误消息显示时间(毫秒)
  errorDuration?: number;
  // 是否允许重复消息
  allowDuplicate?: boolean;
  // 消息唯一标识，用于去重
  messageKey?: string;
}

/**
 * 通用响应处理工具函数
 * 处理API响应并显示相应的消息通知
 * 
 * @param response API响应数据
 * @param options 处理选项
 * @returns 处理后的数据，成功时返回data，失败时抛出异常
 */
export function handleApiResponse<T>(
  response: ApiResponse<T> | any,
  options: ResponseHandlerOptions = {}
): T | null {
  // 设置默认选项
  const {
    showSuccessMessage = false,
    showErrorMessage = true,
    successMessage = '操作成功',
    errorMessage = '操作失败',
    successDuration = 3000,
    errorDuration = 5000,
    allowDuplicate = false,
    messageKey = undefined
  } = options;

  try {
    // 判断是否为标准API响应格式
    if (response && typeof response === 'object' && 'success' in response) {
      if (response.success) {
        // 操作成功
        if (showSuccessMessage) {
          message.success(successMessage, successDuration, allowDuplicate);
        }
        return response.data || null;
      } else {
        // 操作失败
        const errMsg = response.error?.message || errorMessage;
        if (showErrorMessage) {
          message.error(errMsg, errorDuration, allowDuplicate);
        }
        throw new Error(errMsg);
      }
    }
    
    // 非标准格式，假设成功
    if (showSuccessMessage) {
      message.success(successMessage, successDuration, allowDuplicate);
    }
    return response;
  } catch (error) {
    // 处理异常
    const errMsg = error instanceof Error ? error.message : errorMessage;
    if (showErrorMessage) {
      message.error(errMsg, errorDuration, allowDuplicate);
    }
    throw error;
  }
}

/**
 * 创建一个通用的响应处理器
 * 
 * @param defaultOptions 默认处理选项
 * @returns 带有默认配置的响应处理函数
 */
export function createResponseHandler(defaultOptions: ResponseHandlerOptions = {}) {
  return function<T>(response: ApiResponse<T> | any, options: ResponseHandlerOptions = {}) {
    return handleApiResponse<T>(response, { ...defaultOptions, ...options });
  };
}

/**
 * 预配置的响应处理器
 */
export const responseHandler = {
  // 静默处理 - 不显示任何消息
  silent: createResponseHandler({ 
    showSuccessMessage: false, 
    showErrorMessage: false 
  }),
  
  // 只显示错误消息
  errorOnly: createResponseHandler({ 
    showSuccessMessage: false, 
    showErrorMessage: true 
  }),
  
  // 显示所有消息
  all: createResponseHandler({ 
    showSuccessMessage: true, 
    showErrorMessage: true 
  }),

  // 不允许重复消息的处理器
  unique: createResponseHandler({
    showSuccessMessage: true,
    showErrorMessage: true,
    allowDuplicate: false
  }),
  
  // 自定义处理器
  custom: handleApiResponse
};

/**
 * 在Vue组件中使用的响应处理钩子
 * @returns 响应处理函数和工具
 */
export function useResponseHandler() {
  return {
    // 各种预配置的处理器
    handle: responseHandler,
    
    // 创建自定义处理器
    create: createResponseHandler,
    
    // 通用处理函数
    handleResponse: handleApiResponse
  };
}

/**
 * 数据源响应适配器 - 统一处理不同的后端响应格式
 * 
 * @param response 原始API响应
 * @returns 标准化的响应格式
 */
export function adaptDataSourceListResponse(response: any): PageResponse<DataSource> {
  // 默认结果
  const defaultResult: PageResponse<DataSource> = {
    items: [],
    total: 0,
    page: 1,
    size: 10,
    totalPages: 0
  };

  try {
    // 检查响应类型
    if (!response) return defaultResult;

    // 情况1: 标准格式（带items数组和分页信息）
    if (response.items && Array.isArray(response.items)) {
      return {
        items: response.items,
        total: response.total || response.meta?.total || response.items.length,
        page: response.page || response.meta?.page || 1,
        size: response.size || response.meta?.size || 10,
        totalPages: response.totalPages || 
                   response.meta?.totalPages || 
                   Math.ceil((response.total || response.meta?.total || response.items.length) / 
                            (response.size || response.meta?.size || 10))
      };
    }

    // 情况2: 嵌套在data中的标准格式
    if (response.data && response.data.items && Array.isArray(response.data.items)) {
      const data = response.data;
      return {
        items: data.items,
        total: data.total || data.meta?.total || data.items.length,
        page: data.page || data.meta?.page || 1,
        size: data.size || data.meta?.size || 10,
        totalPages: data.totalPages || 
                   data.pages ||  // 添加对pages字段的处理
                   data.meta?.totalPages || 
                   Math.ceil((data.total || data.meta?.total || data.items.length) / 
                            (data.size || data.meta?.size || 10))
      };
    }

    // 情况3: 直接是数组的情况
    if (Array.isArray(response)) {
      return {
        items: response,
        total: response.length,
        page: 1,
        size: response.length,
        totalPages: 1
      };
    }

    // 情况4: 特殊嵌套格式 data.list 或 result.list
    if (response.data && Array.isArray(response.data.list)) {
      return {
        items: response.data.list,
        total: response.data.total || response.data.list.length,
        page: response.data.page || response.data.pageNum || 1,
        size: response.data.size || response.data.pageSize || 10,
        totalPages: response.data.totalPages || 
                  Math.ceil((response.data.total || response.data.list.length) / 
                           (response.data.size || response.data.pageSize || 10))
      };
    }

    if (response.result && Array.isArray(response.result.list)) {
      return {
        items: response.result.list,
        total: response.result.total || response.result.list.length,
        page: response.result.page || response.result.pageNum || 1,
        size: response.result.size || response.result.pageSize || 10,
        totalPages: response.result.totalPages || 
                  Math.ceil((response.result.total || response.result.list.length) / 
                           (response.result.size || response.result.pageSize || 10))
      };
    }

    // 在无法识别的情况下返回默认结果
    console.warn('无法识别的数据源响应格式:', response);
    return defaultResult;
  } catch (error) {
    console.error('处理数据源响应错误:', error);
    return defaultResult;
  }
}

/**
 * 单个数据源响应适配器
 * 
 * @param response 原始API响应
 * @returns 数据源对象
 */
export function adaptDataSourceResponse(response: any): DataSource | null {
  try {
    if (!response) return null;

    // 情况1: 标准响应格式，数据在data字段中
    if (response.data) {
      return response.data;
    }
    
    // 情况2: 直接返回数据源对象
    if (response.id) {
      return response;
    }
    
    // 无法识别的格式
    console.warn('无法识别的数据源详情响应格式:', response);
    return null;
  } catch (error) {
    console.error('处理数据源详情响应错误:', error);
    return null;
  }
}

/**
 * API工具函数
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

interface ApiUrlParams {
  dataSourceId?: string;
  schemaId?: string;
  tableId?: string;
}

/**
 * 获取元数据API URL
 * @param endpoint API端点
 * @param params URL参数
 * @returns 完整的API URL
 */
export const getMetadataApiUrl = (endpoint: string, params: ApiUrlParams = {}): string => {
  const { dataSourceId, schemaId, tableId } = params;
  
  const baseUrl = `${API_BASE_URL}/api/metadata`;
  
  switch (endpoint) {
    case 'datasources':
      return `${baseUrl}/datasources`;
    case 'schemas':
      if (!dataSourceId) throw new Error('dataSourceId is required for schemas endpoint');
      return `${baseUrl}/datasources/${dataSourceId}/schemas`;
    case 'tables':
      if (!dataSourceId) throw new Error('dataSourceId is required for tables endpoint');
      return `${baseUrl}/datasources/${dataSourceId}/tables`;
    case 'columns':
      if (!tableId) throw new Error('tableId is required for columns endpoint');
      return `${baseUrl}/tables/${tableId}/columns`;
    default:
      throw new Error(`Unknown endpoint: ${endpoint}`);
  }
}; 