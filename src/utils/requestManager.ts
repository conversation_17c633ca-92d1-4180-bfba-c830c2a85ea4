/**
 * 简单的请求管理器，用于避免重复请求
 */

interface RequestOptions {
  cacheTime?: number; // 缓存时间（毫秒）
}

interface CacheEntry {
  data: any;
  timestamp: number;
  promise?: Promise<any>;
}

export class SimpleRequestManager {
  private cache = new Map<string, CacheEntry>();
  private pendingRequests = new Map<string, Promise<any>>();

  /**
   * 发起请求，如果有缓存或正在进行的请求则返回缓存/等待结果
   * @param requestFn 请求函数
   * @param cacheKey 缓存键
   * @param params 请求参数（用于生成缓存键）
   * @param options 选项
   */
  async request<T>(
    requestFn: () => Promise<T>,
    cacheKey: string,
    params: Record<string, any> = {},
    options: RequestOptions = {}
  ): Promise<T> {
    const { cacheTime = 30000 } = options; // 默认30秒缓存

    // 生成完整的缓存键
    const fullCacheKey = this.generateCacheKey(cacheKey, params);

    console.log(`🔍 [RequestManager] 请求分析:`, {
      baseKey: cacheKey,
      params,
      fullCacheKey,
      cacheSize: this.cache.size,
      pendingSize: this.pendingRequests.size
    });

    // 检查缓存
    const cached = this.cache.get(fullCacheKey);
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      console.log(`✅ [RequestManager] 使用缓存数据: ${fullCacheKey}`);
      return cached.data;
    }

    // 检查是否有正在进行的请求
    const pendingRequest = this.pendingRequests.get(fullCacheKey);
    if (pendingRequest) {
      console.log(`⏳ [RequestManager] 等待正在进行的请求: ${fullCacheKey}`);
      return pendingRequest;
    }

    // 发起新请求
    console.log(`🚀 [RequestManager] 发起新请求: ${fullCacheKey}`);
    const requestPromise = requestFn().then(
      (result) => {
        console.log(`✅ [RequestManager] 请求完成，缓存结果: ${fullCacheKey}`);
        // 缓存结果
        this.cache.set(fullCacheKey, {
          data: result,
          timestamp: Date.now()
        });

        // 清除pending状态
        this.pendingRequests.delete(fullCacheKey);

        return result;
      },
      (error) => {
        console.log(`❌ [RequestManager] 请求失败: ${fullCacheKey}`, error);
        // 清除pending状态
        this.pendingRequests.delete(fullCacheKey);
        throw error;
      }
    );

    // 记录pending请求
    this.pendingRequests.set(fullCacheKey, requestPromise);

    return requestPromise;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(baseKey: string, params: Record<string, any>): string {
    const paramStr = Object.keys(params)
      .sort((a, b) => a.localeCompare(b))
      .map(key => `${key}=${params[key]}`)
      .join('&');

    return paramStr ? `${baseKey}?${paramStr}` : baseKey;
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache(maxAge: number = 300000): void { // 默认5分钟
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > maxAge) {
        this.cache.delete(key);
      }
    }
  }
}

// 导出默认实例
export const defaultRequestManager = new SimpleRequestManager();

// 导出专门用于API请求的全局实例
export const globalApiRequestManager = new SimpleRequestManager();

// 测试requestManager是否正常工作
console.log('🚀 [RequestManager] 模块已加载，globalApiRequestManager已创建');
