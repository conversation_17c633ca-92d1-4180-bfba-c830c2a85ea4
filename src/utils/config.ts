/**
 * 配置工具函数
 */

// 环境变量
const env = import.meta.env

/**
 * 判断是否启用模拟数据模式
 * @param feature 功能名称，可选
 * @returns 是否启用模拟数据
 */
export function usesMockData(feature?: string): boolean {
  // 全局模拟数据开关
  const globalMock = env.VITE_USE_MOCK_API === 'true'

  // 如果未指定功能，返回全局设置
  if (!feature) {
    return globalMock
  }

  // 功能特定的模拟数据开关
  const featureMock = env[`VITE_USE_MOCK_${feature.toUpperCase()}`] === 'true'

  // 优先使用功能特定设置，如果未设置则使用全局设置
  return featureMock || (env[`VITE_USE_MOCK_${feature.toUpperCase()}`] === undefined && globalMock)
}

/**
 * 获取API基础路径
 * @returns API基础路径
 */
export function getApiBasePath(): string {
  // 从全局变量获取，如果设置了的话
  if ((window as any).__API_BASE_PATH) {
    return (window as any).__API_BASE_PATH
  }

  // 从环境变量获取
  if (env.VITE_API_BASE_URL) {
    return env.VITE_API_BASE_URL
  }

  // 默认路径
  return import.meta.env.DEV
    ? 'https://qaboss.yeepay.com/data-scope/' // 开发环境使用测试域名
    : '/data-scope' // 生产环境相对路径
}

/**
 * 获取数据源API路径
 * @returns 数据源API路径
 */
export function getDataSourceApiPath(): string {
  return `${getApiBasePath()}/api/datasources`
}

/**
 * 获取元数据API路径
 * @returns 元数据API路径
 */
export function getMetadataApiPath(): string {
  return `${getApiBasePath()}/api/metadata`
}

/**
 * 获取低代码平台认证Token
 * @returns 认证Token
 */
export function getLowCodeAuthToken(): string {
  // 从localStorage中获取token
  const token = localStorage.getItem('token');

  // 如果token存在则返回，否则返回空字符串
  return token || '';
}

/**
 * 获取低代码平台域名
 * 优先使用环境变量 VITE_LOWCODE_DOMAIN，如果未设置则根据环境判断
 * @returns 低代码平台域名
 */
function getLowCodeDomain(): string {
  console.log('import.meta.env.MODE', import.meta.env)

  // 如果没有配置环境变量，则根据构建环境判断
  return import.meta.env.MODE === 'development' ? 'https://qaboss.yeepay.com' : window.location.origin;


}

/**
 * 低代码平台相关配置
 */
export const lowCodeConfig = {
  /**
   * 低代码平台Base URL
   * 优先使用环境变量 VITE_LOWCODE_DOMAIN，如果未设置则根据环境判断
   * 生产环境使用 boss.yeepay.com，开发/测试环境使用 qaboss.yeepay.com
   */
  baseUrl: `${getLowCodeDomain()}`,

  /**
   * 低代码平台认证Token
   * 动态从localStorage中获取，不再硬编码
   * @returns 当前有效的认证Token
   */
  get authToken(): string {
    return getLowCodeAuthToken();
  },

  /**
   * 低代码系统代码
   * 用于请求头中的systemcode字段
   */
  systemCode: 'Biz-YeeWorks',

  /**
   * 低代码项目代码
   * 用于发布页面时指定的projectCode
   * 注意：此projectCode专用于低代码平台页面发布，与枚举服务的projectCode不同
   */
  projectCode: 'APass-Test',

  /**
   * 预览页面基础路径
   */
  previewBasePath: '/low-code-editor/2_18_3/preview/',

  /**
   * 创建页面API路径
   */
  createPageApiPath: '/pmc-server/page/save'
}

/**
 * 枚举服务相关配置
 */
export const enumServiceConfig = {
  /**
   * 枚举服务Base URL
   * 与低代码平台共用同一个baseUrl
   */
  baseUrl: lowCodeConfig.baseUrl,

  /**
   * 枚举服务所属项目代码
   * 用于枚举列表查询和创建枚举时使用
   * 注意：此projectCode现在与低代码平台保持一致，使用'APass-Test'
   * 临时统一处理，未来将通过统一接口配置
   */
  projectCode: 'APass-Test',

  /**
   * 枚举服务API路径
   */
  apiBasePath: '/yeeworks/modeling/rest/enumeration'
}

/**
 * 权限中心相关配置
 * 注意：此处的配置仅供参考，实际使用时请根据需要进行调整
 */
export const authConfig = {
  /**
   * 权限中心 URL
   * 与低代码平台共用同一个baseUrl
   */
  baseUrl: lowCodeConfig.baseUrl,

  /**
   * 项目在 权限中心 systemCode 标识
   */
  systemCode: 'data-scope',

  /**
   * 权限中心 获取用户信息的API路径
   */
  getUserProfileApiPath: '/yuia-service-boss/auth/principal',

  /**
   * 权限中心 获取接口权限的API路径
   */
  getInterfacePermissionApiPath: '/yuia-service-boss/auth/funcs',

  /**
   * 退出登录API路径
   */
  logoutApiPath: '/yuia-service-boss/sso/logout',

  /**
   * 登录页面URL
   */
  loginUrl: import.meta.env.PROD
    ? '/yuia-service-boss/login.html'
    : 'https://qauiaservice.yeepay.com/yuia-service-boss/login.html',

  /**
   * 登录后回调URL
   * 注意：这是默认的全局回调URL，实际使用时会被当前页面的URL覆盖
   * 优先使用环境变量 VITE_LOWCODE_DOMAIN，如果未设置则根据环境判断
   * 生产环境使用 boss.yeepay.com，开发/测试环境使用 qaboss.yeepay.com
   * @see TheNavigation.vue中的handleLogout和goToLogin函数
   */
  callbackUrl: `${getLowCodeDomain()}/boss-yeeworks/index.html?lowcode=/boss-lowcode/index.html`
}
