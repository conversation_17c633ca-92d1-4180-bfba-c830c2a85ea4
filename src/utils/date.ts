/**
 * 格式化日期为相对时间
 * @param date 日期对象或日期字符串
 * @returns 格式化后的相对时间字符串
 */
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diff = now.getTime() - d.getTime();
  
  // 转换为分钟
  const minutes = Math.floor(diff / 1000 / 60);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  
  // 转换为小时
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}小时前`;
  
  // 转换为天
  const days = Math.floor(hours / 24);
  if (days < 30) return `${days}天前`;
  
  // 超过30天显示具体日期
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}; 