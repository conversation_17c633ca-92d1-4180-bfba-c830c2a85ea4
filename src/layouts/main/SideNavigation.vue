<style lang="scss">
@use '@/styles/variables' as *;

// ... 其他样式保持不变 ...

.tertiary-menu {
  background: $bg-color-light;
  
  .menu-item {
    padding: 8px 16px 8px 40px;
    cursor: pointer;
    
    &:hover {
      background: color.adjust($bg-color-light, $lightness: 2%);
    }
    
    &.active {
      color: $primary-color;
      background: $primary-color-light;
    }
    
    .label {
      font-size: 13px;
    }
  }
}
</style> 