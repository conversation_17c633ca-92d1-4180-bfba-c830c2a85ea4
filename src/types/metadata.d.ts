export interface ColumnMetadata {
  name: string
  type: string
  size?: number
  scale?: number
  nullable: boolean
  defaultValue?: string
  primaryKey: boolean
  foreignKey: boolean
  referencedTable?: string
  referencedColumn?: string
  unique: boolean
  autoIncrement: boolean
  comment?: string
}

export interface TableMetadata {
  id?: string
  name: string
  type: 'TABLE' | 'VIEW' | 'MATERIALIZED_VIEW' | 'SYSTEM_TABLE' | 'SYSTEM_VIEW' | 'OTHER'
  comment?: string
  schema?: string
  catalog?: string
  columns: ColumnMetadata[]
  primaryKey?: string[]
  indices?: {
    name: string
    type: 'BTREE' | 'HASH' | 'FULLTEXT' | 'OTHER'
    columns: string[]
    unique: boolean
  }[]
}

export interface ExtendedTableMetadata extends TableMetadata {
  isUpdating?: boolean
  lastUpdated?: Date
}

export interface MetadataState {
  loading: boolean
  error: string
  tables: ExtendedTableMetadata[]
  expandedTables: string[]
  searchTerm: string
  highlightedTable: string | null
} 