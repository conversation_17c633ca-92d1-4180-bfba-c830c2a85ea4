// 类型定义文件 - 集成编辑容器

import { ColumnAlign, ChartType } from '@/types/integration';
import type { ChartStyleOptions, ChartInteractions } from '@/types/integration';

// 集成类型定义
export type IntegrationType = 'TABLE' | 'SIMPLE_TABLE' | 'CHART';

// 集成状态定义
export type IntegrationStatus = 'DRAFT' | 'ACTIVE' | 'INACTIVE';

// 表格列定义
export interface TableColumn {
  field: string;
  label: string;
  type: string;
  format?: string;
  visible: boolean;
  align: ColumnAlign;
  width?: string | number;
  sortable?: boolean;
  filterable?: boolean;
  displayOrder?: number;
  [key: string]: any;
}

// 分页配置
export interface PaginationConfig {
  enabled: boolean;
  pageSize: number;
  pageSizeOptions: number[];
  showSizeChanger?: boolean;
}

// 导出配置
export interface ExportConfig {
  enabled: boolean;
  formats: string[];
  maxRows: number;
}

// 聚合配置
export interface AggregationConfig {
  enabled: boolean;
  groupByFields: string[];
  aggregationFunctions: any[];
}

// 高级筛选配置
export interface AdvancedFiltersConfig {
  enabled: boolean;
  defaultFilters: any[];
  savedFilters: any[];
}

// 表格配置定义
export interface TableConfig {
  columns: TableColumn[];
  actions: any[];
  pagination: PaginationConfig;
  export: ExportConfig;
  batchActions: any[];
  aggregation: AggregationConfig;
  advancedFilters: AdvancedFiltersConfig;
  rowSelection?: boolean;
  showHeader?: boolean;
  bordered?: boolean;
  showFooter?: boolean;
  rowActionsFixed?: 'left' | 'right' | false; // 行操作列是否固定，以及固定位置
}

// 图表数据映射配置
export interface ChartDataMapping {
  xField: string;
  yField: string;
  seriesField?: string;
  valueField?: string;
  categoryField?: string;
  sizeField?: string;
  colorField?: string;
}

// 图表配置定义
export interface ChartConfig {
  type: string;
  title?: string;
  description?: string;
  height: number;
  showLegend: boolean;
  animation: boolean;
  theme: string;
  dataMapping: ChartDataMapping;
  styleOptions?: ChartStyleOptions;
  interactions?: ChartInteractions;
  [key: string]: any;
}

// 查询参数定义
export interface QueryParam {
  name: string;
  type: string;
  description: string;
  format: string;
  formType: string;
  required: boolean;
  isNewParam: boolean;
  options?: Array<{label: string, value: string}>;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  defaultValue?: any;
  placeholder?: string;
  [key: string]: any;
}

// 集成对象定义
export interface IntegrationData {
  id: string;
  name: string;
  description: string;
  type: IntegrationType;
  status: IntegrationStatus;
  dataSourceId: string;
  queryId: string;
  tableConfig?: TableConfig;
  chartConfig?: ChartConfig;
  queryParams?: QueryParam[];
  parameters?: QueryParam[];
  createTime?: string;
  updateTime?: string;
  formConfig?: any;
}