// API响应状态码
export enum ApiResponseStatus {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  PENDING = 'PENDING'
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  status: ApiResponseStatus;
  timestamp: string;
}

// API映射配置类型
export interface ApiMappingConfig {
  baseConfig: {
    apiBaseUrl: string;
    mockDataEnabled: boolean;
    autoSwitchToMock: boolean;
    connectionTimeout: number;
    defaultTimeoutMs: number;
  };
  datasource: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    test: string;
    testExistingConnection: string;
    checkStatus: string;
    stats: string;
    metadata: string;
    sync: string;
    schemas: string;
    tables: string;
    search: string;
  };
  metadataManagement: {
    sync: string;
    schemas: string;
    tables: string;
    search: string;
  };
  metadata: {
    schemas: string;
    tables: string;
    table: string;
    views: string;
    columns: string;
    statistics: string;
    relationships: string;
    sync: string;
  };
  query: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    execute: string;
    cancel: string;
    history: string;
    search: string;
    versions: string;
  };
  version: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    publish: string;
    activate: string;
    deprecate: string;
  };
  integration: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    test: string;
    sync: string;
  };
}

// 导出常量
export const DEFAULT_TIMEOUT = 30000;
export const DEFAULT_PAGE_SIZE = 10;
