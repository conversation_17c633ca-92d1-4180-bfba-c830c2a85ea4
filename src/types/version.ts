/**
 * 查询版本信息接口
 * 描述查询的版本记录
 */
export interface VersionInfo {
  id: string;
  queryId: string;
  versionNumber: number;
  name?: string;
  description?: string;
  createdAt: string;
  createdBy?: string;
  isDraft: boolean;
  sql?: string;
  naturalLanguageQuery?: string;
  builderQuery?: string;
}

/**
 * 创建版本请求接口
 * 用于发送创建版本的请求
 */
export interface CreateVersionRequest {
  name?: string;
  description?: string;
  sql?: string;
  naturalLanguageQuery?: string;
  builderQuery?: string;
  queryType: 'sql' | 'natural-language' | 'builder';
  isDraft: boolean;
  tags?: string[];
}

/**
 * 版本响应接口
 * API返回的版本数据结构
 */
export interface VersionResponse {
  success: boolean;
  version: VersionInfo;
  message?: string;
}

/**
 * 版本列表响应接口
 * API返回的版本列表数据结构
 */
export interface VersionListResponse {
  success: boolean;
  versions: VersionInfo[];
  total?: number;
  message?: string;
}

/**
 * 版本状态类型
 * 描述版本数据的加载状态
 */
export type VersionStatus = 'loading' | 'loaded' | 'error' | 'empty';