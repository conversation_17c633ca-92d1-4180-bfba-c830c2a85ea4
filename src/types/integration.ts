export interface TableColumn {
  id: string;
  field: string;
  label: string;
  displayType: DisplayType;
  description?: string;
  visible: boolean;
  options?: SelectOption[];
  align?: ColumnAlign;
  width?: string | number;
  format?: string;
  sortable?: boolean;
  filterable?: boolean;
  displayOrder?: number;
  isNewColumn?: boolean;
  type?: string;
  enumCode?: string;
  enumId?: string | number;
  enumName?: string;
  enumDisplay?: string;
  defaultEnumValue?: string;
  maskType?: string;
  config?: Record<string, any>;
}

export type DisplayType = 
  | 'text'
  | 'number'
  | 'date'
  | 'datetime'
  | 'boolean'
  | 'select'
  | 'tags'
  | 'image'
  | 'file'
  | 'link';

export interface SelectOption {
  value: string;
  label: string;
}

export interface IntegrationTable {
  id: string;
  name: string;
  description?: string;
  columns: TableColumn[];
  createdAt: string;
  updatedAt: string;
}

export interface IntegrationTableSettings {
  defaultView: 'table' | 'card' | 'list';
  defaultPageSize: number;
  enableSearch: boolean;
  enableFilters: boolean;
  enableExport: boolean;
}

export enum ColumnDisplayType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  DATETIME = 'DATETIME',
  BOOLEAN = 'BOOLEAN',
  SELECT = 'SELECT',
  TAGS = 'TAGS',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  LINK = 'LINK'
}

export enum ColumnAlign {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right'
}

export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  SCATTER = 'scatter',
  RADAR = 'radar',
  HEATMAP = 'heatmap'
}

export enum ChartTheme {
  LIGHT = 'light',
  DARK = 'dark',
  CUSTOM = 'custom'
}

export interface TableConfig {
  columns: TableColumn[];
  actions: TableAction[];
  rowActions: TableAction[];
  batchActions: BatchAction[];
  pagination: {
    enabled: boolean;
    pageSize: number;
    pageSizeOptions: number[];
  };
  export: {
    enabled: boolean;
    formats: string[];
    maxRows: number;
  };
  aggregation: {
    enabled: boolean;
    groupByFields: string[];
    aggregationFunctions: string[];
  };
  advancedFilters: {
    enabled: boolean;
    defaultFilters: any[];
    savedFilters: any[];
  };
  rowActionsFixed: false | 'left' | 'right';
}

export interface TableAction {
  label: string;
  handler: string;
  confirm?: boolean;
  icon?: string;
  type?: string;
  originalLabel?: string;
  [key: string]: any;
}

export interface BatchAction {
  id?: string;
  label: string;
  handler: string;
  confirmationRequired?: boolean;
  originalId?: string;
  index?: number;
  [key: string]: any;
}

export interface QueryResult {
  success: boolean;
  data: any;
  columns?: QueryResultColumn[];
  fields?: QueryResultColumn[];
  [key: string]: any;
}

export interface QueryResultColumn {
  name: string;
  type: string;
  label?: string;
  [key: string]: any;
}

export interface PaginationConfig {
  enabled: boolean;
  pageSize: number;
  pageSizeOptions: number[];
}

export interface ExportConfig {
  enabled: boolean;
  formats: string[];
  maxRows: number;
}