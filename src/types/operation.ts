import type { PaginationParams, PaginationResponse } from './common';
import type { Query, QueryParameter } from './query';

/**
 * 操作类型枚举
 */
export type OperationType = 'CREATE' | 'UPDATE' | 'DELETE' | 'CUSTOM';

/**
 * 操作状态枚举
 */
export type OperationStatus = 'DRAFT' | 'ACTIVE' | 'INACTIVE';

/**
 * 执行状态枚举
 */
export type ExecutionStatus = 'SUCCESS' | 'FAILED' | 'PENDING' | 'PROCESSING';

/**
 * 协议类型枚举
 */
export type ProtocolType = 'HTTP' | 'GENERIC';

/**
 * Content-Type类型枚举
 */
export type ContentType = 'APPLICATION/JSON' | 'APPLICATION/FORM-DATA';

/**
 * 应用接口参数
 */
export interface ApiParameter {
  id: string;
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description?: string;
  mappingField?: string; // 查询结果中的字段映射
}

/**
 * 操作执行记录
 */
export interface OperationExecution {
  id: string;
  operationId: string;
  executedAt: string;
  executionTime: number;
  status: ExecutionStatus;
  result?: any;
  errorMessage?: string;
  executedBy?: string;
}

/**
 * 操作模型
 */
export interface Operation {
  id: string;
  name: string;
  description?: string;
  type: OperationType;
  status: OperationStatus;
  queryId: string;           // 关联的查询ID
  applicationId: string;     // 关联的应用ID
  protocolType: ProtocolType; // 协议类型
  contentType: ContentType;   // 内容类型
  apiEndpoint: string;       // 调用的API端点
  apiMethod: 'GET' | 'POST' | 'PUT' | 'DELETE';  // 请求方法
  apiParameters: ApiParameter[];
  requireConfirmation: boolean; // 是否需要确认
  confirmationMessage?: string; // 确认消息
  successMessage?: string;      // 成功消息
  errorMessage?: string;        // 错误消息
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  query?: Query;               // 关联的查询对象
}

/**
 * 应用接口定义
 */
export interface Application {
  id: string;
  name: string;
  description?: string;
  baseUrl: string;
  apis: Array<{
    id: string;
    name: string;
    path: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    parameters: ApiParameter[];
  }>;
}

/**
 * 操作查询参数
 */
export interface OperationQueryParams extends PaginationParams {
  search?: string;
  type?: OperationType;
  status?: OperationStatus;
  queryId?: string;
  applicationId?: string;
}

/**
 * 操作分页响应
 */
export interface OperationPaginationResponse extends PaginationResponse {
  items: Operation[];
}

/**
 * 创建操作参数
 */
export interface CreateOperationParams {
  name: string;
  description?: string;
  type: OperationType;
  queryId: string;
  applicationId: string;
  protocolType: ProtocolType;
  contentType: ContentType;
  apiEndpoint: string;
  apiMethod: 'GET' | 'POST' | 'PUT' | 'DELETE';
  apiParameters: ApiParameter[];
  requireConfirmation?: boolean;
  confirmationMessage?: string;
  successMessage?: string;
  errorMessage?: string;
}

/**
 * 更新操作参数
 */
export interface UpdateOperationParams {
  name?: string;
  description?: string;
  type?: OperationType;
  queryId?: string;
  applicationId?: string;
  protocolType?: ProtocolType;
  contentType?: ContentType;
  apiEndpoint?: string;
  apiMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  apiParameters?: ApiParameter[];
  requireConfirmation?: boolean;
  confirmationMessage?: string;
  successMessage?: string;
  errorMessage?: string;
  status?: OperationStatus;
} 