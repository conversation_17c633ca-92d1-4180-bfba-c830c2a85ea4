// 脱敏类型枚举
export enum DesensitizeType {
  BANK_CARD = 'BANK_CARD',
  CVV = 'CVV',
  ID_CARD = 'ID_CARD',
  OFFICER_ID = 'OFFICER_ID',
  NAME = 'NAME',
  PHONE = 'PHONE',
  MOBILE = 'MOBILE',
  EMAIL = 'EMAIL',
  ADDRESS = 'ADDRESS'
}

// 脱敏类型配置
export interface DesensitizeTypeConfig {
  code: DesensitizeType;
  name: string;
  description: string;
}

// 脱敏类型配置列表
export const DESENSITIZE_TYPE_CONFIGS: DesensitizeTypeConfig[] = [
  {
    code: DesensitizeType.BANK_CARD,
    name: '银行卡号',
    description: '显示前6位+***+后4位'
  },
  {
    code: DesensitizeType.CVV,
    name: '有效期CVV',
    description: '完全屏蔽'
  },
  {
    code: DesensitizeType.ID_CARD,
    name: '身份证',
    description: '显示前2位+*(实际位数)+后2位'
  },
  {
    code: DesensitizeType.OFFICER_ID,
    name: '军官证号/护照号',
    description: '显示前2位+*(实际位数)+后2位'
  },
  {
    code: DesensitizeType.NAME,
    name: '姓名',
    description: '隐藏第一个字'
  },
  {
    code: DesensitizeType.PHONE,
    name: '固定电话',
    description: '显示区号和后4位'
  },
  {
    code: DesensitizeType.MOBILE,
    name: '手机号',
    description: '显示前3位+***+后4位'
  },
  {
    code: DesensitizeType.EMAIL,
    name: '邮箱',
    description: '@前面显示3位，后面显示3个*，@后面完整显示'
  },
  {
    code: DesensitizeType.ADDRESS,
    name: '地址',
    description: '隐藏小区信息以及门牌号（隐藏数字）'
  }
]; 