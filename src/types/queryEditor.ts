import type { Query, SaveQueryParams, QueryType } from './query';
import type { DataSource } from './datasource';
import type { SchemaMetadata } from './metadata';
import type { QueryBuilderState } from './builder';
import type { QueryVersion } from './queryVersion';

// QueryEditor 组件的数据类型定义
export interface QueryEditorState {
  // 编辑器标签页
  activeTab: 'editor' | 'nlq' | 'builder';
  
  // 左侧面板
  leftPanel: 'metadata' | 'saved';
  
  // 数据源相关
  selectedDataSourceId: string;
  selectedSchema: string;
  availableSchemas: SchemaMetadata[];
  
  // 查询内容
  sqlQuery: string;
  naturalLanguageQuery: string;
  builderQuery: string;
  builderState: QueryBuilderState;
  
  // 执行状态
  isExecuting: boolean;
  executionTime: number;
  queryError: string | null;
  
  // 消息状态
  statusMessage: string | null;
  errorMessage: string | null;
  
  // 查询数据
  currentQueryId: string | null;
  queryName: string;
  
  // 版本信息
  queryVersion: string;
  availableVersions: string[];
  selectedVersion: string;
  currentMaxVersionNumber: number;
  versionStatus: 'DRAFT' | 'PUBLISHED' | 'DEPRECATED';
  isActiveVersion: boolean;
  
  // UI状态
  isSaveModalVisible: boolean;
  isLoadingQuery: boolean;
  showExportOptions: boolean;
  
  // 其他
  isFavorite: boolean;
}

// 查询执行结果类型
export interface QueryExecutionResult {
  id: string;
  columns: string[];
  rows: any[][];
  executionTime: number;
  rowCount: number;
  success: boolean;
  error?: string;
}

// 查询保存数据类型
export interface QuerySaveData {
  name: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

// 组件方法接口
export interface QueryEditorMethods {
  // 查询操作
  executeQuery: () => Promise<void>;
  cancelQuery: () => void;
  saveQuery: () => void;
  handleSaveQuery: (saveData: QuerySaveData) => Promise<void>;
  publishQuery: () => Promise<void>;
  
  // 数据源相关
  refreshMetadata: () => Promise<void>;
  handleSchemaChange: () => Promise<void>;
  
  // 导出操作
  exportResults: (format: 'csv' | 'excel' | 'json') => void;
  
  // 导航
  returnToList: () => void;
  viewVersions: () => void;
  
  // 工具函数
  getQueryTextByType: (type: QueryType) => string;
  formatDate: (dateString: string | number | Date | undefined | null) => string;
}

// Schema处理后的接口
export interface ProcessedSchema {
  value: string;
  name: string;
  tablesCount?: number;
  dataSourceId?: string;
}

// 延迟模拟函数类型
export type SimulateDelayFunction = (min: number, max: number) => Promise<void>;