<template>
  <TheNavigation />
  <router-view />
  <TheFooter />
  <MessageNotification />
</template>

<script setup lang="ts">
import { onMounted, onBeforeMount, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { usePermission } from '@/stores/permission'
import { setTokenFromUrl, getQueryObject } from '@/utils';
import TheNavigation from './components/layout/TheNavigation.vue'
import TheFooter from './components/layout/TheFooter.vue'
import MessageNotification from './components/common/MessageNotification.vue'

const userStore = useUserStore()

// 检查页面URL是否包含退出标记
const checkForLogoutFlag = () => {
  const params = getQueryObject(window.location.href)
  if (params['logout'] === 'true') {
    // 清除本地状态
    userStore.setUser(null)
    userStore.setToken(null)
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    sessionStorage.clear()

    // 移除logout参数
    const newUrl = window.location.href.replace(/[?&]logout=true/, '')
    window.history.replaceState({}, '', newUrl)
  }
}

// 立即检查并获取URL中的token
onBeforeMount(() => {

  // 先检查是否有退出标记
  checkForLogoutFlag()

  // 从 URL 中获取 token并立即移除URL中的token参数
  const hasToken = setTokenFromUrl('yuiassotoken')

  // 验证URL是否还包含token参数
  const validateNoToken = () => {
    const params = getQueryObject(window.location.href)
    if (params['yuiassotoken']) {
      console.warn('[安全警告] URL中仍然包含token参数，再次尝试移除')
      setTokenFromUrl('yuiassotoken')
    }
  }

  // 立即验证并在100ms后再次验证（确保异步操作完成）
  validateNoToken()
  setTimeout(validateNoToken, 100)

  if (hasToken) {
    // 如果获取到了token，立即刷新用户信息
    userStore.fetchUserProfile()
      .catch(error => {
        console.error('获取用户信息失败:', error)
      })
  }
})

onMounted(() => {
  // 检查登录状态
  const storedToken = localStorage.getItem('token')

  // 同步localStorage和Pinia中的token
  if (storedToken && userStore.token !== storedToken) {
    userStore.setToken(storedToken)
  }

  const isLoggedIn = !!storedToken

  if (isLoggedIn) {
    // 刷新用户信息
    userStore.fetchUserProfile()
      .then(async () => {
        // 强制刷新组件
        if (userStore.isAuthenticated && !userStore.user) {
          setTimeout(() => userStore.fetchUserProfile(), 1000)
        }

        // 获取接口权限
        const { fetchInterfacePermission } = usePermission()
        void fetchInterfacePermission()
      })
      .catch((error: any) => {
        console.error('获取用户信息失败:', error)
      })
  }
})

// 监听路由变化，重新检查登录状态
watch(() => window.location.href, () => {
  checkForLogoutFlag()
}, { immediate: true })
</script>
