import { createVNode, render } from 'vue'
import type { App } from 'vue'
import type { MessageConfig, MessageInstance, MessageService, MessageQueueConfig } from '@/types/message'
import MessageAlert from '@/components/common/MessageAlert.vue'

// 消息容器
let messageContainer: HTMLDivElement | null = null

// 消息实例列表
const instances: MessageInstance[] = []

// 最近显示的消息记录，用于短时间内的严格防重复
const recentMessages = new Map<string, number>();

// 消息队列配置
const queueConfig: MessageQueueConfig = {
  deduplicationTimeWindow: 3000, // 3秒内的相同消息将被合并
  maxCount: 10, // 最多显示10条消息
  defaultDuration: 3000 // 默认显示3秒
}

// 创建消息容器
const createMessageContainer = () => {
  if (messageContainer) return

  messageContainer = document.createElement('div')
  messageContainer.className = 'fixed top-4 right-4 flex flex-col gap-2' // 右上角显示
  messageContainer.style.zIndex = '9999' // 设置极高的z-index确保消息始终显示在最上层
  document.body.appendChild(messageContainer)
}

// 移除消息实例
const removeInstance = (id: string) => {
  const index = instances.findIndex(instance => instance.id === id)
  if (index !== -1) {
    instances.splice(index, 1)
    if (instances.length === 0 && messageContainer) {
      document.body.removeChild(messageContainer)
      messageContainer = null
    }
  }
}

// 添加短时间内严格防重复逻辑
const isRecentlySent = (type: string, content: string): boolean => {
  // 生成消息的键
  const messageKey = `${type}-${content}`;
  const now = Date.now();
  
  // 检查是否最近才发送了相同消息
  const lastSent = recentMessages.get(messageKey);
  if (lastSent && now - lastSent < 500) { // 500毫秒内的相同消息视为重复
    console.log(`[消息服务] 阻止快速重复消息: ${messageKey}`);
    return true;
  }
  
  // 记录此消息的发送时间
  recentMessages.set(messageKey, now);
  
  // 设置过期清理
  setTimeout(() => {
    recentMessages.delete(messageKey);
  }, 2000); // 2秒后过期
  
  return false;
}

// 检查是否为重复消息
const isDuplicateMessage = (config: MessageConfig): MessageInstance | null => {
  // 如果明确允许重复，则不检查
  if (config.allowDuplicate === true) {
    return null
  }
  
  const now = Date.now()
  const key = config.key || config.content // 使用key或content作为去重依据
  
  // 查找时间窗口内相同类型和内容的消息
  const duplicate = instances.find(instance => {
    const sameType = instance.config.type === config.type
    const sameContent = (instance.config.key || instance.config.content) === key
    const withinTimeWindow = now - instance.createdAt < queueConfig.deduplicationTimeWindow!
    return sameType && sameContent && withinTimeWindow
  })
  
  return duplicate || null
}

// 更新重复消息
const updateDuplicateMessage = (id: string) => {
  try {
    const messageIndex = instances.findIndex(msg => msg.id === id);
    if (messageIndex !== -1) {
      instances[messageIndex].duplicateCount = (instances[messageIndex].duplicateCount || 0) + 1;
      
      // 使用带错误处理的setTimeout包装
      try {
        window.setTimeout(() => {
          removeInstance(id);
        }, instances[messageIndex].config.duration || 3000);
      } catch (error) {
        console.error('消息服务setTimeout错误:', error);
        // 降级处理：直接在下一个事件循环移除
        Promise.resolve().then(() => {
          setTimeout(() => removeInstance(id), 3000);
        });
      }
    }
  } catch (error) {
    console.error('更新重复消息状态错误:', error);
  }
};

// 限制消息数量
const limitMessageCount = () => {
  const maxCount = queueConfig.maxCount || 10
  if (instances.length > maxCount) {
    // 移除最早的消息，直到数量符合限制
    const removeCount = instances.length - maxCount
    const earliestInstances = [...instances]
      .sort((a, b) => a.createdAt - b.createdAt)
      .slice(0, removeCount)
    
    earliestInstances.forEach(instance => {
      removeInstance(instance.id)
      
      // 移除DOM元素
      const container = document.getElementById(instance.id)
      if (container && container.parentNode) {
        container.parentNode.removeChild(container)
      }
    })
  }
}

// 创建消息实例
const createMessage = (config: MessageConfig) => {
  // 先检查是否是快速重复的消息
  if (!config.allowDuplicate && isRecentlySent(config.type, config.content)) {
    return null; // 如果是快速重复消息，直接阻止显示
  }
  
  createMessageContainer()
  
  // 检查重复消息
  const duplicate = isDuplicateMessage(config)
  if (duplicate) {
    return updateDuplicateMessage(duplicate.id)
  }
  
  // 限制消息数量
  limitMessageCount()

  // 生成唯一ID - 添加随机数避免时间戳相同导致的ID重复
  const id = `message-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  
  // 创建容器
  const container = document.createElement('div')
  container.id = id
  
  // 如果是合并消息，添加计数
  if (!config.allowDuplicate && config.count === undefined) {
    config.count = 1
  }

  // 创建消息节点
  const vnode = createVNode(MessageAlert, {
    config,
    onClose: () => removeInstance(id)
  })

  render(vnode, container)
  messageContainer?.appendChild(container)

  // 保存实例
  const instance: MessageInstance = {
    id,
    config,
    createdAt: Date.now()
  }
  instances.push(instance)

  return instance
}

// 消息服务实例
export const message: MessageService = {
  info(content: string, duration = queueConfig.defaultDuration, allowDuplicate = false) {
    // 为每个消息生成唯一key防止重复
    const key = `info-${content}`;
    createMessage({
      type: 'info',
      content,
      duration,
      showIcon: true,
      closable: true,
      allowDuplicate,
      key: key
    })
  },

  success(content: string, duration = queueConfig.defaultDuration, allowDuplicate = false) {
    // 为每个消息生成唯一key防止重复
    const key = `success-${content}`;
    createMessage({
      type: 'success',
      content,
      duration,
      showIcon: true,
      closable: true,
      allowDuplicate,
      key: key
    })
  },

  warning(content: string, duration = queueConfig.defaultDuration, allowDuplicate = false) {
    // 为每个消息生成唯一key防止重复
    const key = `warning-${content}`;
    createMessage({
      type: 'warning',
      content,
      duration,
      showIcon: true,
      closable: true,
      allowDuplicate,
      key: key
    })
  },

  error(content: string, duration = queueConfig.defaultDuration, allowDuplicate = false) {
    // 为每个消息生成唯一key防止重复
    const key = `error-${content}`;
    createMessage({
      type: 'error',
      content,
      duration,
      showIcon: true,
      closable: true,
      allowDuplicate,
      key: key
    })
  },

  show(config: MessageConfig) {
    createMessage({
      ...config,
      duration: config.duration || queueConfig.defaultDuration
    })
  },

  close(id: string) {
    removeInstance(id)
  },

  closeAll() {
    instances.forEach(instance => removeInstance(instance.id))
  },
  
  setQueueConfig(config: Partial<MessageQueueConfig>) {
    Object.assign(queueConfig, config)
  }
}

/**
 * 消息服务插件，用于在Vue 3中通过插件机制安装消息服务
 */
export const installMessageService = {
  /**
   * 安装插件方法
   * @param app Vue应用实例
   */
  install(app: App): void {
    // 将消息服务添加到全局属性中
    app.config.globalProperties.$message = message;
    
    // 提供消息服务供组件通过inject使用
    app.provide('messageService', message);
  }
};

/**
 * 获取消息服务的Hook函数
 * @returns 消息服务实例
 */
export function useMessageService(): MessageService {
  return message;
}

export default message