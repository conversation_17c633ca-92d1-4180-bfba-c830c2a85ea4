import type {
  QueryVersion,
  QueryVersionStatus,
  GetVersionsParams,
  CreateVersionParams,
  UpdateVersionParams
} from '@/types/queryVersion';
import { getApiBaseUrl } from './query';
import { getQueryApiUrl, getApiUrl } from './apiUtils';
import type { PageResponse } from '@/types/query';
import instance from '@/utils/axios';

// 检查是否启用mock模式
const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';
console.log('查询版本服务 - Mock模式:', USE_MOCK ? '已启用' : '已禁用');

// 模拟数据：查询版本
const mockVersions: QueryVersion[] = [
  // 查询1的版本
  ...Array.from({ length: 5 }, (_, i) => ({
    id: `ver-query-1-${i + 1}`,
    queryId: 'query-1',
    versionNumber: i + 1,
    queryText: `SELECT * FROM users WHERE id > ${i * 10}\nLIMIT 100;`,
    status: i === 0 ? 'PUBLISHED' : (i === 4 ? 'DEPRECATED' : 'DRAFT') as QueryVersionStatus,
    isActive: i === 0,
    createdAt: new Date(Date.now() - (5 - i) * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - (5 - i) * 86400000 + 3600000).toISOString(),
    publishedAt: i === 0 ? new Date(Date.now() - (5 - i) * 86400000 + 7200000).toISOString() : undefined,
    deprecatedAt: i === 4 ? new Date().toISOString() : undefined,
    dataSourceId: 'ds-1'
  })),

  // 查询2的版本
  ...Array.from({ length: 3 }, (_, i) => ({
    id: `ver-query-2-${i + 1}`,
    queryId: 'query-2',
    versionNumber: i + 1,
    queryText: `SELECT * FROM products WHERE category_id = ${i + 1}\nORDER BY price DESC\nLIMIT 50;`,
    status: i === 0 ? 'PUBLISHED' : 'DRAFT' as QueryVersionStatus,
    isActive: i === 0,
    createdAt: new Date(Date.now() - (3 - i) * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - (3 - i) * 86400000 + 3600000).toISOString(),
    publishedAt: i === 0 ? new Date(Date.now() - (3 - i) * 86400000 + 7200000).toISOString() : undefined,
    dataSourceId: 'ds-1'
  })),

  // 查询3的版本
  ...Array.from({ length: 2 }, (_, i) => ({
    id: `ver-query-3-${i + 1}`,
    queryId: 'query-3',
    versionNumber: i + 1,
    queryText: `SELECT o.id, o.order_date, c.name as customer_name, SUM(oi.quantity * oi.price) as total_amount\nFROM orders o\nJOIN customers c ON o.customer_id = c.id\nJOIN order_items oi ON o.id = oi.order_id\nWHERE o.order_date > '2023-01-01'\nGROUP BY o.id, o.order_date, c.name\nORDER BY total_amount DESC\nLIMIT ${(i + 1) * 10};`,
    status: i === 0 ? 'PUBLISHED' : 'DRAFT' as QueryVersionStatus,
    isActive: i === 0,
    createdAt: new Date(Date.now() - (2 - i) * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - (2 - i) * 86400000 + 3600000).toISOString(),
    publishedAt: i === 0 ? new Date(Date.now() - (2 - i) * 86400000 + 7200000).toISOString() : undefined,
    dataSourceId: 'ds-2'
  }))
];

// 版本服务
export const versionService = {
  // 获取查询服务的版本列表
  async getVersions(params: GetVersionsParams): Promise<PageResponse<QueryVersion>> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据返回查询版本列表，查询ID: ${params.queryId}`);

        // 过滤版本数据
        let filteredVersions = [...mockVersions];

        // 按状态过滤
        if (params.status) {
          filteredVersions = filteredVersions.filter(v => v.status === params.status);
        }

        // 应用分页
        const page = params.page || 1;
        const size = params.size || 10;
        const startIndex = (page - 1) * size;
        const endIndex = Math.min(startIndex + size, filteredVersions.length);

        // 返回分页结果
        return {
          items: filteredVersions.slice(startIndex, endIndex),
          total: filteredVersions.length,
          page: page,
          size: size,
          totalPages: Math.ceil(filteredVersions.length / size)
        };
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.size) queryParams.append('size', params.size.toString());
      if (params.status) queryParams.append('status', params.status);

      const url = `/api/queries/${params.queryId}/versions?${queryParams.toString()}`;

      // 使用instance.get代替fetch
      const response = await instance.get(url);

      // 注意：instance.get已经解析了JSON，不需要再调用response.json()
      const result = response.data;
      console.log('获取到版本列表原始数据:', result);

      // 检查数据格式
      if (!result) {
        throw new Error('获取版本列表失败: 服务器返回空数据');
      }

      // 处理不同的响应格式
      let items = [];
      let pagination = {
        total: 0,
        page: params.page || 1,
        size: params.size || 10,
        totalPages: 0
      };

      if (result.data) {
        // 标准响应格式
        if (Array.isArray(result.data)) {
          items = result.data.map(mapVersionFromApi);
        } else if (result.data.items && Array.isArray(result.data.items)) {
          items = result.data.items.map(mapVersionFromApi);
        }

        // 获取分页信息
        pagination = {
          total: result.data.total || result.total || items.length,
          page: result.data.page || result.page || params.page || 1,
          size: result.data.size || result.size || params.size || 10,
          totalPages: result.data.totalPages || result.totalPages || Math.ceil(items.length / (params.size || 10))
        };
      } else if (Array.isArray(result)) {
        // 直接返回数组的格式
        items = result.map(mapVersionFromApi);
        pagination = {
          total: items.length,
          page: params.page || 1,
          size: params.size || 10,
          totalPages: Math.ceil(items.length / (params.size || 10))
        };
      } else if (result.items && Array.isArray(result.items)) {
        // 包含items字段的格式
        items = result.items.map(mapVersionFromApi);
        pagination = {
          total: result.total || items.length,
          page: result.page || params.page || 1,
          size: result.size || params.size || 10,
          totalPages: result.totalPages || Math.ceil(items.length / (params.size || 10))
        };
      }

      return {
        items,
        total: pagination.total,
        page: pagination.page,
        size: pagination.size,
        totalPages: pagination.totalPages
      };
    } catch (error) {
      console.error('获取版本列表失败:', error);
      throw error;
    }
  },

  // 获取单个版本
  async getVersion(versionId: string): Promise<QueryVersion> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据返回查询版本详情，版本ID: ${versionId}`);

        // 查找模拟版本
        const mockVersion = mockVersions.find(v => v.id === versionId);

        // 如果找不到版本，抛出错误
        if (!mockVersion) {
          throw new Error(`获取版本详情失败: 未找到版本 ${versionId}`);
        }

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 返回找到的版本
        return mockVersion;
      }

      const url = `/api/queries/versions/${versionId}`;

      // 使用instance.get代替fetch
      const response = await instance.get(url);

      // 注意：instance.get已经解析了JSON，不需要再调用response.json()
      const result = response.data;
      console.log('获取到版本详情原始数据:', result);

      // 尝试识别结果的格式（有些API返回包裹在data字段，有些直接返回对象）
      const versionData = result.data || result;

      // 确保结果非空
      if (!versionData || Object.keys(versionData).length === 0) {
        throw new Error(`获取版本详情失败: 服务器返回空数据`);
      }

      return mapVersionFromApi(versionData);
    } catch (error) {
      console.error('获取版本详情失败:', error);
      throw error;
    }
  },

  // 创建版本
  async createVersion(params: CreateVersionParams): Promise<QueryVersion> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据创建查询版本，查询ID: ${params.queryId}`);

        // 创建新的版本编号（当前最大版本号+1）
        const maxVersionNumber = Math.max(...mockVersions
          .filter(v => v.queryId === params.queryId)
          .map(v => v.versionNumber), 0);
        const newVersionNumber = maxVersionNumber + 1;

        // 创建新的模拟版本
        const newVersion: QueryVersion = {
          id: `ver-${params.queryId}-${newVersionNumber}`,
          queryId: params.queryId,
          versionNumber: newVersionNumber,
          queryText: params.sqlContent,
          status: 'DRAFT',
          isActive: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          dataSourceId: params.dataSourceId
        };

        // 添加到模拟数据中
        mockVersions.push(newVersion);

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        return newVersion;
      }

      const url = `/api/queries/${params.queryId}/versions`;

      console.log('创建版本，请求URL:', url);
      console.log('创建版本，请求参数:', params);

      // 准备请求体
      const requestBody = {
        sqlContent: params.sqlContent,
        dataSourceId: params.dataSourceId,
        description: params.description || ''
      };

      // 使用instance.post代替fetch
      const response = await instance.post(url, requestBody);

      // 处理响应
      const result = response.data;
      console.log('创建版本成功，返回结果:', result);
      return mapVersionFromApi(result.data);
    } catch (error) {
      console.error('创建版本失败:', error);
      throw error;
    }
  },

  // 更新版本
  async updateVersion(params: UpdateVersionParams): Promise<QueryVersion> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据更新查询版本，版本ID: ${params.id}`);

        // 查找模拟版本
        const index = mockVersions.findIndex(v => v.id === params.id);

        // 如果找不到版本，抛出错误
        if (index === -1) {
          throw new Error(`更新版本失败: 未找到版本 ${params.id}`);
        }

        // 更新版本
        if (params.sqlContent) {
          mockVersions[index].queryText = params.sqlContent;
        }

        mockVersions[index].updatedAt = new Date().toISOString();

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockVersions[index];
      }

      const url = `/api/queries/versions/${params.id}`;

      // 准备请求体
      const requestBody = {
        sqlContent: params.sqlContent
      };

      // 使用instance.put代替fetch
      const response = await instance.put(url, requestBody);

      // 处理响应
      const result = response.data;
      return mapVersionFromApi(result.data);
    } catch (error) {
      console.error('更新版本失败:', error);
      throw error;
    }
  },

  // 发布版本
  async publishVersion(versionId: string): Promise<QueryVersion> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据发布查询版本，版本ID: ${versionId}`);

        // 查找模拟版本
        const index = mockVersions.findIndex(v => v.id === versionId);

        // 如果找不到版本，抛出错误
        if (index === -1) {
          throw new Error(`发布版本失败: 未找到版本 ${versionId}`);
        }

        // 更新版本状态
        mockVersions[index].status = 'PUBLISHED';
        mockVersions[index].publishedAt = new Date().toISOString();
        mockVersions[index].updatedAt = new Date().toISOString();

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockVersions[index];
      }

      // 使用正确的API路径格式
      const url = `/api/queries/versions/${versionId}/publish`;
      console.log(`发布版本URL: ${url}`);

      // 使用instance.post代替fetch
      const response = await instance.post(url);

      // 处理响应
      const result = response.data;
      return mapVersionFromApi(result.data);
    } catch (error) {
      console.error('发布版本失败:', error);
      throw error;
    }
  },

  // 废弃版本
  async deprecateVersion(versionId: string): Promise<QueryVersion> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据废弃查询版本，版本ID: ${versionId}`);

        // 查找模拟版本
        const index = mockVersions.findIndex(v => v.id === versionId);

        // 如果找不到版本，抛出错误
        if (index === -1) {
          throw new Error(`废弃版本失败: 未找到版本 ${versionId}`);
        }

        // 更新版本状态
        mockVersions[index].status = 'DEPRECATED';
        mockVersions[index].deprecatedAt = new Date().toISOString();
        mockVersions[index].updatedAt = new Date().toISOString();

        // 如果该版本是活跃版本，取消活跃状态
        if (mockVersions[index].isActive) {
          mockVersions[index].isActive = false;
        }

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockVersions[index];
      }

      const url = `/api/queries/versions/${versionId}/deprecate`;

      // 使用instance.post代替fetch
      const response = await instance.post(url);

      // 处理响应
      const result = response.data;
      return mapVersionFromApi(result.data);
    } catch (error) {
      console.error('废弃版本失败:', error);
      throw error;
    }
  },

  // 设置活跃版本
  async activateVersion(queryId: string, versionId: string): Promise<{ success: boolean }> {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据设置活跃版本，查询ID: ${queryId}, 版本ID: ${versionId}`);

        // 查找模拟版本
        const index = mockVersions.findIndex(v => v.id === versionId);

        // 如果找不到版本，抛出错误
        if (index === -1) {
          throw new Error(`设置活跃版本失败: 未找到版本 ${versionId}`);
        }

        // 先取消所有版本的活跃状态
        mockVersions
          .filter(v => v.queryId === queryId)
          .forEach(v => v.isActive = false);

        // 设置新的活跃版本
        mockVersions[index].isActive = true;
        mockVersions[index].updatedAt = new Date().toISOString();

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        return { success: true };
      }

      const url = `/api/queries/${queryId}/versions/${versionId}/activate`;

      // 使用instance.post代替fetch
      const response = await instance.post(url);

      // 处理响应
      return { success: response.success };
    } catch (error) {
      console.error('设置活跃版本失败:', error);
      throw error;
    }
  },

  // 执行版本
  async executeVersion(queryId: string, versionId: string, parameters?: any): Promise<any> {
    try {
      // 验证版本ID
      if (!versionId || versionId.trim() === '') {
        console.error('执行版本失败: 版本ID不能为空');
        throw new Error('执行版本失败: 版本ID不能为空');
      }

      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据执行查询版本，查询ID: ${queryId}, 版本ID: ${versionId}`);

        // 查找模拟版本
        const version = mockVersions.find(v => v.id === versionId);

        // 如果找不到版本，抛出错误
        if (!version) {
          throw new Error(`执行版本失败: 未找到版本 ${versionId}`);
        }

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 返回符合QueryExecution接口的模拟执行结果
        return {
          id: `exec-${Math.random().toString(36).substring(2, 9)}`,
          queryId: queryId,
          executedAt: new Date().toISOString(),
          executionTime: 1500,
          status: 'SUCCESS',
          rowCount: 3,
          errorMessage: null
        };
      }

      // 修改为符合后端API的正确URL和参数格式
      const url = `/api/queries/execute`;

      // 构建请求体，确保version_id字段有效
      const requestBody = {
        queryId,
        version_id: versionId, // 使用version_id作为参数名，而不是versionId
        dataSourceId: parameters?.dataSourceId,
        params: parameters?.params || []
      };

      console.log('执行查询请求体:', requestBody);

      // 使用instance.post代替fetch
      const response = await instance.post(url, requestBody);

      // 处理响应
      return response.data;
    } catch (error) {
      console.error('执行版本失败:', error);
      throw error;
    }
  }
};

// 将API返回的版本数据映射为前端使用的格式
export const mapVersionFromApi = (version: any): QueryVersion => {
  // 输出原始API数据
  console.log('原始版本API数据:', version);

  // 检查多个可能包含SQL内容的字段
  const possibleSqlFields = ['queryText', 'sqlContent', 'sql_content', 'content', 'sql', 'query'];
  let foundSqlContent = '';

  for (const field of possibleSqlFields) {
    if (version[field] && typeof version[field] === 'string') {
      foundSqlContent = version[field];
      console.log(`找到SQL内容字段: ${field}, 内容长度: ${foundSqlContent.length}`);
      break;
    }
  }

  if (!foundSqlContent) {
    console.warn('未找到SQL内容，所有可能的字段都为空', possibleSqlFields);

    // 输出所有字段，帮助调试
    console.log('版本对象所有字段:');
    Object.keys(version).forEach(key => {
      console.log(`${key}: ${typeof version[key]}, ${version[key] ? '有值' : '无值'}`);
    });
  }

  // 处理版本ID，确保不为空
  const versionId = version.id || version.versionId || '';
  if (!versionId) {
    console.warn('版本ID为空，这可能会导致问题');
  }

  // 解析状态，使用字符串常量而非枚举引用
  const status = version.status || version.version_status || 'DRAFT';

  return {
    id: versionId,
    queryId: version.queryId || version.query_id || '',
    versionNumber: version.versionNumber || version.version_number || 1,
    queryText: foundSqlContent,
    sql_content: foundSqlContent,  // 添加兼容性字段
    status: status,
    isActive: version.isActive || version.is_active || false,
    createdAt: version.createdAt || version.created_at || new Date().toISOString(),
    updatedAt: version.updatedAt || version.updated_at || new Date().toISOString(),
    publishedAt: version.publishedAt || version.published_at || '',
    deprecatedAt: version.deprecatedAt || version.deprecated_at || '',
    createdBy: version.createdBy || version.created_by || '',
    updatedBy: version.updatedBy || version.updated_by || '',
    description: version.description || '',
    changeLog: version.changeLog || version.change_log || '',
    dataSourceId: version.dataSourceId || version.data_source_id || '',
  };
};
