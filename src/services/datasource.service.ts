import { getMetadataApiUrl } from '@/services/apiUtils';
import instance from '@/utils/axios';

async updateDataSourceStatus(id: string, status: string, message?: string): Promise<DataSource> {
  try {
    logger.info(`更新数据源状态: ID=${id}, 状态=${status}, 错误消息=${message || 'none'}`);
    // 检查数据源是否存在
    const dataSource = await prisma.dataSource.findUnique({
      where: { id }
    })

    if (!dataSource) {
      throw new DataSourceError(`数据源 ID ${id} 不存在`, 404)
    }

    // 更新数据源状态
    const updatedDataSource = await prisma.dataSource.update({
      where: { id },
      data: { 
        status: status as DataSourceStatus,
        statusMessage: message
      }
    })

    return updatedDataSource
  } catch (error) {
    logger.error(`更新数据源状态失败:`, error)
    throw new DataSourceError(
      error instanceof Error ? error.message : '更新数据源状态失败',
      error instanceof DataSourceError ? error.statusCode : 500
    )
  }
}

/**
 * 获取数据源的所有Schema
 * @param dataSourceId 数据源ID
 */
async getSchemas(dataSourceId: string): Promise<string[]> {
  const url = getMetadataApiUrl('schemas', { dataSourceId });
  
  try {
    console.log(`获取数据源 ${dataSourceId} 的schemas`);
    const response = await instance.get(url);
    
    // 处理不同格式的响应
    const data = response.data;
    
    if (data && data.success && data.data) {
      return data.data;
    } else if (Array.isArray(data)) {
      return data;
    } else {
      console.warn('获取schemas返回未知格式:', data);
      return [];
    }
  } catch (error) {
    console.error('获取schemas出错:', error);
    return [];
  }
}

/**
 * 获取表元数据
 * @param dataSourceId 数据源ID
 * @param schema 指定的schema名称（可选）
 */
async getTableMetadata(dataSourceId: string, schema?: string): Promise<TableMetadata[]> {
  try {
    // 首先获取数据源的schemas
    const schemasResponse = await this.getSchemas(dataSourceId);
    
    if (!schemasResponse || schemasResponse.length === 0) {
      console.warn(`数据源 ${dataSourceId} 没有可用的schemas`);
      return [];
    }
    
    let allTables: TableMetadata[] = [];
    
    // 遍历所有schema获取表
    for (const schemaInfo of schemasResponse) {
      // 如果提供了schema参数，且当前schema不匹配，则跳过
      if (schema && schemaInfo.name !== schema) {
        continue;
      }
      
      const schemaId = schemaInfo.id;
      if (!schemaId) {
        console.warn(`跳过无效的schema: ${schemaInfo.name}`);
        continue;
      }
      
      // 使用API工具函数获取表
      const tablesUrl = getMetadataApiUrl('tables', { schemaId }) + `?_t=${Date.now()}`;
      
      try {
        const response = await instance.get(tablesUrl);
        const data = response.data;
        
        // 处理不同格式的响应
        let tables: TableMetadata[] = [];
        if (data && data.success && data.data) {
          tables = Array.isArray(data.data) ? data.data : [];
        } else if (Array.isArray(data)) {
          tables = data;
        } else {
          console.warn(`获取schema ${schemaInfo.name} 的表返回未知格式:`, data);
          continue;
        }
        
        // 添加schema信息到表
        tables.forEach(table => {
          table.schema = schemaInfo.name;
        });
        
        allTables = [...allTables, ...tables];
      } catch (error) {
        console.error(`获取schema ${schemaInfo.name} 的表出错:`, error);
      }
    }
    
    return allTables;
  } catch (error) {
    console.error('获取表元数据出错:', error);
    throw error;
  }
} 
