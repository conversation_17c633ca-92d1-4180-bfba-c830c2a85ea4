import { getApiBaseUrl } from './query';
import { getQueryApiUrl } from '@/services/apiUtils';
import instance from '@/utils/axios';

// 查询服务状态类型
export type QueryServiceStatus = 'ENABLED' | 'DISABLED';

// 查询服务状态信息
export interface QueryStatusInfo {
  status: QueryServiceStatus;
  disabledReason?: string;
  disabledAt?: string;
  disabledBy?: string;
}

// 禁用查询参数
export interface DisableQueryParams {
  queryId: string;
  reason: string;
}

// 查询状态服务
export const queryStatusService = {
  // 获取查询服务状态
  async getQueryStatus(queryId: string): Promise<QueryStatusInfo> {
    try {
      const apiUrl = getQueryApiUrl('status', { id: queryId });
      const response = await instance.get(apiUrl);
      
      const responseData = response.data;
      const result = responseData.data || responseData;
      
      // 转换为QueryStatusInfo格式
      return {
        status: result.status,
        disabledReason: result.disabledReason || result.disabled_reason,
        disabledAt: result.disabledAt || result.disabled_at,
        disabledBy: result.disabledBy || result.disabled_by
      };
    } catch (error) {
      console.error('获取查询服务状态错误:', error);
      throw error;
    }
  },
  
  // 启用查询服务
  async enableQuery(queryId: string): Promise<QueryStatusInfo> {
    try {
      // 构建启用查询URL - 需要添加自定义路径，因为api-mapping中没有专门的enable配置
      const baseUrl = getQueryApiUrl('detail', { id: queryId });
      const apiUrl = baseUrl.replace(`/${queryId}`, `/${queryId}/enable`);
      
      const response = await instance.post(apiUrl);
      
      const responseData = response.data;
      
      console.log('启用查询服务响应:', responseData);
      
      // 检查API响应格式
      if (!responseData) {
        throw new Error('启用查询服务失败: 空响应');
      }
      
      // 获取结果数据
      const result = responseData.data || responseData;
      
      // 支持简单的成功响应：{"success":true,"code":200,"message":"操作成功","data":true}
      if (responseData.success === true && responseData.data === true) {
        console.log('接收到简单成功响应，返回标准格式的成功结果');
        return {
          status: 'ENABLED', // 直接返回期望的启用状态
          disabledReason: undefined,
          disabledAt: undefined,
          disabledBy: undefined
        };
      }
      
      // 如果result是布尔值true，转换为标准格式
      if (result === true) {
        return {
          status: 'ENABLED',
          disabledReason: undefined,
          disabledAt: undefined,
          disabledBy: undefined
        };
      }
      
      // 转换为QueryStatusInfo格式
      return {
        status: result.status,
        disabledReason: result.disabledReason || result.disabled_reason,
        disabledAt: result.disabledAt || result.disabled_at,
        disabledBy: result.disabledBy || result.disabled_by
      };
    } catch (error) {
      console.error('启用查询服务错误:', error);
      throw error;
    }
  },
  
  // 禁用查询服务
  async disableQuery(params: DisableQueryParams): Promise<QueryStatusInfo> {
    try {
      if (!params || !params.queryId) {
        throw new Error('禁用查询服务失败: 缺少查询ID');
      }
      
      const requestBody = {
        reason: params.reason || '手动禁用查询'
      };
      
      console.log(`准备禁用查询服务 [${params.queryId}]，参数:`, requestBody);
      
      // 构建禁用查询URL - 需要添加自定义路径，因为api-mapping中没有专门的disable配置
      const baseUrl = getQueryApiUrl('detail', { id: params.queryId });
      const apiUrl = baseUrl.replace(`/${params.queryId}`, `/${params.queryId}/disable`);
      console.log('禁用查询URL:', apiUrl);
      
      const response = await instance.post(apiUrl, requestBody);
      
      const responseData = response.data;
      
      console.log('禁用查询服务响应:', responseData);
      
      // 检查API响应格式
      if (!responseData) {
        throw new Error('禁用查询服务失败: 空响应');
      }
      
      // 获取结果数据
      const result = responseData.data || responseData;
      
      // 支持简单的成功响应：{"success":true,"code":200,"message":"操作成功","data":true}
      if (responseData.success === true && responseData.data === true) {
        console.log('接收到简单成功响应，返回标准格式的成功结果');
        return {
          status: 'DISABLED', // 直接返回期望的禁用状态
          disabledReason: requestBody.reason,
          disabledAt: new Date().toISOString(),
          disabledBy: 'current_user' // 理想情况下应从用户上下文获取
        };
      }
      
      // 验证结果数据 - 标准格式结果检查
      if (!result || (typeof result !== 'boolean' && !result.status)) {
        console.error('禁用查询服务返回了无效的数据格式:', responseData);
        throw new Error('禁用查询服务失败: 无效的响应格式');
      }
      
      // 如果result是布尔值true，转换为标准格式
      if (result === true) {
        return {
          status: 'DISABLED',
          disabledReason: requestBody.reason,
          disabledAt: new Date().toISOString(),
          disabledBy: 'current_user'
        };
      }
      
      // 转换为QueryStatusInfo格式 - 标准对象响应
      return {
        status: result.status,
        disabledReason: result.disabledReason || result.disabled_reason,
        disabledAt: result.disabledAt || result.disabled_at,
        disabledBy: result.disabledBy || result.disabled_by
      };
    } catch (error) {
      console.error('禁用查询服务错误:', error);
      
      // 确保返回有意义的错误信息
      if (error instanceof Error) {
        throw error; // 直接重抛出有明确信息的错误
      } else {
        throw new Error(`禁用查询服务失败: ${error}`);
      }
    }
  },
  
  // 检查查询服务是否已启用
  async isQueryEnabled(queryId: string): Promise<boolean> {
    try {
      const statusInfo = await this.getQueryStatus(queryId);
      return statusInfo.status === 'ENABLED';
    } catch (error) {
      console.error('检查查询服务状态错误:', error);
      // 出错时默认为禁用状态，确保安全
      return false;
    }
  }
};

export default queryStatusService;
