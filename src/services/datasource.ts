import type {
    ConnectionTestResult,
    CreateDataSourceParams as DataSourceInput,
    DataSource,
    DataSourceStats,
    DataSourceStatus,
    DataSourceType,
    MetadataSyncResult,
    SyncFrequency,
    SyncMetadataParams,
    TestConnectionParams,
    UpdateDataSourceParams
} from '@/types/datasource'
import type {ColumnMetadata, TableMetadata, TableRelationship} from '@/types/metadata'
import {getApiUrl, getDataSourceApiUrl, getMetadataApiUrl} from './apiUtils'
// 导入新的mock系统中的数据源数据，使用type关键字导入类型
import {mockDataSources as mockDataSourcesOriginal} from '@/mock/data/datasource'
import instance from "@/utils/axios";

// 表数据预览结果接口
export interface TableDataPreviewResult {
  data: any[];
  columns: { name: string, type: string }[];
  page: number;
  size: number;
  total: number;
  totalPages: number;
}

// 用于分页和筛选的查询参数
interface QueryParams<T = any> {
  page?: number;
  size?: number;
  name?: string;
  type?: DataSourceType;
  status?: DataSourceStatus;
  [key: string]: any;
}

// 数据源分页信息
interface DataSourcePagination {
  page: number;
  size: number;
  total: number;
  totalPages: number;
}

// 数据源列表响应
interface DataSourcesResponse {
  items: DataSource[];
  meta?: {
    page: number;
    size: number;
    total: number;
    success: boolean;
  };
}

// 将mock数据源类型转换为服务使用的类型
const mockDataSources: DataSource[] = mockDataSourcesOriginal.map(ds => ({
  id: ds.id,
  name: ds.name,
  description: ds.description || '', // 确保description不为undefined
  type: ds.type as DataSourceType, // 类型转换
  host: ds.host || 'localhost',
  port: ds.port || 3306,
  databaseName: ds.databaseName || '',
  database: ds.databaseName, // 保持兼容性
  username: ds.username || '',
  status: ds.status as DataSourceStatus, // 类型转换
  syncFrequency: ds.syncFrequency || 'manual' as SyncFrequency,
  lastSyncTime: ds.lastSyncTime,
  createdAt: ds.createdAt,
  updatedAt: ds.updatedAt,
  isActive: ds.isActive
}));

// 检查是否启用mock模式
const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true'; // 根据环境变量决定是否使用mock
console.log('数据源服务 - Mock模式:', USE_MOCK ? '已启用' : '已禁用')

// 模拟表列表数据
const mockTables = ['users', 'products', 'orders', 'customers', 'employees', 'inventory'];

// API 基础路径 - 使用apiUtils中的函数
const getDataSourceApiBaseUrl = () => {
  return getApiUrl(); // 使用统一的API URL获取函数
};

// 元数据API基础路径 - 统一使用apiUtils
const getMetadataApiBaseUrl = () => {
  return getApiUrl(); // 使用统一的API URL获取函数
};

// 处理统一响应格式
const handleResponse = async <T>(response: Response): Promise<T> => {
  try {
    const data = await response.json();
    // 处理后端统一响应格式
    if (data.success === false) {
      throw {
        success: false,
        error: data.error || {
          statusCode: 400,
          code: 'API_ERROR',
          message: data.message || '请求失败',
          details: null
        }
      };
    }
    return data.success === undefined ? data : data.data;
  } catch (error) {
    console.error('处理API响应错误:', error);
    throw error;
  }
}

// 统一错误处理函数
const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
  console.error('API错误:', error);

  // 如果已经是标准格式的错误，直接返回
  if (error && error.success === false && error.error) {
    return error;
  }

  // 构造标准错误响应
  return {
    success: false,
    error: {
      statusCode: error?.status || error?.statusCode || 500,
      code: error?.code || 'UNKNOWN_ERROR',
      message: error?.message || defaultMessage,
      details: error?.details || error?.stack
    }
  };
}

// 将后端返回的数据源对象转换为前端所需的格式
const adaptDataSource = (source: any): DataSource => {
  // 如果没有获取到数据，返回空对象
  if (!source) {
    return {} as DataSource;
  }

    // 处理connectionParams，确保它是一个对象
    let connectionParams = source.connectionParams || {};

    // 如果是字符串，尝试解析
    if (typeof connectionParams === 'string') {
        try {
            connectionParams = JSON.parse(connectionParams);
        } catch (e) {
            console.error('解析connectionParams失败:', e);
            connectionParams = {};
        }
    }

    // 确保isAuthRequired字段正确处理
    const isAuthRequired = source.isAuthRequired === true;

    console.log('adaptDataSource - 处理isAuthRequired字段:', source.isAuthRequired, '转换后:', isAuthRequired);

  return {
    id: source.id,
    name: source.name,
    description: source.description || '',
    type: source.type as DataSourceType, // 后端现在返回小写的类型，不需要转换
    host: source.host,
    port: source.port,
    databaseName: source.databaseName || source.database || '', // 优先使用databaseName，其次使用database
    database: source.databaseName || source.database || '', // 同时记录database字段确保两边兼容
    schema: source.schema || '', // 添加schema字段
    username: source.username,
    // 密码通常不会返回
    status: source.status as DataSourceStatus,
    syncFrequency: source.syncFrequency as SyncFrequency,
    lastSyncTime: source.lastSyncTime,
    createdAt: source.createdAt,
    updatedAt: source.updatedAt,
    // 其他可选字段
    errorMessage: source.errorMessage,
      connectionParams: connectionParams,
    isActive: source.isActive,
      // 授权相关字段
      isAuthRequired: isAuthRequired,
    // 额外字段处理
    metadata: source.metadata
  }
}

// 数据源服务
export const dataSourceService = {
  /**
   * 获取数据源列表
   * @param params 查询参数
   * @returns 数据源列表及分页信息
   */
  async getDataSources(
    params?: QueryParams<DataSourcePagination>,
  ): Promise<DataSourcesResponse> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据源数据');
        return this.getMockDataSources(params);
      }

      console.log('[DataSourceService] 开始获取数据源列表');

      const url = getDataSourceApiUrl('list');
      const queryParams = new URLSearchParams();

      // 添加查询参数
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        });
      }

      const finalUrl = `${url}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

      // 发送请求
      console.log('[DataSourceService] 请求URL:', finalUrl);
      const response = await instance.get(finalUrl);
      console.log('[DataSourceService] 获取数据源列表响应:', response);

      // 从response.data获取真正的响应内容
      const responseData = response.data;

      // 检查响应是否成功
      if (responseData && typeof responseData === 'object' && 'success' in responseData && responseData.success === false) {
        throw new Error((responseData as any).message || '获取数据源列表失败');
      }

      // 处理各种可能的响应格式
      const result: DataSourcesResponse = { items: [] };

      if (Array.isArray(responseData)) {
        // 响应直接是数组格式
        console.log(`[DataSourceService] 成功获取${responseData.length}个数据源 (数组格式)`);
        result.items = responseData.map(adaptDataSource);
        // 构建元数据
        result.meta = {
          page: params?.page || 1,
          size: params?.size || responseData.length,
          total: responseData.length,
          success: true
        };
      } else if (responseData && typeof responseData === 'object') {
        // 对象格式处理
        if ('data' in responseData && Array.isArray(responseData.data)) {
          // 标准格式: { success: true, data: [...] }
          console.log(`[DataSourceService] 成功获取${responseData.data.length}个数据源 (标准对象格式)`);
          result.items = responseData.data.map(adaptDataSource);
          // 构建元数据
          result.meta = {
            page: responseData.page || params?.page || 1,
            size: responseData.size || params?.size || responseData.data.length,
            total: responseData.total || responseData.data.length,
            success: true
          };
        } else if ('items' in responseData && Array.isArray(responseData.items)) {
          // 直接返回了包含items的对象
          console.log(`[DataSourceService] 成功获取${responseData.items.length}个数据源 (items对象格式)`);
          result.items = responseData.items.map(adaptDataSource);
          result.meta = responseData.meta || {
            page: responseData.page || params?.page || 1,
            size: responseData.size || params?.size || responseData.items.length,
            total: responseData.total || responseData.items.length,
            success: true
          };
        } else {
          // 查找可能的数据源数组字段
          console.warn('[DataSourceService] 获取数据源返回未知格式，尝试提取数组:', responseData);
          let dataSourceArray: any[] = [];

          // 查找第一个数组类型的字段
          for (const key in responseData) {
            if (Array.isArray(responseData[key])) {
              dataSourceArray = responseData[key];
              console.log(`[DataSourceService] 从字段 "${key}" 找到数据源数组: ${dataSourceArray.length}个`);
              break;
            }
          }

          if (dataSourceArray.length > 0) {
            result.items = dataSourceArray.map(adaptDataSource);
            result.meta = {
              page: params?.page || 1,
              size: params?.size || dataSourceArray.length,
              total: dataSourceArray.length,
              success: true
            };
          } else {
            console.error('[DataSourceService] 无法从响应中提取数据源数组');
            result.items = [];
            result.meta = {
              page: 1,
              size: 10,
              total: 0,
              success: false
            };
          }
        }
      }

      console.log(`[DataSourceService] 成功获取${result.items.length}个数据源`);
      return result;
    } catch (error) {
      console.error('获取数据源列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取模拟数据源列表
   * @param params 查询参数
   * @returns 模拟数据源列表及分页信息
   */
  getMockDataSources(params?: QueryParams<DataSourcePagination>): DataSourcesResponse {
    console.log('[Mock] 获取模拟数据源列表，参数：', params);
    // 过滤数据 - 确保安全地使用mockDataSources
    let filteredData = Array.isArray(mockDataSources) ? [...mockDataSources] : [];

    if (params?.name) {
      filteredData = filteredData.filter((item) =>
        item.name.toLowerCase().includes(params.name!.toLowerCase())
      );
      console.log(`[Mock] 按名称过滤，筛选条件："${params.name}"，剩余：${filteredData.length}项`);
    }

    if (params?.type) {
      filteredData = filteredData.filter((item) => item.type === params.type);
      console.log(`[Mock] 按类型过滤，筛选条件："${params.type}"，剩余：${filteredData.length}项`);
    }

    if (params?.status) {
      filteredData = filteredData.filter((item) =>
        item.status.toLowerCase() === params.status!.toLowerCase()
      );
      console.log(`[Mock] 按状态过滤，筛选条件："${params.status}"，剩余：${filteredData.length}项`);
    }

    // 分页处理
    const page = params?.page || 1;
    const size = params?.size || 10;
    const start = (page - 1) * size;
    const end = start + size;
    const paginatedData = filteredData.slice(start, end);

    console.log(`[Mock] 分页处理：第${page}页，每页${size}条，总计${filteredData.length}条，当前页${paginatedData.length}条`);

    // 输出详细的返回数据
    console.log('[Mock] 返回数据源列表的前3条：',
      paginatedData.slice(0, 3).map(item => ({
        id: item.id,
        name: item.name,
        type: item.type,
        status: item.status
      }))
    );

    return {
      items: paginatedData,
      meta: {
        total: filteredData.length,
        page,
        size,
        success: true
      }
    };
  },

  // 获取单个数据源详情
  async getDataSource(id: string): Promise<DataSource> {
    try {
      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回数据源详情');
        const mockDataSources = this.getMockDataSources();
        const mockDataSource = mockDataSources.items.find(ds => ds.id === id);

        if (!mockDataSource) {
          throw new Error(`未找到数据源: ${id}`);
        }

        return mockDataSource;
      }

      console.log(`[DataSourceService] 获取数据源详情, id: ${id}`);

      const url = getDataSourceApiUrl('detail').replace('{id}', id);
      console.log('[DataSourceService] 请求URL:', url);

      const response = await instance.get(url);
      console.log('[DataSourceService] 获取数据源详情响应:', response);

      // 从response.data获取真正的响应内容
      const responseData = response.data;

      // 检查响应是否成功
      if (responseData && typeof responseData === 'object' && 'success' in responseData && responseData.success === false) {
        throw new Error((responseData as any).message || `获取数据源失败: ${id}`);
      }

      // 根据可能的响应格式提取数据
      let dataSource: any;

      if (responseData && typeof responseData === 'object') {
        if ('data' in responseData) {
          // 标准格式: { success: true, data: {...} }
          dataSource = responseData.data;
        } else {
          // 假设responseData本身就是数据源对象
          dataSource = responseData;
        }
      } else {
        throw new Error(`获取数据源失败: 响应格式不正确`);
      }

      // 适配响应格式
      return adaptDataSource(dataSource);
    } catch (error) {
      console.error(`[DataSourceService] 获取数据源详情失败:`, error);
      throw error;
    }
  },

  // 创建数据源
  async createDataSource(data: DataSourceInput): Promise<DataSource> {
    try {
      // 如果启用了Mock模式，返回模拟数据
      if (USE_MOCK) {
        console.log('创建模拟数据源:', data);

        // 创建新的模拟数据源
        const newId = `ds-${mockDataSources.length + 1}`;
        const newDataSource: DataSource = {
          id: newId,
          name: data.name,
          description: data.description || '',
          type: data.type,
          host: data.host,
          port: data.port,
          databaseName: data.databaseName,
          database: data.database || data.databaseName,
          schema: data.schema || '',  // 添加schema字段
          username: data.username,
          status: 'active',
          syncFrequency: data.syncFrequency,
          lastSyncTime: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 将新数据源添加到模拟列表
        mockDataSources.push(newDataSource);

        return newDataSource;
      }

      // 处理请求数据
      const requestBody = {
        name: data.name,
        description: data.description,
        // 类型使用原始值，后端会处理大小写
        type: data.type?.toLowerCase(),
        host: data.host,
        port: data.port,
        databaseName: data.databaseName,
        username: data.username,
        password: data.password,
        schema: data.schema,  // 添加schema字段
        connectionParams: data.connectionParams,
        // 其他可能的字段
        syncFrequency: data.syncFrequency || 'manual'
      };

      console.log('[DataSourceService] 创建数据源，发送数据:', requestBody);

      // 使用apiUtils构建URL
      const url = getDataSourceApiUrl('create');
      const response = await instance.post(url, requestBody);

      // 处理响应
      if (response.success === false) {
        throw new Error(response.message || '创建数据源失败');
      }

      // 提取数据并适配格式
      const dataSource = response.data;
      console.log('[DataSourceService] 创建数据源成功:', dataSource);

      return adaptDataSource(dataSource);
    } catch (error) {
      console.error('[DataSourceService] 创建数据源失败:', error);
      throw error;
    }
  },

  // 更新数据源
  async updateDataSource(data: UpdateDataSourceParams): Promise<DataSource> {
    try {
      if (!data.id) {
        throw new Error('缺少数据源ID');
      }

      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据更新数据源');
        // 模拟更新成功后返回数据源对象
        return {
          id: data.id,
          name: data.name,
          type: data.type || 'unknown',  // 修复字段名：从dataSourceType改为type
          status: 'active',
          connectionParams: {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      }

      console.log(`[DataSourceService] 更新数据源, id: ${data.id}`);

      // 准备请求数据
      // 将connectionParams对象序列化为字符串，避免Java后端处理嵌套对象时出错
      let requestData: Record<string, any> = {
        id: data.id,
        name: data.name,
        description: data.description,
        type: data.type,  // 修复字段名：从dataSourceType改为type
        host: data.host,
        port: data.port,
        databaseName: data.databaseName || data.database,
        username: data.username,
        password: data.password,
        schema: data.schema,  // 添加schema字段
        syncFrequency: data.syncFrequency,  // 添加同步频率字段
        connectionParams: typeof data.connectionParams === 'object' ?
          JSON.stringify(data.connectionParams) : data.connectionParams
      };

      // 过滤掉undefined和null值
      requestData = Object.fromEntries(
        Object.entries(requestData).filter(([_, v]) => v !== undefined && v !== null)
      );

      console.log('[DataSourceService] 发送的更新数据:', requestData);

      const url = getDataSourceApiUrl('update').replace('{id}', data.id);
      console.log('[DataSourceService] 请求URL:', url);

      // 发送PUT请求
      const response = await instance.put(url, requestData);
      console.log('[DataSourceService] 更新数据源响应:', response);

      // 从response.data获取真正的响应内容
      const responseData = response.data;

      // 检查响应是否成功
      if (responseData && typeof responseData === 'object' && 'success' in responseData && responseData.success === false) {
        throw new Error((responseData as any).message || '更新数据源失败');
      }

      // 根据可能的响应格式提取数据
      let updatedDataSource: any;

      if (responseData && typeof responseData === 'object') {
        if ('data' in responseData) {
          // 标准格式: { success: true, data: {...} }
          updatedDataSource = responseData.data;
        } else {
          // 假设responseData本身就是数据源对象
          updatedDataSource = responseData;
        }
      } else {
        throw new Error(`更新数据源失败: 响应格式不正确`);
      }

      // 适配响应格式
      return adaptDataSource(updatedDataSource);
    } catch (error) {
      console.error(`[DataSourceService] 更新数据源失败:`, error);
      throw error;
    }
  },

  // 删除数据源
  async deleteDataSource(id: string): Promise<boolean> {
    try {
      // 如果启用了Mock模式，从模拟列表中删除
      if (USE_MOCK) {
        console.log('[DataSourceService] 删除模拟数据源, id:', id);
        const index = mockDataSources.findIndex(ds => ds.id === id);

        if (index === -1) {
          throw new Error(`未找到ID为${id}的数据源`);
        }

        // 从模拟列表中删除
        mockDataSources.splice(index, 1);

        return true;
      }

      const url = getDataSourceApiUrl('delete', { id });
      const response = await instance.delete(url);

      // 处理响应
      if (response.success === false) {
        throw new Error(response.message || `删除数据源失败: ${id}`);
      }

      console.log('[DataSourceService] 删除数据源成功:', id);
      return true;
    } catch (error) {
      console.error(`[DataSourceService] 删除数据源失败 [${id}]:`, error);
      throw error;
    }
  },

  // 测试连接 - 新的数据源连接测试
  async testConnection(params: TestConnectionParams): Promise<ConnectionTestResult> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回连接测试结果');
        // 模拟1秒的延迟，模拟真实连接测试的时间
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 始终返回成功的连接测试结果
        return {
          success: true,
          message: '连接测试成功',
          details: {
            connectionTime: '100ms',
            serverVersion: `Mock ${params.type?.toUpperCase() || 'DATABASE'} Server 5.7.0`,
            dbName: params.databaseName || params.database || 'mock_database'
          }
        };
      }

      const url = getDataSourceApiUrl('test', { id: params.id || 'new' });
      console.log('[DataSourceService] 测试连接, URL:', url);
      const response = await instance.post(url, params);

      console.log('[DataSourceService] 测试连接原始响应:', response);

      // 处理外层和内层响应结构
      // 外层响应: {success: true, code: 200, message: "操作成功", data: {...}}
      // 内层响应: data: {success: true/false, message: "...", details: {...}}

      if (!response) {
        return {
          success: false,
          message: '连接测试失败: 无响应',
          details: null
        };
      }

      // 检查外层响应是否成功
      if (!response.success) {
        return {
          success: false,
          message: response.message || '连接测试失败: 请求错误',
          details: response.details || response
        };
      }

      // 外层成功，检查内层data
      if (response.data) {
        // 使用内层data中的success字段作为最终结果
        return {
          success: response.data.success === true, // 显式转换为布尔值
          message: response.data.message || (response.data.success ? '连接成功' : '连接失败'),
          details: response.data.details || response.data
        };
      }

      // 响应格式异常
      return {
        success: false,
        message: '连接测试结果格式异常',
        details: response
      };
    } catch (error) {
      console.error('[DataSourceService] 测试连接失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '连接测试失败，请检查连接详情后重试',
        details: error
      }
    }
  },

  // 同步元数据
  async syncMetadata(params: SyncMetadataParams): Promise<MetadataSyncResult> {
    try {
      console.log('[DataSourceService] 开始同步元数据，参数:', params);

      if (!params.id) {
        console.error('[DataSourceService] 同步元数据失败: 未提供数据源ID');
        return {
          success: false,
          message: '同步元数据失败: 未提供数据源ID'
        };
      }

      // 验证数据源是否存在
      try {
        console.log('[DataSourceService] 验证数据源是否存在，ID:', params.id);
        // 使用apiUtils构建URL，不使用getDataSource避免递归调用
        const checkUrl = getDataSourceApiUrl('detail', { id: params.id });
        const checkResponse = await instance.get(checkUrl);

        if (checkResponse.success === false) {
          console.error('[DataSourceService] 数据源不存在，无法同步元数据:', params.id);
          return {
            success: false,
            message: `同步元数据失败: 数据源不存在 (${params.id})`
          };
        }

        console.log('[DataSourceService] 数据源存在，继续同步操作');
      } catch (checkError) {
        console.error('[DataSourceService] 验证数据源存在性失败:', checkError);
        return {
          success: false,
          message: `同步元数据失败: 验证数据源失败 (${checkError instanceof Error ? checkError.message : String(checkError)})`
        };
      }

      if (USE_MOCK) {
        console.log('执行模拟元数据同步:', params);

        // 查找数据源
        const mockDataSource = mockDataSources.find(ds => ds.id === params.id);
        if (!mockDataSource) {
          throw new Error(`未找到ID为${params.id}的数据源`);
        }

        // 延迟1-3秒模拟同步过程
        await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));

        // 返回同步结果
        return {
          success: true,
          syncHistoryId: `sync-${Date.now()}`,
          message: '元数据同步完成',
          tablesCount: Math.floor(Math.random() * 20) + 5,
          viewsCount: Math.floor(Math.random() * 10) + 1,
          syncDuration: Math.floor(Math.random() * 5000) + 1000,
          lastSyncTime: new Date().toISOString()
        };
      }

      const url = getMetadataApiUrl('sync', { dataSourceId: params.id });
      console.log('[DataSourceService] 发送元数据同步请求，URL:', url);
      const response = await instance.post(url, params.filters || {});

      console.log('[DataSourceService] 元数据同步响应:', response);

      // 处理响应数据
      if (response.success === true && response.data) {
        // 如果是标准响应格式
        const syncResult = response.data;

        // 构建标准同步结果
        return {
          success: syncResult.success || true,
          message: syncResult.message || '同步完成',
          tablesCount: syncResult.tablesCount,
          viewsCount: syncResult.viewsCount,
          syncDuration: syncResult.syncDuration,
          lastSyncTime: syncResult.endTime || new Date().toISOString(),
          syncHistoryId: syncResult.syncId
        };
      }

      // 如果响应本身就是同步结果
      if (response.success !== undefined) {
        return {
          success: response.success,
          message: response.message || '同步完成',
          tablesCount: response.tablesCount,
          viewsCount: response.viewsCount,
          syncDuration: response.syncDuration,
          lastSyncTime: response.lastSyncTime || new Date().toISOString(),
          syncHistoryId: response.syncHistoryId
        };
      }

      throw new Error('同步元数据失败：无效的响应格式');
    } catch (error) {
      console.error('同步元数据错误:', error);
      throw error;
    }
  },

  // 获取数据源统计信息
  async getDataSourceStats(id: string): Promise<DataSourceStats> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回数据源统计信息');

        // 查找数据源
        const dataSource = mockDataSources.find(ds => ds.id === id);
        if (!dataSource) {
          throw new Error(`未找到ID为${id}的数据源`);
        }

        // 返回模拟的数据源统计信息
        return {
          dataSourceId: id,
          tablesCount: Math.floor(Math.random() * 20) + 5,
          viewsCount: Math.floor(Math.random() * 10) + 1,
          totalRows: Math.floor(Math.random() * 100000),
          totalSize: Math.floor(Math.random() * 100) + ' MB',
          lastUpdate: new Date().toISOString(),
          queriesCount: Math.floor(Math.random() * 1000),
          connectionPoolSize: Math.floor(Math.random() * 20),
          activeConnections: Math.floor(Math.random() * 10),
          avgQueryTime: Math.floor(Math.random() * 100) + 'ms',
          totalTables: Math.floor(Math.random() * 20) + 5,
          totalViews: Math.floor(Math.random() * 10) + 1,
          totalQueries: Math.floor(Math.random() * 1000),
          avgResponseTime: Math.floor(Math.random() * 100),
          peakConnections: Math.floor(Math.random() * 10) + 1
        };
      }

      const url = getDataSourceApiUrl('stats', { id });
      const response = await instance.get(url);

      if (!response.ok) {
        throw new Error(`获取数据源统计信息失败: ${response.statusText}`);
      }

      const result = await handleResponse<any>(response);

      // 转换为前端所需的格式
      return {
        dataSourceId: id,
        tablesCount: result.tablesCount || 0,
        viewsCount: result.viewsCount || 0,
        totalRows: result.totalRows || 0,
        totalSize: result.totalSize || '0 MB',
        lastUpdate: result.lastUpdate || new Date().toISOString(),
        queriesCount: result.queriesCount || 0,
        connectionPoolSize: result.connectionPoolSize || 0,
        activeConnections: result.activeConnections || 0,
        avgQueryTime: result.avgQueryTime || '0ms',
        totalTables: result.totalTables || result.tablesCount || 0,
        totalViews: result.totalViews || result.viewsCount || 0,
        totalQueries: result.totalQueries || result.queriesCount || 0,
        avgResponseTime: result.avgResponseTime || 0,
        peakConnections: result.peakConnections || 0
      };
    } catch (error) {
      console.error(`获取数据源${id}统计信息错误:`, error);
      throw error;
    }
  },

  // 获取数据源模式
  async getSchemas(dataSourceId: string, options: { groupBy?: string } = {}): Promise<any[]> {
    try {
      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回schema列表');
        return ['public', 'dbo', 'test', 'information_schema'];
      }

      const url = getMetadataApiUrl('schemas', { dataSourceId });
      const queryParams = new URLSearchParams();
      if (options.groupBy) {
        queryParams.append('groupBy', options.groupBy);
      }

      const finalUrl = `${url}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      console.log('[DataSourceService] 获取schemas的URL:', finalUrl);

      // 使用 instance.get 发送请求
      const response = await instance.get(finalUrl);
      console.log(`[DataSourceService] 获取schema的响应数据:`, response);

      // 从response.data获取真正的响应内容
      const responseData = response.data;

      // 处理各种可能的响应格式
      let schemas: any[] = [];

      // 检查响应是否成功
      if (responseData && typeof responseData === 'object' && 'success' in responseData && responseData.success === false) {
        throw new Error((responseData as any).message || '获取schema列表失败');
      }

      // 确定schemas数据的位置
      if (Array.isArray(responseData)) {
        // 响应直接是数组格式
        console.log(`[DataSourceService] 成功获取${responseData.length}个schema (数组格式)`);
        schemas = responseData;
      } else if (responseData && typeof responseData === 'object') {
        if ('data' in responseData && Array.isArray(responseData.data)) {
          // 标准格式: { success: true, data: [...] }
          console.log(`[DataSourceService] 成功获取${responseData.data.length}个schema (标准对象格式)`);
          schemas = responseData.data;
        } else {
          // 其他对象格式，尝试找出数组字段
          console.warn('[DataSourceService] 获取schema返回未知格式，尝试提取数组:', responseData);
          // 查找第一个数组类型的字段
          for (const key in responseData) {
            if (Array.isArray(responseData[key])) {
              schemas = responseData[key];
              console.log(`[DataSourceService] 从字段 "${key}" 找到schemas数组: ${schemas.length}个`);
              break;
            }
          }
        }
      }

      // 没有找到任何有效数据，返回空数组
      if (schemas.length === 0) {
        console.warn('[DataSourceService] 未从响应中获取到有效的schemas数据');
      } else {
        console.log(`[DataSourceService] 最终获取到${schemas.length}个schema`);
      }

      // 返回完整的schema对象，如果是字符串则转为对象
      return schemas.map((schema: any) => {
        if (typeof schema === 'string') {
          return {
            id: schema,
            name: schema,
            value: schema
          };
        }
        // 确保schema对象有value字段，用于选择器组件
        if (!schema.value && (schema.id || schema.name)) {
          schema.value = schema.id || schema.name;
        }
        return schema; // 保留完整的对象结构
      });
    } catch (error) {
      console.error(`[DataSourceService] 获取数据源${dataSourceId}的schema列表失败:`, error);
      return [];
    }
  },

  // 获取表元数据
  async getTableMetadata(dataSourceId: string, tableName?: string): Promise<TableMetadata[] | TableMetadata> {
    try {
      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回表元数据');
        const mockTablesList = this.getMockTablesList(dataSourceId);

        if (tableName) {
          const mockTable = mockTablesList.find(table => table.name === tableName);
          return mockTable || this.getMockTableMetadata(dataSourceId, tableName);
        }

        return mockTablesList;
      }

      // 构建API URL
      let url: string;

      if (tableName) {
        // 如果指定了表名，查询特定的表
        console.log(`[DataSourceService] 查询数据源 ${dataSourceId} 的表 ${tableName}`);
        url = getMetadataApiUrl('schemas', { dataSourceId });
      } else {
        // 否则获取所有表
        console.log(`[DataSourceService] 查询数据源 ${dataSourceId} 的所有表`);
        url = getMetadataApiUrl('schemas', { dataSourceId });
      }

      console.log('[DataSourceService] 请求表元数据的URL:', url);

      // 使用instance.get替代fetch发送请求
      const response = await instance.get(url);
      // 响应数据在response.data中
      const responseData = response.data;

      // 检查响应是否成功
      if (responseData && typeof responseData === 'object' && 'success' in responseData && responseData.success === false) {
        console.error('[DataSourceService] 获取表元数据失败:', responseData);
        throw new Error((responseData as any).message || '获取表元数据失败');
      }

      // 处理数据，支持多种响应格式
      let schemas: any[] = [];

      if (Array.isArray(responseData)) {
        // 直接返回数组格式
        schemas = responseData;
      } else if (responseData && typeof responseData === 'object' && 'data' in responseData && Array.isArray(responseData.data)) {
        // 返回 { success: true, data: [...] } 格式
        schemas = responseData.data;
      } else {
        console.error('[DataSourceService] 返回的schemas格式不正确:', responseData);
        throw new Error('获取表元数据失败: 返回格式不正确');
      }

      console.log(`[DataSourceService] 获取到${schemas.length}个schemas`);

      // 收集所有表
      const tables: TableMetadata[] = [];

      // 遍历所有schemas，获取其中的表
      for (const schema of schemas) {
        const schemaId = schema.id;
        const schemaName = schema.name;

        if (!schemaId) {
          console.warn(`[DataSourceService] schema缺少ID, 跳过:`, schema);
          continue;
        }

        // 获取schema下的表
        const tablesUrl = getMetadataApiUrl('tables', { schemaId });

        try {
          console.log(`[DataSourceService] 获取schema ${schemaName}的表, URL:`, tablesUrl);

          // 使用instance.get替代fetch
          const tablesResponse = await instance.get(tablesUrl);
          const tablesData = tablesResponse.data;

          // 检查响应是否成功
          if (tablesData && typeof tablesData === 'object' && 'success' in tablesData && tablesData.success === false) {
            console.warn(`[DataSourceService] 获取schema ${schemaName}的表失败:`, tablesData.message);
            continue;
          }

          // 处理数据，支持多种响应格式
          let schemaTables: any[] = [];

          if (Array.isArray(tablesData)) {
            // 直接返回数组格式
            schemaTables = tablesData;
          } else if (tablesData && typeof tablesData === 'object' && 'data' in tablesData && Array.isArray(tablesData.data)) {
            // 返回 { success: true, data: [...] } 格式
            schemaTables = tablesData.data;
          } else {
            console.warn(`[DataSourceService] schema ${schemaName}的表格式不正确:`, tablesData);
            continue;
          }

          console.log(`[DataSourceService] 获取到schema ${schemaName}的${schemaTables.length}张表`);

          // 将表添加到结果中，并添加schema信息
          schemaTables.forEach(table => {
            tables.push({
              ...table,
              schema: schemaName,
              schemaId: schemaId
            });
          });
        } catch (error) {
          console.error(`[DataSourceService] 获取schema ${schemaName}的表失败:`, error);
        }
      }

      console.log(`[DataSourceService] 总共获取到${tables.length}张表`);

      // 如果查询特定表名
      if (tableName) {
        console.log(`[DataSourceService] 查询数据源 ${dataSourceId} 的表 ${tableName}`);

        // 寻找匹配表名的表
        const matchingTable = tables.find(table => table.name === tableName);

        if (matchingTable) {
          return matchingTable;
        }

        // 如果在现有的表结构中未找到匹配的表，尝试构建最小化的表结构
        console.warn(`[DataSourceService] 未在表列表中找到表 ${tableName}，将尝试直接获取表详情`);

        // 这里改为直接调用已有的getSingleTableMetadata方法
        try {
          return await this.getSingleTableMetadata(dataSourceId, tableName);
        } catch (error) {
          console.error(`[DataSourceService] 获取单个表元数据失败:`, error);
          throw error;
        }
      }

      return tables;
    } catch (error) {
      console.error(`获取表元数据失败:`, error);
      throw error;
    }
  },

  // 新增: 获取模拟数据表列表
  getMockTablesList(dataSourceId: string): TableMetadata[] {
    console.log(`[DataSourceService] 生成数据源 ${dataSourceId} 的模拟表列表`);
    return mockTables.map(tableName => {
      return {
        id: `table_${tableName}_${Date.now()}`,
        name: tableName,
        type: 'TABLE',
        comment: `${tableName} table`,
        schema: 'public',
        columns: Array(8).fill(0).map((_, i) => {
          return {
            name: `column_${i+1}`,
            type: i === 0 ? 'INTEGER' : (i === 1 ? 'VARCHAR' : (i === 2 ? 'TEXT' : (i === 3 ? 'BOOLEAN' : 'TIMESTAMP'))),
            nullable: i > 0,
            primaryKey: i === 0,
            foreignKey: false,
            unique: i === 0,
            autoIncrement: i === 0,
            comment: i === 0 ? 'Primary key' : `Regular column ${i+1}`
          }
        })
      };
    });
  },

  // 获取单个表元数据（内部方法）
  async getSingleTableMetadata(dataSourceId: string, tableName: string): Promise<TableMetadata> {
    try {
      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回表元数据');
        // 返回模拟数据
        return this.getMockTableMetadata(dataSourceId, tableName);
      }

      // 验证参数，防止使用undefined作为表名
      if (!tableName || tableName === 'undefined') {
        console.error('[DataSourceService] 请求表元数据时提供的表名无效:', tableName);
        throw new Error('获取表元数据失败: 无效的表名');
      }

      // 获取所有表，找到匹配的表ID
      const tables = await this.getTableMetadata(dataSourceId) as TableMetadata[];
      const targetTable = tables.find(table => table.name === tableName);

      if (!targetTable || !targetTable.id) {
        console.error(`[DataSourceService] 未找到表 ${tableName} 的ID`);
        throw new Error(`获取表元数据失败: 未找到表 ${tableName}`);
      }

      const tableId = targetTable.id;
      console.log(`[DataSourceService] 找到表ID: ${tableId} for 表: ${tableName}`);

      // 使用表ID获取表详情
      const url = getMetadataApiUrl('table', { tableId }) + `?_t=${Date.now()}`;
      console.log('[DataSourceService] 请求表元数据的URL:', url);

      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`获取表元数据失败: ${response?.status || 'Unknown Error'}`);
      }

      // 处理响应
      const responseData = response.data;

      if (responseData && responseData.success && responseData.data) {
        console.log('[DataSourceService] 成功获取表元数据');
        return responseData.data;
      } else {
        console.error('[DataSourceService] 获取表元数据返回未知格式:', responseData);
        throw new Error('获取表元数据失败: 响应格式错误');
      }
    } catch (error) {
      console.error(`获取表元数据失败:`, error);
      throw error;
    }
  },

  // 获取表字段列表
  async getTableColumns(dataSourceId: string, tableName: string, schema?: string): Promise<ColumnMetadata[]> {
    try {
      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回表字段');
        return this.getMockTableColumns(tableName);
      }

      // 验证参数
      if (!dataSourceId || dataSourceId === 'undefined') {
        console.error('[DataSourceService] 请求表字段时提供的数据源ID无效:', dataSourceId);
        throw new Error('获取表字段失败: 无效的数据源ID');
      }

      if (!tableName || tableName === 'undefined') {
        console.error('[DataSourceService] 请求表字段时提供的表名无效:', tableName);
        throw new Error('获取表字段失败: 无效的表名');
      }

      // 首先获取表的ID
      const tables = await this.getTableMetadata(dataSourceId) as TableMetadata[];
      let targetTable = null;

      // 如果提供了schema，则使用schema过滤
      if (schema) {
        targetTable = tables.find(table => table.name === tableName && table.schema === schema);
      } else {
        targetTable = tables.find(table => table.name === tableName);
      }

      if (!targetTable || !targetTable.id) {
        console.error(`[DataSourceService] 未找到表 ${tableName} 的ID`);
        throw new Error(`获取表字段失败: 未找到表 ${tableName}`);
      }

      const tableId = targetTable.id;
      console.log(`[DataSourceService] 找到表ID: ${tableId} for 表: ${tableName}`);

      // 使用表ID获取列信息
      const url = getMetadataApiUrl('columns', { tableId });
      console.log('[DataSourceService] 请求表字段的URL:', url);

      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`获取表字段失败: ${response?.status || 'Unknown Error'}`);
      }

      const responseData = response.data;

      if (responseData && responseData.success && responseData.data) {
        console.log(`[DataSourceService] 成功获取表字段, 数量: ${Array.isArray(responseData.data) ? responseData.data.length : 0}`);
        return Array.isArray(responseData.data) ? responseData.data : [];
      } else {
        console.warn('[DataSourceService] 获取表字段返回格式不支持:', responseData);
        return [];
      }
    } catch (error) {
      console.error(`获取表 ${tableName} 字段失败:`, error);
      throw error;
    }
  },

  // 模拟表元数据（用于开发测试）
  getMockTableMetadata(dataSourceId: string, tableName: string): TableMetadata {
    const table: TableMetadata = {
      name: tableName,
      schema: 'public',
      type: 'TABLE',
      comment: `这是一个表 ${tableName}`,
      columns: []
    };

    // 创建一些模拟列
    for (let i = 1; i <= 8; i++) {
      table.columns.push({
        name: `column_${i}`,
        type: i % 5 === 0 ? 'INTEGER' : (i % 5 === 1 ? 'VARCHAR' : (i % 5 === 2 ? 'TIMESTAMP' : (i % 5 === 3 ? 'BOOLEAN' : 'DECIMAL'))),
        nullable: i % 2 === 0,
        primaryKey: i === 1,
        foreignKey: i === 2,
        unique: i === 3,
        autoIncrement: i === 1,
        comment: `这是表 ${tableName} 的${i === 1 ? '主键' : '普通'}列`,
        size: i % 5 === 1 ? 255 : undefined,
        scale: i % 5 === 4 ? 2 : undefined
      });
    }

    return table;
  },

  // 模拟表列信息（用于开发测试）
  getMockTableColumns(tableName: string): ColumnMetadata[] {
    const columns: ColumnMetadata[] = [];

    // 创建一些模拟列
    for (let i = 1; i <= 8; i++) {
      columns.push({
        name: `column_${i}`,
        type: i % 5 === 0 ? 'INTEGER' : (i % 5 === 1 ? 'VARCHAR' : (i % 5 === 2 ? 'TIMESTAMP' : (i % 5 === 3 ? 'BOOLEAN' : 'DECIMAL'))),
        nullable: i % 2 === 0,
        primaryKey: i === 1,
        foreignKey: i === 2,
        unique: i === 3,
        autoIncrement: i === 1,
        comment: `这是表 ${tableName} 的${i === 1 ? '主键' : '普通'}列`,
        size: i % 5 === 1 ? 255 : undefined,
        scale: i % 5 === 4 ? 2 : undefined
      });
    }

    return columns;
  },

  // 获取表关系
  async getTableRelationships(dataSourceId: string): Promise<TableRelationship[]> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回表关系');
        return [];
      }

      // 使用apiUtils构建URL
      const url = getMetadataApiUrl('relationships', { dataSourceId });
      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`获取表关系失败: ${response?.status || 'Unknown Error'}`);
      }

      const data = response.data;
      return data.relationships || [];
    } catch (error) {
      console.error(`获取数据源${dataSourceId}表关系错误:`, error);
      throw error;
    }
  },

  // 获取表数据预览
  async getTableDataPreview(dataSourceId: string, tableName: string, params?: { page?: number, size?: number, sort?: string, order?: 'asc' | 'desc', filters?: Record<string, any> }): Promise<TableDataPreviewResult> {
    try {
      console.log(`[DataSourceService] 开始获取表数据预览，数据源ID: ${dataSourceId}, 表名: ${tableName}, 参数:`, params);

      if (USE_MOCK) {
        console.log(`使用模拟数据返回表数据预览，数据源ID: ${dataSourceId}, 表名: ${tableName}, 参数:`, params);

        // 模拟数据 - 为空数据预览创建合理的列定义
        const page = params?.page || 1;
        const size = params?.size || 10;

        // 创建模拟列定义
        const mockColumns = [
          { name: 'id', type: 'INTEGER', description: '主键ID' },
          { name: 'name', type: 'VARCHAR', description: '名称' },
          { name: 'description', type: 'TEXT', description: '描述信息' },
          { name: 'created_at', type: 'TIMESTAMP', description: '创建时间' },
          { name: 'updated_at', type: 'TIMESTAMP', description: '更新时间' },
          { name: 'status', type: 'VARCHAR', description: '状态' },
          { name: 'amount', type: 'DECIMAL', description: '金额' },
          { name: 'quantity', type: 'INTEGER', description: '数量' },
          { name: 'category', type: 'VARCHAR', description: '分类' },
          { name: 'is_active', type: 'BOOLEAN', description: '是否激活' }
        ];

        // 返回模拟数据预览，但行数据为空
        return {
          data: [], // 返回空数据行
          columns: mockColumns,
          page: page,
          size: size,
          total: 0,
          totalPages: 0
        };
      }

      // 规范化参数
      const page = params?.page || 1;
      const size = params?.size || 10;
      const sort = params?.sort;
      const order = params?.order || 'asc';

      try {
        // 尝试获取表的元数据
        const table = await this.getTableMetadata(dataSourceId, tableName) as TableMetadata;

        if (!table || !table.id) {
          console.error(`[DataSourceService] 未找到表 ${tableName} 的信息`);
          throw new Error(`获取表 ${tableName} 的预览数据失败: 未找到表信息`);
        }

        const tableId = table.id;
        console.log(`[DataSourceService] 找到表ID ${tableId}, 表名 ${tableName}`);

        // 获取表的列信息
        console.log(`[DataSourceService] 获取表 ${tableName} (ID: ${tableId}) 的列信息`);
        const columnsUrl = getMetadataApiUrl('columns', { tableId });
        console.log(`[DataSourceService] 获取列信息的URL: ${columnsUrl}`);

        const columnsResponse = await instance.get(columnsUrl);
        // http返回的数据在response.data中
        const columnsData = columnsResponse.data;

        // 处理列信息 - 支持两种格式:
        // 1. 标准格式: { success: true, data: [...] }
        // 2. 直接数组格式: [...]
        let columnsArray: any[] = [];

        if (Array.isArray(columnsData)) {
          // 直接是数组格式
          console.log(`[DataSourceService] 列信息直接以数组形式返回，共 ${columnsData.length} 列`);
          columnsArray = columnsData;
        } else if (columnsData.success && Array.isArray(columnsData.data)) {
          // 标准格式
          console.log(`[DataSourceService] 获取到 ${columnsData.data.length} 列信息`);
          columnsArray = columnsData.data;
        } else {
          // 无效格式
          console.error(`[DataSourceService] 列信息数据格式错误:`, columnsData);
          throw new Error('获取表列信息失败: 返回格式不正确');
        }

        // 处理列信息
        const formattedColumns = columnsArray.map((col: any) => ({
          name: col.name,
          type: col.dataType || col.columnType || col.type || 'VARCHAR',
          description: col.description || ''
        }));

        // 尝试从API获取表数据预览
        try {
          // 构建API URL - 使用正确的API路径
          const dataUrl = getMetadataApiUrl('table-data-by-id', { tableId });
          console.log(`[DataSourceService] 构建数据URL: ${dataUrl}`);

          const queryParams = new URLSearchParams();
          if (page) queryParams.append('page', page.toString());
          if (size) queryParams.append('size', size.toString());
          if (sort) queryParams.append('sort', sort);
          if (order) queryParams.append('order', order);

          const fullUrl = `${dataUrl}?${queryParams.toString()}`;
          console.log(`[DataSourceService] 请求表数据的URL: ${fullUrl}`);

          // 添加详细的请求跟踪
          console.log(`[DataSourceService] 发起表数据请求，参数:`, { page, size, sort, order });

          try {
            const dataResponse = await instance.get(fullUrl);
            // http返回的数据在response.data中
            const dataResult = dataResponse.data;
            console.log(`[DataSourceService] 表数据API返回数据:`, dataResult);

            // 处理标准格式的响应：{ success: true, code: 200, message: "操作成功", data: {total, pages, items} }
            if (dataResult && dataResult.success === true && dataResult.code === 200 && dataResult.data) {
              // 检查是否有嵌套的items字段
              if (dataResult.data.items && Array.isArray(dataResult.data.items)) {
                console.log(`[DataSourceService] 找到标准成功响应中的嵌套items数组, 长度:`, dataResult.data.items.length);
                return {
                  data: dataResult.data.items,
                  columns: formattedColumns,
                  page: dataResult.data.page || page,
                  size: dataResult.data.size || size,
                  total: dataResult.data.total || 0,
                  totalPages: dataResult.data.pages || Math.ceil((dataResult.data.total || 0) / size) || 0
                };
              }
            }

            // 处理其他格式响应
            if (dataResult && dataResult.success && dataResult.data) {
              // 格式化API返回的数据
              // 处理标准嵌套结构: { success: true, data: { items: [...], total: 41, ... } }
              if (dataResult.data.items && Array.isArray(dataResult.data.items)) {
                return {
                  data: dataResult.data.items,
                  columns: formattedColumns,
                  page: dataResult.data.page || page,
                  size: dataResult.data.size || size,
                  total: dataResult.data.total || 0,
                  totalPages: dataResult.data.pages || Math.ceil((dataResult.data.total || 0) / size) || 0
                };
              } else {
                // 处理简单结构
                return {
                  data: Array.isArray(dataResult.data) ? dataResult.data : [],
                  columns: formattedColumns,
                  page: dataResult.page || page,
                  size: dataResult.size || size,
                  total: dataResult.total || 0,
                  totalPages: dataResult.totalPages || Math.ceil((dataResult.total || 0) / size) || 0
                };
              }
            } else if (!dataResult || (!dataResult.items && !dataResult.data)) {
              // 只在真正没有数据时输出警告
              console.warn('[DataSourceService] 表数据API返回成功但没有数据:', dataResult);
            } else if (dataResult.items && Array.isArray(dataResult.items)) {
              // 直接处理包含items的数据格式
              console.log(`[DataSourceService] 处理直接包含items数组的数据, 长度:`, dataResult.items.length);
              return {
                data: dataResult.items,
                columns: formattedColumns,
                page: dataResult.page || page,
                size: dataResult.size || size,
                total: dataResult.total || dataResult.items.length,
                totalPages: dataResult.pages || Math.ceil((dataResult.total || dataResult.items.length) / size) || 0
              };
            }
          } catch (dataError) {
            console.error('[DataSourceService] 请求表数据时发生错误:', dataError);
          }
        } catch (urlError) {
          console.error('[DataSourceService] 构建数据URL时发生错误:', urlError);
        }

        // 如果到达这里，说明数据获取失败但列信息获取成功，返回空数据集
        console.log('[DataSourceService] 表数据获取失败，返回空数据集和列信息');
        return {
          data: [],
          columns: formattedColumns,
          page: page,
          size: size,
          total: formattedColumns.length || 0,
          totalPages: 0
        };

      } catch (metadataError) {
        console.error('[DataSourceService] 获取表元数据时出错:', metadataError);

        // 如果连元数据都获取失败，则返回最小的空数据集
        return {
          data: [],
          columns: [],
          page: page,
          size: size,
          total: 0,
          totalPages: 0
        };
      }
    } catch (error) {
      console.error(`[DataSourceService] 获取表数据预览失败:`, error);
      throw error;
    }
  },

  // 使用表格预览功能作为备选方案
  async getTablePreview(dataSourceId: string, tableName: string, params?: { limit?: number }): Promise<{ columns: { name: string, type: string }[], data: any[] }> {
    try {
      if (USE_MOCK) {
        console.log(`使用模拟数据返回表预览数据，数据源ID: ${dataSourceId}, 表名: ${tableName}, 参数:`, params);

        // 模拟数据加载延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 生成随机数据行数（10-50之间）
        const totalRows = 45;

        // 计算当前页的数据
        const limit = params?.limit || 10;

        // 创建列定义
        const columns = [
          { name: 'id', type: 'INTEGER' },
          { name: 'name', type: 'VARCHAR' },
          { name: 'description', type: 'TEXT' },
          { name: 'price', type: 'DECIMAL' },
          { name: 'quantity', type: 'INTEGER' },
          { name: 'created_at', type: 'TIMESTAMP' },
          { name: 'is_active', type: 'BOOLEAN' }
        ];

        // 生成数据行
        const rows = Array.from({ length: Math.min(limit, totalRows) }, (_, i) => {
          const rowIndex = i + 1;
          return {
            id: rowIndex,
            name: `Item ${rowIndex}`,
            description: `Description for item ${rowIndex}`,
            price: parseFloat((Math.random() * 1000).toFixed(2)),
            quantity: Math.floor(Math.random() * 100),
            created_at: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString(),
            is_active: Math.random() > 0.2
          };
        });

        return {
          columns: columns,
          data: rows
        };
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (params?.limit) queryParams.append('limit', params.limit.toString());

      // 使用apiUtils构建URL
      const baseUrl = getMetadataApiUrl('table', { dataSourceId, tableName });
      const url = `${baseUrl}/preview${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

      const response = await instance.get(url);
      const data = response.data;

      if (!data.success) {
        throw new Error(`获取表预览数据失败: ${data.message || '未知错误'}`);
      }

      return data.data || { columns: [], data: [] };
    } catch (error) {
      console.error(`获取数据源${dataSourceId}表${tableName}预览错误:`, error);
      throw error;
    }
  },

  // 搜索数据源元数据
  async searchMetadata(dataSourceId: string, keyword: string, params?: { entities?: string[], caseSensitive?: boolean, useRegex?: boolean, limit?: number }): Promise<any> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回搜索结果');
        return [];
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('keyword', keyword);
      if (params?.entities) params.entities.forEach(entity => queryParams.append('entities', entity));
      if (params?.caseSensitive !== undefined) queryParams.append('caseSensitive', params.caseSensitive.toString());
      if (params?.useRegex !== undefined) queryParams.append('useRegex', params.useRegex.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());

      // 使用apiUtils构建URL
      const baseUrl = getMetadataApiUrl('search', { dataSourceId });
      const url = `${baseUrl}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`搜索元数据失败: ${response?.status || 'Unknown Error'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`搜索数据源${dataSourceId}元数据错误:`, error);
      throw error;
    }
  },

  // 新增: 获取同步历史记录
  async getSyncHistory(dataSourceId: string): Promise<any[]> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回同步历史记录');
        return [];
      }

      // 使用apiUtils构建URL
      const url = getMetadataApiUrl('syncHistory', { dataSourceId });
      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`获取同步历史失败: ${response?.status || 'Unknown Error'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`获取数据源${dataSourceId}同步历史错误:`, error);
      throw error;
    }
  },

  // 新增: 分析表列的详细信息
  async analyzeColumns(dataSourceId: string, params: { tables: string[], columns: string[] }): Promise<any> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回分析列结果');
        return [];
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      params.tables.forEach(table => queryParams.append('tables', table));
      params.columns.forEach(column => queryParams.append('columns', column));

      // 使用apiUtils构建URL
      const baseUrl = getMetadataApiUrl('analyzeColumns', { dataSourceId });
      const url = `${baseUrl}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`分析列详情失败: ${response?.status || 'Unknown Error'}`);
      }

      return response.data;
    } catch (error) {
      console.error(`分析数据源${dataSourceId}列错误:`, error);
      throw error;
    }
  },

  // 测试现有数据源连接
  async testExistingConnection(id: string): Promise<ConnectionTestResult> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回已存在数据源的连接测试结果');
        // 模拟1秒的延迟
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 始终返回成功的连接测试结果
        return {
          success: true,
          message: '连接测试成功',
          details: {
            connectionTime: '120ms',
            serverVersion: 'Mock MySQL Server 5.7.0',
            dbName: 'mock_database'
          }
        };
      }

      const url = getDataSourceApiUrl('testExistingConnection', { id });
      console.log('[DataSourceService] 测试现有连接, URL:', url);

      // 发送请求，包含必要参数
      const response = await instance.post(url);

      console.log('[DataSourceService] 测试现有连接原始响应:', response);

      // 处理外层和内层响应结构
      // 外层响应: {success: true, code: 200, message: "操作成功", data: {...}}
      // 内层响应: data: {success: true/false, message: "...", details: {...}}

      if (!response) {
        return {
          success: false,
          message: '连接测试失败: 无响应',
          details: null
        };
      }

      // 检查外层响应是否成功
      if (!response.success) {
        return {
          success: false,
          message: response.message || '连接测试失败: 请求错误',
          details: response.details || response
        };
      }

      // 外层成功，检查内层data
      if (response.data) {
        // 使用内层data中的success字段作为最终结果
        return {
          success: response.data.success === true, // 显式转换为布尔值
          message: response.data.message || (response.data.success ? '连接成功' : '连接失败'),
          details: response.data.details || response.data
        };
      }

      // 响应格式异常
      return {
        success: false,
        message: '连接测试结果格式异常',
        details: response
      };
    } catch (error) {
      console.error('[DataSourceService] 测试现有连接失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '连接测试失败',
        details: error
      }
    }
  },

  // 高级搜索
  async advancedSearch(params: { keyword: string, dataSourceIds: string[], entityTypes: string[], useRegex?: boolean, caseSensitive?: boolean }): Promise<any> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回高级搜索结果');
        return [];
      }

      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('keyword', params.keyword);
      params.dataSourceIds.forEach(id => queryParams.append('dataSourceIds', id));
      params.entityTypes.forEach(type => queryParams.append('entityTypes', type));
      if (params.useRegex !== undefined) queryParams.append('useRegex', params.useRegex.toString());
      if (params.caseSensitive !== undefined) queryParams.append('caseSensitive', params.caseSensitive.toString());

      // 使用apiUtils构建URL
      const url = `${getApiUrl()}/metadata/datasources/search?${queryParams.toString()}`;
      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`高级搜索失败: ${response?.status || 'Unknown Error'}`);
      }

      // 解析响应
      const result = response.data;
      console.log('高级搜索结果:', result);

      // 处理嵌套数据结构
      const data = result.success && result.data ? result.data : result;

      // 最后返回结果时确保page和size是数字类型
      return {
        items: data.items || [],
        total: data.total || 0,
        page: data.page ? Number(data.page) : 1,
        size: data.size ? Number(data.size) : 10,
        totalPages: data.totalPages || 0
      };
    } catch (error) {
      console.error('高级搜索失败:', error);
      throw error;
    }
  },

  // 根据ID获取数据源名称
  async getDataSourceName(id: string): Promise<string> {
    if (!id) return '未指定';

    try {
      const dataSource = await this.getDataSource(id);
      return dataSource?.name || '未指定';
    } catch (error) {
      console.error(`获取数据源${id}名称失败:`, error);
      return '未指定';
    }
  },

  // 批量获取数据源名称
  async getDataSourceNames(ids: string[]): Promise<Record<string, string>> {
    if (!ids || ids.length === 0) return {};

    try {
      // 获取所有数据源
      const response = await this.getDataSources({ page: 1, size: 100 });
      const dataSources = response.items || [];

      // 创建ID到名称的映射
      const nameMap: Record<string, string> = {};
      dataSources.forEach(ds => {
        if (ds.id && ids.includes(ds.id)) {
          nameMap[ds.id] = ds.name || '未指定';
        }
      });

      return nameMap;
    } catch (error) {
      console.error('批量获取数据源名称失败:', error);
      // 返回默认值映射
      return ids.reduce((map, id) => ({ ...map, [id]: '未指定' }), {});
    }
  },

  // 检查数据源状态
  async checkDataSourceStatus(id: string): Promise<{ status: DataSourceStatus, message?: string }> {
    try {
      if (USE_MOCK) {
        console.log('使用模拟数据返回数据源状态');

        // 查找数据源
        const dataSource = mockDataSources.find(ds => ds.id === id);
        if (!dataSource) {
          throw new Error(`未找到ID为${id}的数据源`);
        }

        // 返回模拟的数据源状态
        return {
          status: dataSource.status,
          message: '数据源状态正常'
        };
      }

      // 使用apiUtils构建URL
      const url = getDataSourceApiUrl('checkStatus', { id });
      const response = await instance.get(url);

      if (!response || response.status !== 200) {
        throw new Error(`检查数据源状态失败: ${response?.status || 'Unknown Error'}`);
      }

      const responseData = response.data;

      // 处理标准响应格式
      if (responseData.success === true && responseData.data) {
        return responseData.data;
      }

      return responseData;
    } catch (error) {
      console.error('检查数据源状态失败:', error);
      throw error;
    }
  },

  /**
   * 获取数据库架构下的表列表
   * @param schemaId 架构ID
   * @returns 表列表
   */
  async getTables(schemaId: string): Promise<TableMetadata[]> {
    try {
      console.log(`[DataSourceService] 获取架构 ${schemaId} 的表列表`);

      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回表列表');
        // 生成一些模拟表数据
        return mockTables.map((tableName, index) => ({
          id: `table-${schemaId}-${index}`,
          name: tableName,
          schemaId,
          type: 'TABLE',
          description: `Mock table ${tableName}`,
          rowCount: Math.floor(Math.random() * 10000),
          columnsCount: Math.floor(Math.random() * 20) + 5,
          columns: []
        }));
      }

      // 使用apiUtils构建URL
      const url = getMetadataApiUrl('tables', { schemaId });
      console.log(`[DataSourceService] 获取表列表 URL: ${url}`);

      const response = await instance.get(url);
      console.log('[DataSourceService] 获取表列表响应:', response);

      // 检查响应格式
      if (response.success === false) {
        throw new Error(response.message || `获取架构 ${schemaId} 的表列表失败`);
      }

      // 处理响应数据
      let tables: TableMetadata[] = [];

      if (response.data && Array.isArray(response.data)) {
        tables = response.data;
      } else if (response.data && Array.isArray(response.data.items)) {
        tables = response.data.items;
      } else if (Array.isArray(response)) {
        tables = response;
      } else if (response && Array.isArray(response.items)) {
        tables = response.items;
      }

      console.log(`[DataSourceService] 成功获取到 ${tables.length} 个表`);
      return tables;
    } catch (error) {
      console.error(`[DataSourceService] 获取架构 ${schemaId} 的表列表失败:`, error);
      return [];
    }
  },

  /**
   * 获取表详情信息
   * @param tableId 表ID
   * @returns 表详细信息
   */
  async getTableDetails(tableId: string): Promise<TableMetadata> {
    try {
      console.log(`[DataSourceService] 获取表 ${tableId} 的详情`);

      if (USE_MOCK) {
        console.log('[DataSourceService] 使用模拟数据返回表详情');
        // 生成一些模拟表详情数据
        const mockTableName = mockTables[Math.floor(Math.random() * mockTables.length)];
        return {
          id: tableId,
          name: mockTableName,
          schemaId: tableId.split('-')[1], // 从tableId中提取schemaId
          type: 'TABLE',
          description: `Mock table ${mockTableName} details`,
          rowCount: Math.floor(Math.random() * 10000),
          columnsCount: Math.floor(Math.random() * 20) + 5,
          columns: Array.from({ length: 10 }, (_, i) => ({
            id: `col-${tableId}-${i}`,
            name: `column_${i}`,
            dataType: ['VARCHAR', 'INT', 'TIMESTAMP', 'BOOLEAN', 'DECIMAL'][Math.floor(Math.random() * 5)],
            position: i,
            isNullable: Math.random() > 0.5,
            isPrimaryKey: i === 0,
            description: `Column ${i} of table ${mockTableName}`
          }))
        };
      }

      // 使用apiUtils构建URL
      const url = getMetadataApiUrl('table', { tableId });
      console.log(`[DataSourceService] 获取表详情 URL: ${url}`);

      const response = await instance.get(url);
      console.log('[DataSourceService] 获取表详情响应:', response);

      // 检查响应格式
      if (response.success === false) {
        throw new Error(response.message || `获取表 ${tableId} 的详情失败`);
      }

      // 处理响应数据
      let tableDetails: TableMetadata;

      if (response.data) {
        tableDetails = response.data;
      } else {
        tableDetails = response;
      }

      console.log(`[DataSourceService] 成功获取表 ${tableDetails.name} 的详情`);
      return tableDetails;
    } catch (error) {
      console.error(`[DataSourceService] 获取表 ${tableId} 的详情失败:`, error);
      // 返回一个最小的表数据结构，避免前端崩溃
      return {
        id: tableId,
        name: `未知表 (${tableId})`,
        schemaId: '',
        type: 'TABLE',
        description: `获取详情失败: ${error instanceof Error ? error.message : String(error)}`,
        rowCount: 0,
        columnsCount: 0,
        columns: []
      };
    }
  },
}

export default dataSourceService
