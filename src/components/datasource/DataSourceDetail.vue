<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue'
import type {DataSource} from '@/types/datasource'
import type {TableMetadata} from '@/types/metadata'
import {useDataSourceStore} from '@/stores/datasource'
import {useMessageService} from '@/services/message'
import {useResponseHandler} from '@/utils/api'
import TableDataPreview from './TableDataPreview.vue'
import AuthConfigModal from './AuthConfigModal.vue'
import DecryptConfigModal from './DecryptConfigModal.vue'
import DesensitizeConfigModal from './DesensitizeConfigModal.vue'
// import AuthRequestModal from './AuthRequestModal.vue'
import {useUserStore} from '@/stores/user'
import {getMetadataApiUrl} from '@/services/apiUtils'
import instance from "@/utils/axios";

// 组件属性
const props = defineProps<{
  dataSourceId: string
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'edit', dataSource: DataSource): void
  (e: 'refresh'): void
  (e: 'delete', dataSource: DataSource): void
}>()

// 数据源状态管理
const dataSourceStore = useDataSourceStore()

// 初始化消息服务
const messageService = useMessageService()
const { handleResponse } = useResponseHandler()

// 用户状态
const userStore = useUserStore()

// 在script setup部分顶部定义类型
interface TableColumn {
  name: string;
  columnName?: string;
  dataType?: string;
  type: string;
  nullable?: boolean;
  primaryKey?: boolean;
  defaultValue?: string;
  comment?: string;
  [key: string]: any;
}

// 组件状态
const isLoading = ref(true)
const isMetadataLoading = ref(false)
const isSyncingMetadata = ref(false)
const selectedTable = ref<string | null>(null)
const expandedTables = ref<Record<string, boolean>>({})
const dataSource = ref<DataSource | null>(null)
const tables = ref<TableMetadata[]>([])
const searchKeyword = ref('')
const viewMode = ref<'metadata' | 'preview'>('metadata')
const isMockMode = ref(import.meta.env.VITE_USE_MOCK_API === 'true')
const selectedTableId = ref<string | null>(null)
// 使用更简单的类型定义方式，直接指定为字符串索引的对象
const tableColumnsCache = ref<{[key: string]: TableColumn[]}>({})

// 授权配置相关状态
const showAuthConfigModal = ref(false)
const authConfigType = ref<'datasource' | 'schema' | 'table' | 'column'>('datasource')
const authConfigItemId = ref('')
const authConfigItemName = ref('')
const authConfigInitialIsAuthRequired = ref(false)

// 解密配置相关状态
const showDecryptConfigModal = ref(false)
const decryptConfigColumnId = ref('')
const decryptConfigColumnName = ref('')
const decryptConfigInitialConfig = ref<any>(null)

// 脱敏配置相关状态
const showDesensitizeConfigModal = ref(false)
const desensitizeConfigColumnId = ref('')
const desensitizeConfigColumnName = ref('')
const desensitizeConfigInitialAuthDesensitize = ref(false)
const desensitizeConfigInitialFormatType = ref('')

// 申请授权相关状态已注释
// const showAuthRequestModal = ref(false)
// const authRequestType = ref<'datasource' | 'schema' | 'table' | 'column'>('datasource')
// const authRequestItemId = ref('')
// const authRequestItemName = ref('')

// 加载数据源信息
const loadDataSource = async () => {
  isLoading.value = true;

  try {
    console.log('加载数据源信息，ID:', props.dataSourceId);

    // 检查store中是否已有此数据源
    const existingDataSource = dataSourceStore.currentDataSource?.id === props.dataSourceId
      ? dataSourceStore.currentDataSource
      : dataSourceStore.dataSources.find((ds: DataSource) => ds.id === props.dataSourceId);

    if (existingDataSource) {
      console.log('使用已存在的数据源数据');
      dataSource.value = {...existingDataSource}; // 使用浅拷贝避免引用问题
    } else {
      console.log('从API获取数据源数据');
      const result = await dataSourceStore.getDataSourceById(props.dataSourceId);
      if (result) {
        dataSource.value = result;
      } else {
        throw new Error('获取数据源详情失败：服务器返回空数据');
      }
    }

    return dataSource.value;
  } catch (err) {
    console.error('加载数据源详情失败:', err);
    messageService.error('加载数据源详情失败: ' + (err instanceof Error ? err.message : String(err)));
    throw err; // 重新抛出错误以便调用者处理
  } finally {
    isLoading.value = false;
  }
};

// 加载元数据
const loadMetadata = async () => {
  console.log('开始加载元数据');
  isMetadataLoading.value = true
  tables.value = []

  try {
    console.log('开始加载数据源元数据，dataSourceId:', dataSource.value?.id || props.dataSourceId)

    // 检查数据源是否已加载
    const currentId = dataSource.value?.id || props.dataSourceId
    if (!currentId) {
      throw new Error('数据源未加载或ID无效')
    }

    // 使用Mock模式下的获取方式
    console.log('数据源详情组件 - Mock模式:', isMockMode.value ? '已启用' : '已禁用')
    if (isMockMode.value) {
      try {
        // 在Mock模式下，使用store方法获取元数据
        const metadata = await dataSourceStore.getDataSourceMetadata(currentId)
        if (metadata && Array.isArray(metadata.tables)) {
          tables.value = metadata.tables
          console.log('从store获取到的元数据:', tables.value.length, '张表')
        } else {
          tables.value = []
          console.log('从store获取的元数据为空')
        }
      } catch (error) {
        console.error('Mock模式获取元数据失败:', error)
        tables.value = []
      }
      return
    }

    // 直接从API获取表列表
    try {
      // 验证数据源ID是否有效
      if (currentId === 'undefined') {
        console.error('数据源ID无效，无法获取元数据');
        throw new Error('数据源ID无效');
      }

      // 首先获取schemas
      const schemasUrl = getMetadataApiUrl('schemas', { dataSourceId: currentId }) + `?_t=${Date.now()}`;
      console.log('获取schemas的URL:', schemasUrl);

      const schemasResponse = await instance.get(schemasUrl);
      const schemasData = schemasResponse.data;

      // 处理不同格式的响应：可能是包含success和data的对象，也可能直接是数组
      let schemasList = [];
      if (Array.isArray(schemasData)) {
        // 直接是数组的情况
        console.log('获取到的schemas是数组格式:', schemasData.length);
        schemasList = schemasData;
      } else if (schemasData.success && Array.isArray(schemasData.data)) {
        // 包含success和data的对象
        console.log('获取到的schemas是对象格式:', schemasData.data.length);
        schemasList = schemasData.data;
      } else {
        console.error('获取数据库schemas失败:', schemasData);
        throw new Error('获取数据库schemas失败');
      }

      console.log('获取到的schemas:', schemasList);

      // 如果没有schema，返回空表列表
      if (schemasList.length === 0) {
        console.log('数据源没有可用的schema');
        tables.value = [];
        return;
      }

      // 遍历所有schema，获取表
      const allTables = [];

      for (const schema of schemasList) {
        const schemaId = schema.id || schema.schemaId;
        if (!schemaId) {
          console.warn('跳过无效的schema:', schema);
          continue;
        }

        // 确保schemaId是字符串且不是"{schemaId}"
        if (schemaId === '{schemaId}' || typeof schemaId !== 'string') {
          console.error(`无效的schemaId: ${schemaId}，类型: ${typeof schemaId}`);
          continue;
        }

        // 获取当前schema的表
        try {
          // 直接构建URL并执行健壮性检查
          const tablesUrl = getMetadataApiUrl('tables', { schemaId });

          // 额外检查 - 确保URL中的占位符已被替换
          if (tablesUrl.includes('{schemaId}')) {
            console.error(`tablesUrl中的占位符未被替换: ${tablesUrl}, schemaId=${schemaId}`);
            continue;
          }

          // 添加时间戳防止缓存
          const finalUrl = `${tablesUrl}?_t=${Date.now()}`;
          console.log(`获取schema ${schema.name} (ID: ${schemaId})的表列表, URL:`, finalUrl);

          const tablesResponse = await instance.get(finalUrl);
          const tablesData = tablesResponse.data;

          // 处理不同格式的响应：可能是包含success和data的对象，也可能直接是数组
          let tablesList = [];
          if (Array.isArray(tablesData)) {
            // 直接是数组格式
            console.log(`获取到schema ${schema.name} 的表（数组格式）:`, tablesData.length);
            tablesList = tablesData;
          } else if (tablesData.success && Array.isArray(tablesData.data)) {
            // 包含success和data的对象格式
            console.log(`获取到schema ${schema.name} 的表（对象格式）:`, tablesData.data.length);
            tablesList = tablesData.data;
          } else {
            console.warn(`获取schema ${schema.name} 的表失败:`, tablesData);
            continue;
          }

          // 处理表数据
          const schemaName = schema.name;
          const tablesWithSchema = tablesList.map((table: any) => {
            return {
              ...table,
              schema: schemaName || 'default' // 添加schema信息
            };
          });

          allTables.push(...tablesWithSchema);
        } catch (e) {
          console.error(`获取schema ${schema.name} 的表出错:`, e);
        }
      }

      console.log('获取到所有表:', allTables.length);
      tables.value = allTables;
    } catch (error) {
      console.error('加载元数据失败:', error);
      messageService.error('加载元数据失败: ' + (error instanceof Error ? error.message : String(error)));
      tables.value = [];
    } finally {
      isMetadataLoading.value = false;
    }
  } catch (err) {
    console.error('加载元数据失败:', err)

    // 检查错误是否包含HTML相关信息
    const errorMessage = err instanceof Error ? err.message : String(err);

    if (errorMessage.includes('HTML而非JSON') || errorMessage.includes('<!DOCTYPE') || errorMessage.includes('<html')) {
      messageService.error('加载元数据失败: 服务器返回了非法响应，可能需要重新登录');
    } else {
      messageService.error('加载元数据失败: ' + errorMessage);
    }

    tables.value = []
  }
}

// 同步元数据
const syncMetadata = async () => {
  // 增强检查，确保dataSource.value和id都存在且有效
  if (!dataSource.value) {
    console.error('同步元数据失败: 数据源对象为空');
    messageService.error('同步元数据失败: 数据源信息未加载');
    return;
  }

  if (!dataSource.value.id || dataSource.value.id === 'undefined') {
    console.error('同步元数据失败: 无效的数据源ID', dataSource.value.id);
    messageService.error('同步元数据失败: 无效的数据源ID');
    return;
  }

  isSyncingMetadata.value = true;

  try {
    console.log('开始同步元数据:', dataSource.value.id);
    // 使用store中的方法进行同步，它已经能处理各种异常情况
    const result = await dataSourceStore.syncDataSourceMetadata(dataSource.value.id);

    // 使用统一的响应处理器处理结果
    handleResponse(result, {
      showSuccessMessage: true,
      successMessage: '元数据同步成功',
      errorMessage: '元数据同步失败'
    });

    // 重新加载元数据
    await loadMetadata();
  } catch (err) {
    console.error('同步元数据失败:', err);
    // 错误已由响应处理器处理
  } finally {
    isSyncingMetadata.value = false;
  }
};

// 搜索表
const filteredTables = computed(() => {
  if (!searchKeyword.value.trim()) {
    return tables.value
  }

  const keyword = searchKeyword.value.toLowerCase().trim()

  return tables.value.filter(table => {
    return table.name.toLowerCase().includes(keyword)
  })
})

// 获取表的列数
const getColumnCount = (table: any): number => {
  // 直接使用API返回的columnsCount字段，如果存在
  if (table.columnsCount !== undefined && table.columnsCount !== null) {
    return table.columnsCount;
  }

  // 作为备选，从已加载的列信息中获取
  const tableId = getTableId(table);
  if (tableId && tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] &&
      Array.isArray(tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value])) {
    return tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value].length;
  }

  // 如果都没有，返回0
  return 0;
}

// 获取表名的安全函数
const getTableDisplayName = (table: any): string => {
  if (!table) return '未知表';

  if (typeof table.name === 'string') {
    return table.name;
  } else if (typeof table.name === 'object' && table.name !== null && typeof table.name.name === 'string') {
    return table.name.name;
  } else if (table.tableName && typeof table.tableName === 'string') {
    return table.tableName;
  } else if (table.table_name && typeof table.table_name === 'string') {
    return table.table_name;
  } else {
    return String(table.name || '未知表');
  }
}

// 获取表ID的安全函数
const getTableId = (table: any): string => {
  return String(table.id || '');
}

// 检查表是否展开
const isTableExpanded = (table: any): boolean => {
  const tableId = getTableId(table);
  return expandedTables.value[tableId] || false;
}

// 切换表展开状态
const toggleTableExpand = (table: any) => {
  const tableId = getTableId(table);
  if (!tableId) return;

  if (isTableExpanded(table)) {
    expandedTables.value[tableId] = false;
  } else {
    expandedTables.value[tableId] = true;
    // 如果展开并且没有缓存列信息，则加载列信息
    if (!tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value]) {
      fetchTableColumns(table);
    }
  }
};

// 获取表格列信息
const fetchTableColumns = async (table: any) => {
  const tableId = getTableId(table);
  const tableName = getTableDisplayName(table);

  if (!tableId) return;

  try {
    // 设置为空数组表示正在加载
    tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] = [];

    const columnsUrl = `/api/metadata/tables/${tableId}/columns`;
    console.log(`[DataSourceDetail] 获取表 ${tableName} 的列信息，URL: ${columnsUrl}`);

    // 使用instance.get替代fetch
    const response = await instance.get(columnsUrl);

    // 响应数据在response.data中
    const columnsData = response.data;

    // 处理不同格式的响应
    if (columnsData.success && Array.isArray(columnsData.data)) {
      // 标准格式：{success:true, data:[...]}
      tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] = columnsData.data;
      console.log(`成功获取表 ${tableName} 的列信息:`, columnsData.data);
    } else if (Array.isArray(columnsData)) {
      // 直接返回数组的情况
      tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] = columnsData;
      console.log(`成功获取表 ${tableName} 的列信息(数组格式):`, columnsData);
    } else {
      console.warn(`获取表 ${tableName} 的列信息响应格式不正确:`, columnsData);
      tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] = [];
    }
  } catch (error) {
    console.error(`获取表 ${tableName} 的列信息时出错:`, error);
    tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] = [];
  }
};

// 查看表数据预览
const viewTableData = (table: any) => {
  selectedTable.value = getTableDisplayName(table)
  selectedTableId.value = table.id
  viewMode.value = 'preview'
}

// 获取选中表的ID
const getSelectedTableId = (): string | undefined => {
  if (!selectedTableId.value) {
    // 尝试从tables中查找匹配的表ID
    const foundTable = tables.value.find(t => getTableDisplayName(t) === selectedTable.value);
    return foundTable?.id;
  }
  return selectedTableId.value;
}

// 返回元数据视图
const backToMetadata = () => {
  viewMode.value = 'metadata'
  selectedTable.value = null
}

// 获取数据源类型名称
const getDataSourceTypeName = (type?: string) => {
  if (!type) return '未知';

  const typeMap: Record<string, string> = {
    'mysql': 'MySQL',
    'postgresql': 'PostgreSQL',
    'oracle': 'Oracle',
    'sqlserver': 'SQL Server',
    'mongodb': 'MongoDB',
    'elasticsearch': 'Elasticsearch',
    // 兼容大写格式
    'MYSQL': 'MySQL',
    'POSTGRESQL': 'PostgreSQL',
    'ORACLE': 'Oracle',
    'SQLSERVER': 'SQL Server',
    'MONGODB': 'MongoDB',
    'ELASTICSEARCH': 'Elasticsearch'
  }

  return typeMap[type] || type
}

// 格式化同步频率
const formatSyncFrequency = (frequency?: string) => {
  if (!frequency) return '未知';

  const frequencyMap: Record<string, string> = {
    'manual': '手动',
    'hourly': '每小时',
    'daily': '每天',
    'weekly': '每周',
    'monthly': '每月',
    // 兼容大写格式
    'MANUAL': '手动',
    'HOURLY': '每小时',
    'DAILY': '每天',
    'WEEKLY': '每周',
    'MONTHLY': '每月'
  }

  return frequencyMap[frequency] || frequency
}

// 处理编辑
const handleEdit = () => {
  if (dataSource.value) {
    emit('edit', dataSource.value)
  }
}

// 处理删除
const handleDelete = () => {
  if (dataSource.value) {
    // 使用消息服务的确认方法
    messageService.error(`确定要删除数据源 "${dataSource.value.name}" 吗？此操作不可撤销。`);
    // 直接调用删除事件
    emit('delete', dataSource.value);
  }
}

// 刷新数据
const handleRefresh = async () => {
  console.log('DataSourceDetail组件: 手动刷新数据');

  try {
    isMetadataLoading.value = true; // 确保设置加载状态
    await loadDataSource();
    await loadMetadata();
    messageService.success('数据已刷新');
  } catch (err) {
    console.error('刷新数据失败:', err);
    // 错误已在各自的函数中处理
  } finally {
    isMetadataLoading.value = false; // 确保重置加载状态
  }
}

// 组件挂载
onMounted(async () => {
  console.log('DataSourceDetail组件挂载，dataSourceId:', props.dataSourceId);
  // 首先尝试加载数据源数据
  try {
    await loadDataSource();
    // 确保无论是否已有数据，都重新加载元数据
    await loadMetadata();
    console.log('组件挂载完成，元数据加载状态:', isMetadataLoading.value, '表数量:', tables.value.length);
  } catch (error) {
    console.error('组件挂载时加载数据失败:', error);
    messageService.error('加载数据失败，请刷新页面重试');
  }
})

// 为tables添加监听
watch(() => tables.value, (newTables) => {
  console.log('表数据发生变化，当前表数量:', newTables.length);
});

// 为isMetadataLoading添加监听
watch(() => isMetadataLoading.value, (newValue) => {
  console.log('元数据加载状态变化:', newValue);
});

// 组件卸载前清理
onUnmounted(() => {
  console.log('组件即将卸载，清理状态');
  tables.value = [];
  isMetadataLoading.value = false;
});

// 打开数据源授权配置弹窗
const openDatasourceAuthConfig = () => {
  if (!dataSource.value) return;

  console.log('打开数据源授权配置弹窗，数据源信息:', dataSource.value);

  authConfigType.value = 'datasource';
  authConfigItemId.value = dataSource.value.id;
  authConfigItemName.value = dataSource.value.name;

  // 确保isAuthRequired字段存在且正确转换为布尔值
  // 使用严格的布尔值比较，确保true就是true，false就是false
  const isAuthRequired = dataSource.value.isAuthRequired === true;

  console.log('数据源授权状态:', isAuthRequired, '原始值:', dataSource.value.isAuthRequired);

  authConfigInitialIsAuthRequired.value = isAuthRequired;

  showAuthConfigModal.value = true;
};

// 打开Schema授权配置弹窗
const openSchemaAuthConfig = (schema: any) => {
  console.log('打开Schema授权配置弹窗，Schema信息:', schema);

  authConfigType.value = 'schema';
  authConfigItemId.value = schema.id;
  authConfigItemName.value = schema.name;

  // 确保isAuthRequired字段存在且正确转换为布尔值
  // 使用严格的布尔值比较，确保true就是true，false就是false
  const isAuthRequired = schema.isAuthRequired === true;

  console.log('Schema授权状态:', isAuthRequired, '原始值:', schema.isAuthRequired);

  authConfigInitialIsAuthRequired.value = isAuthRequired;

  showAuthConfigModal.value = true;
};

// 打开表授权配置弹窗
const openTableAuthConfig = (table: any) => {
  console.log('打开表授权配置弹窗，表信息:', table);

  authConfigType.value = 'table';
  authConfigItemId.value = table.id;
  authConfigItemName.value = getTableDisplayName(table);

  // 确保isAuthRequired字段存在且正确转换为布尔值
  // 使用严格的布尔值比较，确保true就是true，false就是false
  const isAuthRequired = table.isAuthRequired === true;

  console.log('表授权状态:', isAuthRequired, '原始值:', table.isAuthRequired);

  authConfigInitialIsAuthRequired.value = isAuthRequired;

  showAuthConfigModal.value = true;
};

// 打开列授权配置弹窗
const openColumnAuthConfig = (column: any) => {
  console.log('打开列授权配置弹窗，列信息:', column);

  authConfigType.value = 'column';
  authConfigItemId.value = column.id;
  authConfigItemName.value = column.name || column.columnName;

  // 确保isAuthRequired字段存在且正确转换为布尔值
  // 使用严格的布尔值比较，确保true就是true，false就是false
  const isAuthRequired = column.isAuthRequired === true;

  console.log('列授权状态:', isAuthRequired, '原始值:', column.isAuthRequired);

  authConfigInitialIsAuthRequired.value = isAuthRequired;

  showAuthConfigModal.value = true;
};

// 关闭授权配置弹窗
const closeAuthConfigModal = () => {
  showAuthConfigModal.value = false;
};

// 解密配置相关方法

// 打开列解密配置弹窗
const openColumnDecryptConfig = (column: any) => {
  console.log('打开列解密配置弹窗，列信息:', column);

  decryptConfigColumnId.value = column.id;
  decryptConfigColumnName.value = column.name || column.columnName;

  // 解析现有的解密配置
  let initialConfig = null;
  if (column.encryConfig) {
    try {
      initialConfig = typeof column.encryConfig === 'string'
          ? JSON.parse(column.encryConfig)
          : column.encryConfig;
      console.log('现有解密配置:', initialConfig);
    } catch (e) {
      console.error('解析解密配置失败:', e);
      initialConfig = null;
    }
  }

  // 确保先设置配置，再显示弹窗
  decryptConfigInitialConfig.value = initialConfig;

  // 使用nextTick确保数据已经设置
  nextTick(() => {
    showDecryptConfigModal.value = true;
  });
};

// 关闭解密配置弹窗
const closeDecryptConfigModal = () => {
  showDecryptConfigModal.value = false;
};

// 保存解密配置
const saveDecryptConfig = async (config: any) => {
  // 查找列并更新本地数据
  for (const tableId in tableColumnsCache.value) {
    const columns = tableColumnsCache.value[tableId];
    const column = columns.find(c => c.id === decryptConfigColumnId.value);
    if (column) {
      // 更新解密配置
      column.encryConfig = config;
      // 更新加密状态
      column.isEncrypted = !!(config.aes || config.gm);
      break;
    }
  }

  // 关闭弹窗
  closeDecryptConfigModal();

  // 显示成功消息
  messageService.success('解密配置保存成功');
};

// 保存授权配置
const saveAuthConfig = async (config: any) => {
  // 根据类型更新本地数据
  if (authConfigType.value === 'datasource' && dataSource.value) {
    dataSource.value.isAuthRequired = config.isAuthRequired;
  } else if (authConfigType.value === 'schema') {
    const schema = tables.value.find(t => t.id === authConfigItemId.value);
    if (schema) {
      schema.isAuthRequired = config.isAuthRequired;
    }
  } else if (authConfigType.value === 'table') {
    const table = tables.value.find(t => t.id === authConfigItemId.value);
    if (table) {
      table.isAuthRequired = config.isAuthRequired;
    }
  } else if (authConfigType.value === 'column') {
    // 查找列
    for (const tableId in tableColumnsCache.value) {
      const columns = tableColumnsCache.value[tableId];
      const column = columns.find(c => c.id === authConfigItemId.value);
      if (column) {
        column.isAuthRequired = config.isAuthRequired;
        break;
      }
    }
  }

  // 关闭弹窗
  closeAuthConfigModal();

  // 显示成功消息
  messageService.success('配置保存成功');
};

// 申请授权相关方法已注释

// 打开数据源申请授权弹窗
// const openDatasourceAuthRequest = () => {
//   if (!dataSource.value) return;

//   console.log('打开数据源申请授权弹窗，数据源信息:', dataSource.value);

//   authRequestType.value = 'datasource';
//   authRequestItemId.value = dataSource.value.id;
//   authRequestItemName.value = dataSource.value.name;

//   showAuthRequestModal.value = true;
// };

// 打开表申请授权弹窗
// const openTableAuthRequest = (table: any) => {
//   console.log('打开表申请授权弹窗，表信息:', table);

//   authRequestType.value = 'table';
//   authRequestItemId.value = table.id;
//   authRequestItemName.value = getTableDisplayName(table);

//   showAuthRequestModal.value = true;
// };

// 打开列申请授权弹窗
// const openColumnAuthRequest = (column: any) => {
//   console.log('打开列申请授权弹窗，列信息:', column);

//   authRequestType.value = 'column';
//   authRequestItemId.value = column.id;
//   authRequestItemName.value = column.name || column.columnName;

//   showAuthRequestModal.value = true;
// };

// 关闭申请授权弹窗
// const closeAuthRequestModal = () => {
//   showAuthRequestModal.value = false;
// };

// 申请授权成功回调
// const onAuthRequestSuccess = () => {
//   messageService.success('权限申请已提交，请等待审核');
//   closeAuthRequestModal();
// };

// 获取表列信息的辅助函数
const getTableColumns = (tableId: string): TableColumn[] => {
  if (tableId && tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value]) {
    return tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value];
  }
  return [];
};

// 检查表是否有列信息
const hasTableColumns = (tableId: string): boolean => {
  return !!tableId && // 使用!!确保转换为布尔值
         !!tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] &&
         Array.isArray(tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value]) &&
         tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value].length > 0;
};

// 检查表是否有空列信息（已加载但为空）
const hasEmptyTableColumns = (tableId: string): boolean => {
  return !!tableId && // 使用!!确保转换为布尔值
         !!tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value] &&
         Array.isArray(tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value]) &&
         tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value].length === 0;
};

// 更安全地获取表格列信息数组
const safeGetTableColumns = (tableId: string): boolean => {
  if (!tableId) return false;
  const columns = tableColumnsCache.value[tableId as string];
  return Array.isArray(columns) && columns.length === 0;
};

// 使用索引安全地判断列是否为空
const isTableColumnsEmpty = (tableId: string): boolean => {
  if (!tableId) return false;

  try {
    // 使用类型断言来告诉TypeScript这是安全的
    const columns = tableColumnsCache.value[tableId as keyof typeof tableColumnsCache.value];
    return Array.isArray(columns) && columns.length === 0;
  } catch (error) {
    return false;
  }
};

// 加载表的列信息
const loadTableColumns = async (tableId: string, tableName: string, schemaName?: string) => {
  console.log('加载表列信息', tableId, tableName);

  // 如果缓存中已有此表的列信息，直接使用缓存
  if (tableColumnsCache.value[tableId]) {
    console.log('使用缓存的列信息');
    return tableColumnsCache.value[tableId];
  }

  try {
    const dataSourceId = dataSource.value?.id;
    if (!dataSourceId) throw new Error('数据源ID无效');

    // 构建API URL
    const columnsUrl = schemaName
      ? getMetadataApiUrl('columns', { dataSourceId, tableName, schema: schemaName })
      : getMetadataApiUrl('columns', { tableId });

    console.log('获取表列信息的URL:', columnsUrl);

    const columnsResponse = await instance.get(columnsUrl);
    const columnsData = columnsResponse.data;

    // 处理不同格式的响应
    if (columnsData.success && Array.isArray(columnsData.data)) {
      // 标准格式：{success:true, data:[...]}
      tableColumnsCache.value[tableId] = columnsData.data;
      return columnsData.data;
    } else if (Array.isArray(columnsData)) {
      // 直接返回数组的情况
      tableColumnsCache.value[tableId] = columnsData;
      return columnsData;
    } else {
      console.error('获取表列信息失败:', columnsData);
      return [];
    }
  } catch (error) {
    console.error('加载表列信息失败:', error);
    messageService.error('加载表列信息失败: ' + (error instanceof Error ? error.message : String(error)));
    return [];
  }
};

// 获取表的列信息 (直接调用API版本)
const fetchTableColumnsByName = async (tableName: string, schemaName: string) => {
  // 参数校验
  if (!tableName || typeof tableName !== 'string') {
    console.error('tableName无效:', tableName);
    return [];
  }

  if (!schemaName || typeof schemaName !== 'string') {
    console.error('schemaName无效:', schemaName);
    return [];
  }

  if (!dataSource.value?.id) {
    console.error('数据源ID无效');
    return [];
  }

  try {
    // 直接通过数据源ID, schema名和表名获取列信息
    const columnsUrl = getMetadataApiUrl('columns', {
      dataSourceId: dataSource.value.id,
      schema: schemaName,
      tableName: tableName
    });

    console.log('直接获取表列信息的URL:', columnsUrl);

    const response = await instance.get(columnsUrl);
    const data = response.data;

    // 处理不同格式的响应
    if (data.success && Array.isArray(data.data)) {
      // 标准格式：{success:true, data:[...]}
      console.log(`获取到表 ${tableName} 的列信息:`, data.data.length);
      return data.data;
    } else if (Array.isArray(data)) {
      // 直接返回数组的情况
      console.log(`获取到表 ${tableName} 的列信息(数组格式):`, data.length);
      return data;
    } else {
      console.error('直接获取表列信息失败:', data);
      return [];
    }
  } catch (error) {
    console.error('直接获取表列信息失败:', error);
    return [];
  }
};

// 在 script 部分添加计算属性和方法
// 在合适的位置添加下面的代码（建议放在其他计算属性附近）
const parsedConnectionParams = computed(() => {
  if (!dataSource.value?.connectionParams) return null;

  // 处理字符串化的 JSON
  if (typeof dataSource.value.connectionParams === 'string') {
    try {
      return JSON.parse(dataSource.value.connectionParams);
    } catch (e) {
      console.error('解析连接参数失败:', e);
      return {};
    }
  }

  // 已经是对象的情况
  return dataSource.value.connectionParams;
});

// 格式化选项名称 - 使其更友好
const formatOptionName = (key: string): string => {
  const nameMap: Record<string, string> = {
    'connectionTimeout': '连接超时',
    'maxPoolSize': '最大连接数',
    'schema': '模式',
    'useSSL': 'SSL连接',
    'autoReconnect': '自动重连',
    'useTLS': 'TLS加密',
    'characterEncoding': '字符编码',
    'useCompression': '压缩传输',
    'serverTimezone': '服务器时区'
  };

  return nameMap[key] || key;
};

// 格式化选项值
const formatOptionValue = (value: any): string => {
  if (value === '' || value === undefined || value === null) {
    return '未设置';
  }

  if (value === 'true' || value === true) {
    return '是';
  }

  if (value === 'false' || value === false) {
    return '否';
  }

  return String(value);
};

// 脱敏配置相关方法

// 打开列脱敏配置弹窗
const openColumnDesensitizeConfig = (column: any) => {
  console.log('打开列脱敏配置弹窗，列信息:', column);

  desensitizeConfigColumnId.value = column.id;
  desensitizeConfigColumnName.value = column.name || column.columnName;

  // 设置脱敏授权状态
  const authDesensitize = column.authDesensitize === true;
  
  // 设置脱敏类型
  const formatType = column.formatType || '';

  console.log('列脱敏授权状态:', authDesensitize, '原始值:', column.authDesensitize);
  console.log('列脱敏类型:', formatType, '原始值:', column.formatType);

  desensitizeConfigInitialAuthDesensitize.value = authDesensitize;
  desensitizeConfigInitialFormatType.value = formatType;

  showDesensitizeConfigModal.value = true;
};

// 关闭脱敏配置弹窗
const closeDesensitizeConfigModal = () => {
  showDesensitizeConfigModal.value = false;
};

// 保存脱敏配置
const saveDesensitizeConfig = async (config: any) => {
  // 查找列并更新本地数据
  for (const tableId in tableColumnsCache.value) {
    const columns = tableColumnsCache.value[tableId];
    const column = columns.find(c => c.id === desensitizeConfigColumnId.value);
    if (column) {
      // 更新脱敏配置
      column.authDesensitize = config.authDesensitize;
      column.formatType = config.formatType;
      break;
    }
  }

  // 关闭弹窗
  closeDesensitizeConfigModal();

  // 显示成功消息
  messageService.success('脱敏配置保存成功');
};
</script>

<template>
  <div class="bg-white shadow rounded-lg overflow-hidden container-card">
    <div v-if="isLoading" class="p-6 text-center">
      <div class="animate-pulse flex justify-center">
        <div class="h-6 w-6 bg-indigo-200 rounded-full"></div>
      </div>
      <p class="mt-2 text-sm text-gray-500">加载数据源信息...</p>
    </div>

    <template v-else-if="dataSource">
      <!-- 数据源基本信息 -->
      <div class="border-b border-gray-200">
        <div class="px-6 py-5 sm:flex sm:items-center sm:justify-between">
          <div>
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              {{ dataSource.name }}
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
              {{ dataSource.description || '无描述' }}
            </p>
          </div>
          <div class="mt-3 flex sm:mt-0 sm:ml-4">
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-2"
                @click="handleEdit"
              >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              编辑
              </button>
              <button
                type="button"
                :class="['inline-flex items-center px-3 py-1.5 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 mr-2', dataSource.isAuthRequired ? 'border-yellow-300 text-yellow-800 bg-yellow-50 hover:bg-yellow-100 focus:ring-yellow-500' : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500']"
                @click="openDatasourceAuthConfig"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                授权配置
                <span v-if="dataSource.isAuthRequired" class="ml-1 text-xs">
                  (已启用)
                </span>
              </button>
              <!-- 数据源申请授权按钮已注释 -->
              <!-- <button
                v-if="dataSource.isAuthRequired"
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-blue-300 shadow-sm text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2"
                @click="openDatasourceAuthRequest"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                申请授权
              </button> -->
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-2"
                @click="handleDelete"
              >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              删除
            </button>
              <button
                type="button"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                @click="handleRefresh"
              >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新
            </button>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="px-6 py-5 grid-container">
        <!-- 左侧：连接信息 -->
        <div class="connection-info">
          <h4 class="text-base font-medium text-gray-700 mb-4">连接信息</h4>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">类型</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ getDataSourceTypeName(dataSource.type) }}
              </dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">状态</dt>
              <dd class="mt-1 text-sm">
                <div class="mt-4 flex items-center">
                  <span class="px-2 py-1 text-xs rounded-full"
                    :class="dataSource.status && ['active', 'ACTIVE'].includes(dataSource.status) ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                  >
                    {{ dataSource.status && ['active', 'ACTIVE'].includes(dataSource.status) ? '活跃' : '未连接' }}
                  </span>
                </div>
              </dd>
            </div>
            <div class="sm:col-span-2">
              <dt class="text-sm font-medium text-gray-500">连接字符串</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ `${dataSource.type ? dataSource.type.toLowerCase() : 'unknown'}://${dataSource.host || 'localhost'}:${dataSource.port || ''}/${dataSource.databaseName || ''}` }}
              </dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">主机</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ dataSource.host }}</dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">端口</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ dataSource.port }}</dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">数据库名称</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ dataSource.databaseName }}</dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">用户名</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ dataSource.username }}</dd>
            </div>
          </dl>
        </div>

        <!-- 右侧：高级设置 -->
        <div class="advanced-settings">
          <h4 class="text-base font-medium text-gray-700 mb-4">高级设置</h4>
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">同步频率</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ formatSyncFrequency(dataSource.syncFrequency) }}
              </dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">最后同步时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ dataSource.lastSyncTime ? (dataSource.lastSyncTime.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/) ? dataSource.lastSyncTime : new Date(dataSource.lastSyncTime).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })) : '从未同步' }}
              </dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">创建时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ dataSource.createdAt ? (dataSource.createdAt.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/) ? dataSource.createdAt : new Date(dataSource.createdAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })) : '未知' }}
              </dd>
            </div>
            <div class="sm:col-span-1">
              <dt class="text-sm font-medium text-gray-500">最后更新时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ dataSource.updatedAt ? (dataSource.updatedAt.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/) ? dataSource.updatedAt : new Date(dataSource.updatedAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })) : '未知' }}
              </dd>
            </div>
            <div class="sm:col-span-2">
              <dt class="text-sm font-medium text-gray-500">连接选项</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <div v-if="parsedConnectionParams && Object.keys(parsedConnectionParams).length > 0" class="bg-gray-50 rounded-md p-3">
                  <div class="grid grid-cols-2 gap-3">
                    <div v-for="(value, key) in parsedConnectionParams" :key="key" class="flex items-center">
                      <span class="font-medium text-gray-700 mr-2">{{ formatOptionName(key) }}:</span>
                      <span class="text-gray-800">{{ formatOptionValue(value) }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="text-gray-500 text-sm">无附加连接选项</div>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- 数据库元数据 -->
      <div class="border-t border-gray-200 px-6 py-5">
        <!-- 元数据视图选项卡 -->
        <div v-if="viewMode === 'metadata'">
          <div class="flex items-center justify-between mb-5">
            <h4 class="text-base font-medium text-gray-700">数据库元数据</h4>
            <div class="flex space-x-3">
              <button
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                @click="syncMetadata"
                :disabled="isSyncingMetadata"
              >
                <template v-if="isSyncingMetadata">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  同步中...
                </template>
                <template v-else>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  同步元数据
                </template>
              </button>
            </div>
          </div>

          <!-- 搜索框和表格 -->
          <div class="mt-4">
            <div class="flex mb-4">
              <div class="relative w-64">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                  </svg>
                </div>
                <input
                  v-model="searchKeyword"
                  type="text"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="搜索表..."
                />
              </div>
              <!-- 调试信息 -->
              <div class="ml-4 text-xs text-gray-500">
                <span v-if="!isMetadataLoading">表数量: {{ tables.length }} | 已过滤: {{ filteredTables.length }}</span>
              </div>
            </div>

            <!-- 加载中状态 -->
            <div v-if="isMetadataLoading" class="py-10 text-center">
              <div class="animate-pulse flex justify-center">
                <div class="h-5 w-5 bg-indigo-200 rounded-full"></div>
              </div>
              <p class="mt-2 text-sm text-gray-500">加载元数据...</p>
            </div>

            <!-- 无数据状态 -->
            <div v-else-if="!tables.length" class="py-10 text-center bg-gray-50 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">暂无表信息</h3>
              <p class="mt-1 text-sm text-gray-500">请点击"同步元数据"按钮获取数据库表信息</p>
            </div>

            <div v-else class="overflow-hidden border border-gray-200 rounded-md">
              <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-md table-container">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      表名
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      列数
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <template v-for="table in filteredTables" :key="getTableDisplayName(table)">
                    <tr class="hover:bg-gray-50 cursor-pointer" @click="toggleTableExpand(table)">
                      <td class="py-3 px-6 whitespace-nowrap">
                        <div class="flex items-center">
                          <span class="transform transition-transform mr-2" :class="{'rotate-90': expandedTables[getTableId(table)]}">
                            ▶
                          </span>
                          {{ getTableDisplayName(table) }}
                        </div>
                      </td>
                      <td class="py-3 px-6 whitespace-nowrap">{{ table.type || 'TABLE' }}</td>
                      <td class="py-3 px-6 whitespace-nowrap text-center">{{ getColumnCount(table) }}</td>
                      <td class="py-3 px-6 text-right">
                        <div class="flex space-x-1">
                          <button
                            @click.stop="openTableAuthConfig(table)"
                            :class="[
                              'inline-flex items-center px-2 py-1 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2',
                              table.isAuthRequired
          ? 'border-yellow-300 text-yellow-800 bg-yellow-50 hover:bg-yellow-100 focus:ring-yellow-500'
          : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500'
  ]
  "
  title = "授权配置"
                          >
  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
  授权配置
  <span v-if="table.isAuthRequired" class="ml-1 text-xs">
      (已启用)
      </span>
                          </button>
                          <!-- 表申请授权按钮已注释 -->
                          <!-- <button
                            v-if="table.isAuthRequired"
                            @click.stop="openTableAuthRequest(table)"
                            class="inline-flex items-center px-2 py-1 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            title="申请授权"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            申请授权
                          </button> -->
                        </div>
                      </td>
                    </tr>

                    <!-- 展开的表列信息 -->
                    <tr v-if="expandedTables[getTableId(table)]">
                      <td colspan="4" class="p-0">
                        <div class="bg-gray-50 p-4 border-t border-b border-gray-200">
                          <div class="text-sm font-medium text-gray-700 mb-2">列信息</div>

                          <!-- 表格头部 -->
                          <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-md column-table">
                            <thead class="bg-gray-50">
                              <tr>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">列名</th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据类型</th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">允许为空</th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主键</th>
                                <th scope="col" class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                              <template v-if="getTableColumns(getTableId(table)).length > 0">
                                <tr v-for="column in getTableColumns(getTableId(table))" :key="column.name || column.columnName">
                                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center">
                                      {{ column.name || column.columnName }}
                                      <span v-if="column.isEncrypted" class="ml-2 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800" title="需要解密">
                                        加密
                                      </span>
                                    </div>
                                  </td>
                                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ column.dataType || column.type }}</td>
                                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ column.nullable !== false ? '是' : '否' }}</td>
                                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ column.primaryKey ? '是' : '否' }}</td>
                                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 text-right">
                                    <div class="flex space-x-1">
                                      <button
                                        @click.stop="openColumnAuthConfig(column)"
                                        :class="[
                                          'inline-flex items-center px-2 py-1 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2',
                                          column.isAuthRequired
                                            ? 'border-yellow-300 text-yellow-800 bg-yellow-50 hover:bg-yellow-100 focus:ring-yellow-500'
                                            : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500'
                                        ]"
                                        title="授权配置"
                                      >
  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
  授权配置
  <span v-if="column.isAuthRequired" class="ml-1 text-xs">
                                          (已启用)
                                        </span>
      </button>
                                      <button
                                        @click.stop="openColumnDecryptConfig(column)"
                                        :class="[
                                          'inline-flex items-center px-2 py-1 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2',
                                          column.isEncrypted
                                            ? 'border-green-300 text-green-800 bg-green-50 hover:bg-green-100 focus:ring-green-500'
                                            : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500'
                                        ]"
                                        title="解密配置"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                                        </svg>
                                        解密配置
                                        <span v-if="column.isEncrypted" class="ml-1 text-xs">
                                          (已启用)
                                        </span>
                                      </button>
                                      <button
                                        @click.stop="openColumnDesensitizeConfig(column)"
                                        :class="[
                                          'inline-flex items-center px-2 py-1 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2',
                                          column.authDesensitize
                                            ? 'border-green-300 text-green-800 bg-green-50 hover:bg-green-100 focus:ring-green-500'
                                            : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500'
                                        ]"
                                        title="脱敏配置"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                        脱敏配置
                                        <span v-if="column.authDesensitize" class="ml-1 text-xs">
                                          (已启用)
                                        </span>
                                      </button>
                                      <!-- 列申请授权按钮已注释 -->
                                      <!-- <button
                                        v-if="column.isAuthRequired"
                                        @click.stop="openColumnAuthRequest(column)"
                                        class="inline-flex items-center px-2 py-1 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        title="申请授权"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        申请授权
                                      </button> -->
                                    </div>
      </td>
                                </tr>
                              </template>
                              <tr v-else>
                                <td colspan="5" class="px-3 py-4 text-center text-gray-500">
                                  <div v-if="isTableColumnsEmpty(getTableId(table))">
                                    无列信息
                                  </div>
                                  <div v-else class="flex justify-center items-center space-x-2">
                                    <svg class="animate-spin h-5 w-5 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>加载中...</span>
                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 表数据预览 -->
        <div v-else-if="viewMode === 'preview' && selectedTable" class="mt-4">
          <div class="flex justify-between items-center mb-4 px-4">
            <button
              class="text-sm flex items-center text-gray-600 hover:text-gray-900"
              @click="backToMetadata"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              返回表列表
            </button>
            <h3 class="text-lg font-medium text-gray-900">
              {{ selectedTable }}
            </h3>
            <div></div> <!-- 保持对称 -->
          </div>

          <TableDataPreview
            :dataSourceId="dataSourceId"
            :tableName="selectedTable"
            :tableId="getSelectedTableId()"
          />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="p-6 text-center">
        <p class="text-gray-500">数据源不存在或已被删除</p>
      </div>
    </template>

          <!-- 授权配置弹窗 -->
          <AuthConfigModal
            :visible="showAuthConfigModal"
            :type="authConfigType"
            :item-id="authConfigItemId"
            :item-name="authConfigItemName"
            :initial-is-auth-required="authConfigInitialIsAuthRequired"
            @close="closeAuthConfigModal"
            @save="saveAuthConfig"
          />

          <!-- 解密配置弹窗 -->
          <DecryptConfigModal
            :visible="showDecryptConfigModal"
            :column-id="decryptConfigColumnId"
            :column-name="decryptConfigColumnName"
            :initial-config="decryptConfigInitialConfig"
            @close="closeDecryptConfigModal"
            @save="saveDecryptConfig"
          />

          <!-- 脱敏配置弹窗 -->
          <DesensitizeConfigModal
            :visible="showDesensitizeConfigModal"
            :column-id="desensitizeConfigColumnId"
            :column-name="desensitizeConfigColumnName"
            :initial-auth-desensitize="desensitizeConfigInitialAuthDesensitize"
            :initial-format-type="desensitizeConfigInitialFormatType"
            @close="closeDesensitizeConfigModal"
            @save="saveDesensitizeConfig"
          />

          <!-- 申请授权弹窗已注释 -->
          <!-- <AuthRequestModal
            :visible="showAuthRequestModal"
            :type="authRequestType"
            :item-id="authRequestItemId"
            :item-name="authRequestItemName"
            @close="closeAuthRequestModal"
            @success="onAuthRequestSuccess"
          /> -->
  </div>
</template>

<style>
/* 容器查询样式 */
.container-card {
  container-type: inline-size;
  container-name: card;
  transition: all 0.3s ease;
}

.container-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@container card (min-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr 1fr;
  }
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

@container card (max-width: 640px) {
  .table-container th,
  .table-container td {
    padding: 0.5rem 0.75rem;
  }

  .table-container th:nth-child(3),
  .table-container td:nth-child(3) {
    display: none;
  }
}

.column-table {
  width: 100%;
  overflow-x: auto;
}

@container card (max-width: 768px) {
  .column-table th,
  .column-table td {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .column-table th:nth-child(5),
  .column-table td:nth-child(5) {
    display: none;
  }
}
</style>
