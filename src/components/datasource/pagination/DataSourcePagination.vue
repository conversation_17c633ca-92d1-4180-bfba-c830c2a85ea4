<script setup lang="ts">
import { computed } from 'vue'
import Pagination from '@/components/common/Pagination.vue'

// 组件属性
const props = defineProps<{
  total: number;
  page: number;
  size: number;
  totalPages: number;
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'change', page: number): void;
}>()

// 处理页码变化事件
const handlePageChange = (page: number) => {
  emit('change', page)
}
</script>

<template>
  <Pagination 
    v-if="totalPages > 1"
    :current-page="page"
    :total-items="total"
    :page-size="size"
    @page-change="handlePageChange"
  />
</template>