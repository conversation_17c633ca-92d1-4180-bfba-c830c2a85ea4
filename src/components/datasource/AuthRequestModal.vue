<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useMessageService } from '@/services/message';
import { useResponseHandler } from '@/utils/api';
import { getUserPermissionStatus, requestPermission } from '@/api/auth';
import type { AuthRequestType, UserPermissionStatus, AuthRequest } from '@/types/auth';

// 组件属性
const props = defineProps<{
  visible: boolean;
  type: 'datasource' | 'schema' | 'table' | 'column';
  itemId: string;
  itemName: string;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'success'): void;
}>();

// 消息服务
const messageService = useMessageService();
const { handleResponse } = useResponseHandler();

// 组件状态
const isLoading = ref(false);
const isSubmitting = ref(false);
const reason = ref('');
const permissionStatus = ref<UserPermissionStatus | null>(null);

// 标题计算属性
const modalTitle = computed(() => {
  const typeMap = {
    'datasource': '数据源',
    'schema': 'Schema',
    'table': '表',
    'column': '列字段'
  };
  return `申请${typeMap[props.type] || '项目'}访问权限`;
});

// 权限状态文本
const permissionStatusText = computed(() => {
  if (!permissionStatus.value) return '';
  
  if (permissionStatus.value.hasPermission) {
    return '您已拥有此资源的访问权限';
  }
  
  if (permissionStatus.value.requestStatus) {
    const statusMap = {
      'PENDING': '权限申请审核中',
      'APPROVED': '权限申请已通过',
      'REJECTED': '权限申请已被拒绝',
      'EXPIRED': '权限申请已过期'
    };
    return statusMap[permissionStatus.value.requestStatus] || '未知状态';
  }
  
  return '您暂无此资源的访问权限';
});

// 权限状态样式
const permissionStatusClass = computed(() => {
  if (!permissionStatus.value) return '';
  
  if (permissionStatus.value.hasPermission) {
    return 'text-green-600 bg-green-50 border-green-200';
  }
  
  if (permissionStatus.value.requestStatus) {
    const statusClassMap = {
      'PENDING': 'text-yellow-600 bg-yellow-50 border-yellow-200',
      'APPROVED': 'text-green-600 bg-green-50 border-green-200',
      'REJECTED': 'text-red-600 bg-red-50 border-red-200',
      'EXPIRED': 'text-gray-600 bg-gray-50 border-gray-200'
    };
    return statusClassMap[permissionStatus.value.requestStatus] || '';
  }
  
  return 'text-gray-600 bg-gray-50 border-gray-200';
});

// 是否可以申请权限
const canRequestPermission = computed(() => {
  if (!permissionStatus.value) return false;
  
  // 已有权限或正在审核中，不能再申请
  if (permissionStatus.value.hasPermission || permissionStatus.value.requestStatus === 'PENDING') {
    return false;
  }
  
  return true;
});

// 转换权限类型
const getAuthRequestType = (type: string): AuthRequestType => {
  const typeMap: Record<string, AuthRequestType> = {
    'datasource': 'DATASOURCE',
    'schema': 'SCHEMA',
    'table': 'TABLE',
    'column': 'COLUMN'
  };
  return typeMap[type] || 'DATASOURCE';
};

// 加载权限状态
const loadPermissionStatus = async () => {
  if (!props.itemId) return;
  
  isLoading.value = true;
  try {
    const response = await getUserPermissionStatus(
      getAuthRequestType(props.type),
      props.itemId
    );
    
    handleResponse(response, {
      showSuccessMessage: false,
      errorMessage: '获取权限状态失败'
    });
    
    permissionStatus.value = response.data;
  } catch (error) {
    console.error('获取权限状态失败:', error);
    // 如果API不存在，使用模拟数据
    permissionStatus.value = {
      hasPermission: false,
      requestStatus: undefined
    };
  } finally {
    isLoading.value = false;
  }
};

// 提交权限申请
const submitRequest = async () => {
  if (!reason.value.trim()) {
    messageService.warning('请填写申请理由');
    return;
  }
  
  isSubmitting.value = true;
  try {
    const request: AuthRequest = {
      resourceType: getAuthRequestType(props.type),
      resourceId: props.itemId,
      resourceName: props.itemName,
      reason: reason.value.trim()
    };
    
    const response = await requestPermission(request);
    
    handleResponse(response, {
      successMessage: '权限申请提交成功，请等待审核',
      errorMessage: '权限申请提交失败'
    });
    
    // 重新加载权限状态
    await loadPermissionStatus();
    
    // 清空申请理由
    reason.value = '';
    
    // 通知父组件
    emit('success');
  } catch (error) {
    console.error('权限申请失败:', error);
    messageService.error('权限申请提交失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 关闭弹窗
const closeModal = () => {
  reason.value = '';
  emit('close');
};

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadPermissionStatus();
  }
}, { immediate: true });

// 初始化
onMounted(() => {
  if (props.visible) {
    loadPermissionStatus();
  }
});
</script>

<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <!-- 弹窗标题 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ modalTitle }}</h3>
      </div>

      <!-- 弹窗内容 -->
      <div class="px-6 py-4">
        <!-- 资源信息 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">申请资源</h4>
          <div class="bg-gray-50 rounded-md p-3">
            <p class="text-sm text-gray-900">{{ itemName }}</p>
            <p class="text-xs text-gray-500 mt-1">{{ modalTitle.replace('申请', '').replace('访问权限', '') }}</p>
          </div>
        </div>

        <!-- 权限状态 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">当前状态</h4>
          <div v-if="isLoading" class="text-sm text-gray-500">
            正在查询权限状态...
          </div>
          <div v-else-if="permissionStatus" 
               :class="['text-sm px-3 py-2 rounded-md border', permissionStatusClass]">
            {{ permissionStatusText }}
            <div v-if="permissionStatus.requestTime" class="text-xs mt-1">
              申请时间: {{ permissionStatus.requestTime }}
            </div>
            <div v-if="permissionStatus.reason" class="text-xs mt-1">
              申请理由: {{ permissionStatus.reason }}
            </div>
          </div>
        </div>

        <!-- 申请表单 -->
        <div v-if="canRequestPermission" class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            申请理由 <span class="text-red-500">*</span>
          </label>
          <textarea
            v-model="reason"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            rows="4"
            placeholder="请详细说明您申请此权限的原因和用途..."
            :disabled="isSubmitting"
          ></textarea>
          <p class="mt-1 text-xs text-gray-500">
            请详细说明申请权限的原因，以便管理员审核
          </p>
        </div>
      </div>

      <!-- 弹窗按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button
          type="button"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          @click="closeModal"
          :disabled="isSubmitting"
        >
          关闭
        </button>
        <button
          v-if="canRequestPermission"
          type="button"
          class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="submitRequest"
          :disabled="isSubmitting || !reason.trim()"
        >
          <span v-if="isSubmitting">提交中...</span>
          <span v-else>提交申请</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
