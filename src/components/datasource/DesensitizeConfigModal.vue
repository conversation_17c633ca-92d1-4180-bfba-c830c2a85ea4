<script lang="ts" setup>
import {computed, onMounted, ref, watch} from 'vue';
import {useMessageService} from '@/services/message';
import {useResponseHandler} from '@/utils/api';
import instance from "@/utils/axios";
import { DESENSITIZE_TYPE_CONFIGS, DesensitizeType } from '@/types/desensitize';

// 组件属性
const props = defineProps<{
  visible: boolean;
  columnId: string;
  columnName: string;
  initialAuthDesensitize?: boolean;
  initialFormatType?: string;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'save', config: any): void;
}>();

// 消息服务
const messageService = useMessageService();
const {handleResponse} = useResponseHandler();

// 组件状态
const isLoading = ref(false);
const authDesensitize = ref(false);
const formatType = ref('');

// 标题计算属性
const modalTitle = computed(() => {
  return `列字段脱敏配置 - ${props.columnName}`;
});

// 脱敏类型选项
const desensitizeOptions = computed(() => {
  return DESENSITIZE_TYPE_CONFIGS.map(config => ({
    value: config.code,
    label: config.name,
    description: config.description
  }));
});

// 获取当前选中的脱敏类型描述
const getSelectedDescription = (type: string) => {
  const config = DESENSITIZE_TYPE_CONFIGS.find(c => c.code === type);
  return config ? config.description : '';
};

// 初始化表单数据
const initFormData = () => {
  console.log('初始化脱敏配置表单数据:', {
    initialAuthDesensitize: props.initialAuthDesensitize,
    initialFormatType: props.initialFormatType
  });

  // 设置脱敏授权状态 - 使用严格的类型检查
  authDesensitize.value = props.initialAuthDesensitize === true;

  // 设置脱敏类型
  formatType.value = props.initialFormatType || '';
};

// 监听props变化，重新初始化表单
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initFormData();
  }
}, {immediate: true});

// 监听初始值的变化
watch(() => [props.initialAuthDesensitize, props.initialFormatType], () => {
  if (props.visible) {
    initFormData();
  }
});

// 监听授权状态变化，如果取消勾选则清空脱敏类型
watch(() => authDesensitize.value, (newValue) => {
  if (!newValue) {
    formatType.value = '';
  }
});

// 初始化
onMounted(() => {
  initFormData();
});

// 保存配置
const saveConfig = async () => {
  isLoading.value = true;

  try {
    console.log('保存脱敏配置:', {
      columnId: props.columnId,
      authDesensitize: authDesensitize.value,
      formatType: formatType.value
    });

    const requestData = {
      authDesensitize: authDesensitize.value,
      formatType: authDesensitize.value ? formatType.value : '',
      id: props.columnId
    };

    // 调用API更新脱敏配置
    const response = await instance.put('/api/metadata/auth/desensitize', requestData);

    // 处理响应
    handleResponse(response, {
      showSuccessMessage: false,
      errorMessage: '脱敏配置保存失败'
    });

    // 显示成功消息
    messageService.success('脱敏配置保存成功');

    // 发送保存事件
    emit('save', {
      authDesensitize: authDesensitize.value,
      formatType: authDesensitize.value ? formatType.value : ''
    });

    // 关闭弹窗
    emit('close');

  } catch (error) {
    console.error('保存脱敏配置失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 关闭弹窗
const closeModal = () => {
  emit('close');
};
</script>

<template>
  <div v-if="visible" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
      <!-- 弹窗头部 -->
      <div class="flex items-center justify-between pb-3 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ modalTitle }}</h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="py-4">
        <div class="space-y-4">
          <!-- 脱敏授权开关 -->
          <div class="flex items-center">
            <input
              id="authDesensitize"
              v-model="authDesensitize"
              type="checkbox"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="authDesensitize" class="ml-2 block text-sm text-gray-900">
              是否需要单独申请脱敏授权
            </label>
          </div>

          <!-- 脱敏类型选择 -->
          <div v-if="authDesensitize" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              脱敏类型
            </label>
            <div class="flex items-center space-x-2">
              <select
                v-model="formatType"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">请选择脱敏类型</option>
                <option
                  v-for="option in desensitizeOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>

              <!-- 描述提示图标 -->
              <div v-if="formatType" class="relative group">
                <svg
                  class="w-5 h-5 text-gray-400 hover:text-gray-600 cursor-help"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <!-- 工具提示 -->
                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                  {{ getSelectedDescription(formatType) }}
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 说明文字 -->
          <div class="text-sm text-gray-600">
            启用后，用户需要单独申请授权才能查看字段脱敏前的值
          </div>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div class="flex items-center justify-end pt-3 border-t border-gray-200 space-x-2">
        <button
          @click="closeModal"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
        >
          取消
        </button>
        <button
          @click="saveConfig"
          :disabled="isLoading"
          class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isLoading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            保存中...
          </span>
          <span v-else>保存</span>
        </button>
      </div>
    </div>
  </div>
</template>
