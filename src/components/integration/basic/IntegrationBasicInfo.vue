<template>
  <div class="bg-white shadow rounded-lg overflow-hidden">
    <div class="px-4 py-5 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900">
      基本信息
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- 名称 -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
            集成名称 <span class="text-red-500">*</span>
          </label>
          <input
            id="name"
            v-model="integrationData.name"
            type="text"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm force-border"
            placeholder="请输入集成名称"
            @input="validateName"
          />
          <p v-if="errors.name" class="mt-1 text-sm text-red-600">
            {{ errors.name }}
          </p>
        </div>

        <!-- 状态 -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
            状态
          </label>
          <select
            id="status"
            v-model="integrationData.status"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm force-border"
          >
            <option value="DRAFT">草稿</option>
            <option value="ACTIVE">已激活</option>
            <option value="INACTIVE">已禁用</option>
          </select>
        </div>
      </div>

      <!-- 数据源与查询选择 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- 数据源选择 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            选择数据源 <span class="text-red-500">*</span>
          </label>
          <DataSourceSelector
            v-model="integrationData.dataSourceId"
            placeholder="请选择数据源"
            :required="true"
            module-key="INTERATION"
            @selected="handleDataSourceSelected"
          />
          <p v-if="errors.dataSourceId" class="mt-1 text-sm text-red-600">
            {{ errors.dataSourceId }}
          </p>
        </div>

        <!-- 查询选择 -->
        <div>
          <QuerySearchSelector
            :current-query-id="integrationData.queryId"
            :data-source-id="integrationData.dataSourceId"
            @update:query="handleQueryChange"
            @selected="handleQuerySelected"
          />
          <p v-if="errors.queryId" class="mt-1 text-sm text-red-600">
            {{ errors.queryId }}
          </p>
          <p v-if="integrationData.dataSourceId && !integrationData.queryId" class="mt-1 text-sm text-indigo-600">
            <i class="fas fa-info-circle mr-1"></i> 查询选择会自动筛选当前数据源的查询
          </p>
        </div>
      </div>

      <!-- 查询版本 -->
      <div>
        <QueryVersionSelector
          v-model="integrationData.versionId"
          :query-id="integrationData.queryId"
          label="查询版本"
          :required="true"
          :error="errors.versionId"
        />
      </div>

      <!-- 描述 -->
      <div>
        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
          描述
        </label>
        <input
          id="description"
          v-model="integrationData.description"
          type="text"
          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm force-border"
          placeholder="请输入集成描述（选填）"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps和defineEmits是编译器宏，不需要导入
import { ref, reactive, watch } from 'vue';
import DataSourceSelector from '@/components/datasource/DataSourceSelector.vue';
import QuerySearchSelector from '@/components/query/QuerySearchSelector.vue';
import QueryVersionSelector from '@/components/query/QueryVersionSelector.vue';
import type { Integration } from '@/types/integration';
import instance from '@/utils/axios';


// 版本类型定义
interface QueryVersion {
  id: string;
  versionNumber: number;
  isLatest: boolean;
  status: string;
}

const props = defineProps<{
  integration: Integration;
  validationTriggered?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:integration', value: Integration): void;
  (e: 'data-source-selected', id: string, dataSource: any): void;
  (e: 'query-selected', id: string, query: any): void;
  (e: 'validation', valid: boolean, errors: Record<string, string>): void;
}>();



// 版本相关状态
const versions = ref<QueryVersion[]>([]);
const isLoadingVersions = ref(false);

// 本地状态，避免直接修改props
const integrationData = reactive<Integration & { versionId?: string }>({
  ...props.integration,
  versionId: '' // 初始化版本ID
});

// 本地错误状态
const errors = reactive({
  name: '',
  dataSourceId: '',
  queryId: '',
  versionId: ''
});

// 验证表单
const validateForm = () => {
  let isValid = true;

  // 重置错误
  errors.name = '';
  errors.dataSourceId = '';
  errors.queryId = '';
  errors.versionId = '';

  // 验证名称
  if (!integrationData.name.trim()) {
    errors.name = '请输入集成名称';
    isValid = false;
  }

  // 验证数据源
  if (!integrationData.dataSourceId) {
    errors.dataSourceId = '请选择数据源';
    isValid = false;
  }

  // 验证查询
  if (!integrationData.queryId) {
    errors.queryId = '请选择数据查询';
    isValid = false;
  }

  // 验证版本
  if (integrationData.queryId && !integrationData.versionId) {
    errors.versionId = '请选择查询版本';
    isValid = false;
  }

  // 触发父组件验证事件
  emit('validation', isValid, errors);

  return isValid;
};

// 单独验证名称字段
const validateName = () => {
  errors.name = !integrationData.name.trim() ? '请输入集成名称' : '';

  // 重新验证整个表单
  validateForm();
};

// 监听props变化更新本地状态
watch(() => props.integration, (newVal) => {
  Object.assign(integrationData, newVal);
}, { deep: true });

// 监听本地状态变化更新父组件
watch(integrationData, (newVal) => {
  emit('update:integration', { ...newVal });
}, { deep: true });

// 监听验证触发器
watch(() => props.validationTriggered, (newVal) => {
  if (newVal) {
    validateForm();
  }
});

// 处理数据源选择
const handleDataSourceSelected = (id: string, dataSource: any) => {
  integrationData.dataSourceId = id;

  // 如果当前查询不属于新选择的数据源，清空查询选择
  if (integrationData.queryId) {
    const currentQuery = queries.value.find(q => q.id === integrationData.queryId);
    if (currentQuery && currentQuery.dataSourceId !== id) {
      integrationData.queryId = '';
      // 同时清空版本
      integrationData.versionId = '';
      versions.value = [];
    }
  }

  // 触发父组件事件
  emit('data-source-selected', id, dataSource);

  // 重新验证表单
  errors.dataSourceId = '';
  validateForm();
};

// 处理查询选择
const handleQueryChange = (id: string) => {
  integrationData.queryId = id;

  // 清空版本选择
  integrationData.versionId = '';

  // 重新验证表单
  errors.queryId = '';
  validateForm();
};

// 处理查询选择完成事件
const handleQuerySelected = (query: any) => {
  // 触发父组件事件
  emit('query-selected', query.id, query);
};
</script>

<style scoped>
.force-border {
  border: 1px solid #d1d5db !important;
}
</style>
