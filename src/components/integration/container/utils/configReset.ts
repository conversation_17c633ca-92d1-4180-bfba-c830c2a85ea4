/**
 * 集成配置重置工具
 */
import type { TableConfig, ChartConfig } from '@/types/unified-integration';

/**
 * 重置表格配置
 * @returns 新的表格配置对象
 */
export const resetTableConfig = (): TableConfig => {
  return {
    columns: [],
    pagination: { 
      enabled: true,
      pageSize: 10, 
      showSizeChanger: true,
      pageSizeOptions: [10, 20, 50, 100]
    },
    actions: [],
    export: {
      enabled: true,
      formats: ['CSV', 'EXCEL'],
      maxRows: 1000
    },
    batchActions: [],
    aggregation: {
      enabled: false,
      groupByFields: [],
      aggregationFunctions: []
    },
    advancedFilters: {
      enabled: false,
      defaultFilters: [],
      savedFilters: []
    },
    rowSelection: false,
    showHeader: true,
    bordered: true,
    showFooter: false
  };
};

/**
 * 重置图表配置
 * @returns 新的图表配置对象
 */
export const resetChartConfig = (): ChartConfig => {
  return {
    type: 'bar',
    title: '',
    description: '',
    height: 400,
    showLegend: true,
    animation: true,
    theme: 'default',
    dataMapping: {
      xField: '',
      yField: '',
      seriesField: '',
      valueField: '',
      categoryField: '',
      sizeField: '',
      colorField: '',
      nameField: ''
    },
    styleOptions: {
      backgroundColor: '',
      colors: []
    },
    interactions: {
      enableZoom: false,
      enablePan: false,
      tooltipMode: 'single'
    }
  };
};