/**
 * 集成验证工具函数
 */
import type { IntegrationData, QueryParam } from '@/types/unified-integration';

/**
 * 验证整个集成表单
 * @param integration 集成数据
 * @param queryParams 查询参数
 * @param validationErrors 验证错误对象
 * @param validateParamsCallback 用于验证参数的回调函数
 * @returns 验证结果，true 表示通过验证
 */
export const validateIntegrationForm = (
  integration: IntegrationData,
  queryParams: QueryParam[],
  validationErrors: Record<string, string>,
  validateParamsCallback?: () => boolean
): boolean => {
  console.log('[integrationValidation] 开始验证表单数据:', {
    integrationType: integration.type,
    hasDataSource: !!integration.dataSourceId,
    hasQuery: !!integration.queryId,
    queryParamsCount: queryParams?.length || 0,
    hasTableConfig: !!integration.tableConfig,
    hasChartConfig: !!integration.chartConfig
  });

  // 清空之前的错误信息
  Object.keys(validationErrors).forEach(key => delete validationErrors[key]);
  
  // 基本信息验证
  let isValid = true;
  
  // 集成名称验证
  if (!integration.name || integration.name.trim() === '') {
    validationErrors['name'] = '请输入集成名称';
    isValid = false;
    console.log('[integrationValidation] 名称验证失败');
  }
  
  // 数据源验证
  if (!integration.dataSourceId) {
    validationErrors['dataSourceId'] = '请选择数据源';
    isValid = false;
    console.log('[integrationValidation] 数据源验证失败');
  }
  
  // 查询ID验证
  if (!integration.queryId) {
    validationErrors['queryId'] = '请选择数据查询';
    isValid = false;
    console.log('[integrationValidation] 查询验证失败');
  }
  
  // 查询版本验证
  if (integration.queryId && !integration.versionId) {
    validationErrors['versionId'] = '请选择查询版本';
    isValid = false;
    console.log('[integrationValidation] 版本验证失败');
  }
  
  // 查询参数验证：根据查询类型判断是否需要参数
  // 图表模式和简单表格模式下不验证查询条件数量
  const shouldValidateParamsCount = integration.queryId && 
                                    integration.type !== 'CHART' && 
                                    integration.type !== 'SIMPLE_TABLE' && 
                                    !(integration.meta && integration.meta.noParamsRequired === true);
  
  // 只有在非图表模式且非简单表格模式下才验证查询条件的数量
  if (shouldValidateParamsCount && (!queryParams || queryParams.length === 0)) {
    validationErrors['queryParams'] = '请至少添加一个查询条件';
    isValid = false;
    console.log('[integrationValidation] 查询参数验证失败: 没有添加条件');
  } else if (queryParams && queryParams.length > 0) {
    // 已经有查询条件了，验证其格式
    // 对于图表模式，不需要必填项验证，仅保留格式验证
    if (validateParamsCallback) {
      try {
        const paramsValid = validateParamsCallback();
        if (!paramsValid) {
          // 检查是否是因为必填项导致的验证失败，如果是，则忽略
          const formatErrors = Object.keys(validationErrors).filter(key => 
            !key.includes('必填项') && 
            !key.includes('不能为空')
          );
          
          // 只有在存在格式错误等其他错误时才报告验证失败
          if (formatErrors.length > 0) {
            validationErrors['queryParamsValues'] = '请检查查询条件的格式是否正确';
            isValid = false;
            console.log('[integrationValidation] 查询参数格式验证失败');
          }
        }
      } catch (error) {
        console.error('[integrationValidation] 执行参数验证回调时出错:', error);
        validationErrors['queryParamsValues'] = '查询条件验证过程出错';
        isValid = false;
      }
    }
  }
  
  // 针对不同集成类型的特殊验证
  if (integration.type === 'TABLE' || integration.type === 'SIMPLE_TABLE') {
    // 验证表格配置
    if (!integration.tableConfig) {
      validationErrors['tableConfig'] = '表格配置不存在，请重新初始化配置';
      isValid = false;
      console.log('[integrationValidation] 表格配置验证失败: 配置不存在');
    } else if (!integration.tableConfig.columns || integration.tableConfig.columns.length === 0) {
      validationErrors['tableColumns'] = '请在表格配置中至少添加一个表格列，可通过"添加列"按钮完成';
      isValid = false;
      console.log('[integrationValidation] 表格配置验证失败: 没有列配置');
    }
  } else if (integration.type === 'CHART') {
    // 验证图表配置
    if (!integration.chartConfig) {
      validationErrors['chartConfig'] = '图表配置不存在，请重新初始化配置';
      isValid = false;
      console.log('[integrationValidation] 图表配置验证失败: 配置不存在');
    } else {
      if (!integration.chartConfig.dataMapping) {
        validationErrors['chartMapping'] = '图表数据映射配置不存在';
        isValid = false;
        console.log('[integrationValidation] 图表配置验证失败: 数据映射不存在');
      } else {
        if (!integration.chartConfig.dataMapping.xField) {
          validationErrors['chartXField'] = '请在图表配置的"数据映射"选项卡中选择X轴字段';
          isValid = false;
          console.log('[integrationValidation] 图表配置验证失败: 没有X轴字段');
        }
        
        if (!integration.chartConfig.dataMapping.yField) {
          validationErrors['chartYField'] = '请在图表配置的"数据映射"选项卡中选择Y轴字段';
          isValid = false;
          console.log('[integrationValidation] 图表配置验证失败: 没有Y轴字段');
        }
      }
    }
  }
  
  console.log('[integrationValidation] 验证结果:', isValid, '错误信息:', validationErrors);
  
  return isValid;
};

/**
 * 整理错误消息用于用户显示
 * @param validationErrors 验证错误对象
 * @returns 整理后的错误信息
 */
export const formatValidationErrors = (validationErrors: Record<string, string>): {
  mainError: string;
  detailMessage: string | null;
  errorCount: number;
} => {
  // 收集所有错误消息
  const errorMessages = Object.values(validationErrors);
  
  // 使用第一个错误作为主要消息
  const mainError = errorMessages[0] || '未知错误';
  
  // 如果有多个错误，生成一个详细的错误列表
  let detailMessage = null;
  if (errorMessages.length > 1) {
    detailMessage = `共有 ${errorMessages.length} 个错误需要修复`;
  }
  
  return {
    mainError,
    detailMessage,
    errorCount: errorMessages.length
  };
};