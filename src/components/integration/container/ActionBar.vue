<template>
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-xl font-bold text-gray-900">{{ isCreateMode ? '创建集成' : '编辑集成' }}</h1>
    </div>
    <div class="flex items-center">
      <button
        v-if="showPreviewButton"
        type="button"
        class="mr-3 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-indigo-600 bg-white hover:bg-gray-50 focus:outline-none"
        @click="preview"
        :disabled="!integrationId || loading || saveLoading"
      >
        <i class="fas fa-eye mr-1"></i> 预览
      </button>
      <button
        v-if="showExportButton"
        type="button"
        class="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
        @click="exportJson"
        :disabled="loading || saveLoading"
      >
        <i class="fas fa-file-export mr-1"></i> 导出JSON
      </button>
      <button
        v-if="!isCreateMode"
        type="button"
        class="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-gray-50 focus:outline-none"
        @click="publishToLowCode"
        :disabled="!integrationId || loading || saveLoading || publishLoading"
      >
        <i class="fas fa-cloud-upload-alt mr-1"></i> {{ publishLoading ? '发布中...' : '发布低代码' }}
      </button>
      <button
        type="button"
        class="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
        @click="cancel"
      >
        取消
      </button>
      <button
        type="button"
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
        @click="save"
        :disabled="loading || saveLoading"
      >
        <i class="fas fa-save mr-1"></i> {{ saveLoading ? '保存中...' : '保存' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps和defineEmits是编译器宏，不需要导入

const props = defineProps({
  isCreateMode: {
    type: Boolean,
    required: true
  },
  integrationId: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  saveLoading: {
    type: Boolean,
    default: false
  },
  publishLoading: {
    type: Boolean,
    default: false
  },
  showExportButton: {
    type: Boolean,
    default: true
  },
  showPreviewButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['preview', 'export-json', 'cancel', 'save', 'publish-to-lowcode']);

const preview = () => {
  emit('preview');
};

const exportJson = () => {
  emit('export-json');
};

const cancel = () => {
  emit('cancel');
};

const save = () => {
  emit('save');
};

const publishToLowCode = () => {
  emit('publish-to-lowcode');
};
</script>
