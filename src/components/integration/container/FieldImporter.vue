<template>
  <div>
    <button
      @click="loadFieldsAsQueryParams"
      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      :disabled="!dataSourceId || !queryId"
    >
      <i class="fas fa-download mr-2"></i> 从数据源导入字段
    </button>
  </div>
</template>

<script setup lang="ts">
import { message } from '@/services/message';
import type { QueryParam } from '@/types/unified-integration';
import instance from '@/utils/axios';
import { globalApiRequestManager } from '@/utils/requestManager';

const props = defineProps({
  dataSourceId: {
    type: String,
    required: true
  },
  queryId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['fields-loaded']);
// 使用导入的message服务

// 将数据类型转换为参数类型
const convertDataTypeToParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
    case 'number':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'boolean';

    case 'string':
    case 'varchar':
    case 'char':
    case 'text':
    default:
      return 'string';
  }
};

// 获取表单类型
const getFormTypeFromParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'number':
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'checkbox';

    case 'select':
    case 'enum':
      return 'select';

    case 'textarea':
    case 'text_area':
      return 'textarea';

    default:
      return 'text';
  }
};

// 方法：从数据源获取字段列表
const fetchFieldsFromDataSource = async () => {
  if (!props.dataSourceId || !props.queryId) {
    message.error({
      content: '无法导入字段',
      description: '请先选择数据源和查询后再尝试导入',
      duration: 5000
    });
    return [];
  }

  try {
    message.info({
      content: '正在加载字段',
      description: '正在从数据源加载字段，请稍候...',
      duration: 3000
    });

    // 请求API获取字段数据
    const url = `/api/queries/${props.queryId}/parameters`;
    console.log('[QueryFields] 请求URL:', url);

    const response = await globalApiRequestManager.request(
      () => instance.get(url),
      url,
      { queryId: props.queryId },
      { cacheTime: 30000 }
    );
    const data = response.data;
    console.log('[QueryFields] 接收到的响应:', data);

    // 检查API响应是否成功 - 修改从fields获取为从parameters获取
    if (data.success === true && data.data && data.data.parameters) {
      console.log('[QueryFields] 成功获取参数:', data.data.parameters);
      return data.data.parameters;
    } else {
      console.warn('[QueryFields] 响应中没有找到parameters数组');

      // 调试：如果是在开发环境中，可以使用一些模拟数据进行测试
      if (import.meta.env.DEV) {
        console.log('[QueryFields] 开发环境中，尝试使用示例数据');

        // 这里我们可以添加示例数据用于测试
        const mockData = [
          { name: "id", type: "String", label: "id" },
          { name: "name", type: "String", label: "名称" },
          { name: "description", type: "String", label: "描述" },
          { name: "type", type: "String", label: "类型" },
          { name: "host", type: "String", label: "主机" },
          { name: "port", type: "Integer", label: "端口" },
          { name: "database_name", type: "String", label: "数据库名" },
          { name: "schema", type: "String", label: "架构" },
          { name: "username", type: "String", label: "用户名" },
          { name: "created_at", type: "Date", label: "创建时间" },
          { name: "updated_at", type: "Date", label: "更新时间" }
        ];

        console.log('[QueryFields] 使用示例数据:', mockData);
        return mockData;
      }

      return [];
    }
  } catch (error) {
    console.error('[QueryFields] 获取字段失败:', error);

    // 调试：如果是在开发环境中且发生错误，也可以使用模拟数据
    if (import.meta.env.DEV) {
      console.log('[QueryFields] 开发环境中出错，使用模拟数据');

      // 使用与上面相同的模拟数据
      return [
        { name: "id", type: "String", label: "id" },
        { name: "name", type: "String", label: "名称" },
        { name: "description", type: "String", label: "描述" },
        { name: "type", type: "String", label: "类型" },
        { name: "created_at", type: "Date", label: "创建时间" },
        { name: "updated_at", type: "Date", label: "更新时间" }
      ];
    }

    message.error({
      content: '获取字段失败',
      description: '无法从数据源中获取字段信息，请检查数据源配置或网络连接',
      duration: 5000
    });
    return [];
  }
};

// 将字段导入为查询参数
const loadFieldsAsQueryParams = async () => {
  try {
    const fields = await fetchFieldsFromDataSource();

    if (!fields || fields.length === 0) {
      message.info({
      content: '未获取到字段',
      description: '从数据源未获取到任何字段信息，请检查数据源配置',
      duration: 3000
    });
      return;
    }

    console.log(`[ImportFields] 开始处理${fields.length}个字段`);

    // 将字段转换为参数格式
    const queryParams: QueryParam[] = fields.map((field: any, index: number) => {
      // 确保field是对象格式
      const fieldObj = typeof field === 'string' ? { name: field } : field;

      // 获取原始类型和标签
      const originalType = fieldObj.type || 'String';
      const label = fieldObj.label || fieldObj.name;

      // 转换数据类型为参数类型
      const paramType = convertDataTypeToParamType(originalType);

      // 根据参数类型获取表单类型
      const formType = getFormTypeFromParamType(originalType);

      console.log(`[ImportFields] 处理字段: ${fieldObj.name}, 原始类型: ${originalType}, 转换为参数类型: ${paramType}, 表单类型: ${formType}`);

      // 创建参数对象
      const paramObj: QueryParam = {
        name: fieldObj.name,
        type: paramType,
        description: label,
        format: originalType.toLowerCase(),
        formType: formType,
        required: false,
        isNewParam: true,
        options: [],
        displayOrder: index
      };

      // 针对特殊类型设置额外属性
      if (paramType === 'number') {
        // 为数字类型添加默认验证范围
        if (originalType.toLowerCase() === 'integer' || originalType.toLowerCase() === 'int') {
          paramObj.minValue = -2147483648;  // INT的最小值
          paramObj.maxValue = 2147483647;   // INT的最大值
        }
      } else if (paramType === 'string') {
        // 为字符串类型添加默认最大长度
        paramObj.maxLength = 255;
      } else if (paramType === 'date') {
        // 为日期类型设置默认格式
        paramObj.dateFormat = 'YYYY-MM-DD';
      }

      return paramObj;
    });

    console.log('[ImportFields] 已生成参数列表:', queryParams);

    // 添加成功提示
    message.success(`成功从数据源导入${fields.length}个字段作为参数`);

    // 发送导入的字段到父组件
    emit('fields-loaded', queryParams);

  } catch (error) {
    console.error('[ImportFields] 导入字段失败:', error);
    message.error({
      content: '导入字段失败',
      description: '在处理字段数据时出错，请重试或联系管理员',
      duration: 5000
    });
  }
};
</script>
