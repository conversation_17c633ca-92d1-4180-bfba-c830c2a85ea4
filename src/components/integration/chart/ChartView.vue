<template>
  <div class="chart-view" :style="{ height: `${height}px` }">
    <div ref="chartContainer" class="chart-container"></div>
    <div v-if="loading" class="loading-overlay">
      <a-spin />
    </div>
    <div v-if="error" class="error-message">
      <a-alert type="error" :message="error" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import { message } from 'ant-design-vue';
import axios from 'axios';

// 定义组件Props
const props = defineProps({
  chartType: {
    type: String,
    default: 'line',
    validator: (value: string) => ['line', 'bar', 'pie', 'scatter'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  // 通用字段
  categoryField: String,
  valueField: String,
  // 饼图特有字段
  nameField: String,
  // 散点图特有字段
  xField: String,
  yField: String,
  sizeField: String,
  // 样式相关
  height: {
    type: Number,
    default: 400
  },
  style: {
    type: Object,
    default: () => ({})
  },
  // 数据源相关
  dataUrl: String,
  queryParams: {
    type: Object,
    default: () => ({})
  },
  headers: {
    type: Object,
    default: () => ({})
  },
  // 静态数据
  data: {
    type: Array,
    default: () => []
  }
});

// 响应式状态
const chartContainer = ref<HTMLElement | null>(null);
const chart = ref<echarts.ECharts | null>(null);
const chartData = ref<any[]>([]);
const loading = ref(false);
const error = ref('');

// 监听配置变化
watch(
  () => [
    props.chartType,
    props.title,
    props.categoryField,
    props.valueField,
    props.nameField,
    props.xField,
    props.yField,
    props.sizeField,
    props.height,
    props.style,
    props.data
  ],
  () => {
    nextTick(() => {
      if (props.data && props.data.length > 0) {
        chartData.value = [...props.data];
        renderChart();
      } else if (props.dataUrl) {
        fetchData();
      }
    });
  },
  { deep: true }
);

// 监听URL和查询参数变化
watch(
  () => [props.dataUrl, props.queryParams, props.headers],
  () => {
    if (props.dataUrl) {
      fetchData();
    }
  },
  { deep: true }
);

// 获取数据
const fetchData = async () => {
  if (!props.dataUrl) {
    error.value = '未提供数据源URL';
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    const response = await axios.request({
      url: props.dataUrl,
      method: 'GET',
      params: props.queryParams,
      headers: props.headers
    });

    // 处理各种可能的响应格式
    if (response.data) {
      if (Array.isArray(response.data)) {
        chartData.value = response.data;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        chartData.value = response.data.data;
      } else if (response.data.result && Array.isArray(response.data.result)) {
        chartData.value = response.data.result;
      } else if (response.data.items && Array.isArray(response.data.items)) {
        chartData.value = response.data.items;
      } else {
        error.value = '无法解析响应数据格式';
      }

      if (chartData.value.length === 0) {
        error.value = '获取到的数据为空';
      }
    } else {
      error.value = '获取数据失败，响应为空';
    }
  } catch (err: any) {
    console.error('获取图表数据失败:', err);
    error.value = `获取数据失败: ${err.message || '未知错误'}`;
  } finally {
    loading.value = false;
    if (!error.value) {
      renderChart();
    }
  }
};

// 渲染图表
const renderChart = () => {
  if (!chartContainer.value) {
    return;
  }

  if (chartData.value.length === 0) {
    error.value = '没有可用的数据';
    return;
  }

  if (!chart.value) {
    chart.value = echarts.init(chartContainer.value);
  }

  try {
    const option = generateChartOption();
    chart.value.setOption(option, true);
  } catch (err: any) {
    console.error('渲染图表失败:', err);
    error.value = `渲染图表失败: ${err.message || '未知错误'}`;
  }
};

// 生成图表配置
const generateChartOption = () => {
  const baseOption: any = {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: props.chartType === 'pie' ? 'item' : 'axis'
    },
    grid: {
      containLabel: true,
      left: '3%',
      right: '4%',
      bottom: '3%'
    },
    ...props.style
  };

  // 根据图表类型生成不同的配置
  switch (props.chartType) {
    case 'line':
      return generateLineBarOption(baseOption, 'line');
    case 'bar':
      return generateLineBarOption(baseOption, 'bar');
    case 'pie':
      return generatePieOption(baseOption);
    case 'scatter':
      return generateScatterOption(baseOption);
    default:
      throw new Error(`不支持的图表类型: ${props.chartType}`);
  }
};

// 生成折线图和柱状图的配置
const generateLineBarOption = (baseOption: any, type: 'line' | 'bar') => {
  if (!props.categoryField || !props.valueField) {
    throw new Error(`${type === 'line' ? '折线图' : '柱状图'}必须指定分类字段和数值字段`);
  }

  // 获取所有唯一的类别
  const categories = Array.from(new Set(chartData.value.map(item => item[props.categoryField as string])));

  // 提取数据系列
  const seriesData = categories.map(category => {
    const value = chartData.value
      .filter(item => item[props.categoryField as string] === category)
      .reduce((sum, item) => sum + Number(item[props.valueField as string] || 0), 0);
    
    return {
      name: category,
      value: value
    };
  });

  return {
    ...baseOption,
    xAxis: {
      type: 'category',
      data: seriesData.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type,
        data: seriesData.map(item => item.value),
        emphasis: {
          focus: 'series'
        }
      }
    ]
  };
};

// 生成饼图的配置
const generatePieOption = (baseOption: any) => {
  if (!props.nameField || !props.valueField) {
    throw new Error('饼图必须指定名称字段和数值字段');
  }

  const seriesData = chartData.value.map(item => ({
    name: item[props.nameField as string],
    value: Number(item[props.valueField as string] || 0)
  }));

  return {
    ...baseOption,
    series: [
      {
        type: 'pie',
        radius: '50%',
        center: ['50%', '50%'],
        data: seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          formatter: '{b}: {c} ({d}%)'
        }
      }
    ]
  };
};

// 生成散点图的配置
const generateScatterOption = (baseOption: any) => {
  if (!props.xField || !props.yField) {
    throw new Error('散点图必须指定X轴字段和Y轴字段');
  }

  const seriesData = chartData.value.map(item => {
    const point = [
      Number(item[props.xField as string] || 0),
      Number(item[props.yField as string] || 0)
    ];
    
    // 如果有大小字段，添加为第三个值
    if (props.sizeField && item[props.sizeField]) {
      point.push(Number(item[props.sizeField] || 5));
    }

    return point;
  });

  return {
    ...baseOption,
    xAxis: {
      type: 'value',
      scale: true
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    series: [
      {
        type: 'scatter',
        data: seriesData,
        symbolSize: function(data: any) {
          // 如果有大小字段，使用第三个值作为大小；否则使用默认大小
          return props.sizeField && data.length > 2 ? data[2] : 10;
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: function(param: any) {
              return `(${param.data[0]}, ${param.data[1]})`;
            },
            position: 'top'
          }
        }
      }
    ]
  };
};

// 处理窗口大小调整
const handleResize = () => {
  if (chart.value) {
    chart.value.resize();
  }
};

// 销毁图表实例
const disposeChart = () => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
};

// 暴露刷新方法
defineExpose({
  refresh: () => {
    if (props.dataUrl) {
      fetchData();
    } else {
      renderChart();
    }
  }
});

// 生命周期钩子
onMounted(() => {
  if (props.data && props.data.length > 0) {
    chartData.value = [...props.data];
    nextTick(() => {
      renderChart();
    });
  } else if (props.dataUrl) {
    fetchData();
  }

  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  disposeChart();
});
</script>

<style scoped>
.chart-view {
  position: relative;
  width: 100%;
  min-height: 300px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  z-index: 10;
}
</style>