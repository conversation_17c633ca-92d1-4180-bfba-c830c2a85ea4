<template>
  <div class="page-header">
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <h1 class="text-xl font-semibold mb-0">{{ title }}</h1>
        <a-tag v-if="integrationType" color="blue" class="ml-2">{{ getIntegrationType(integrationType) }}</a-tag>
      </div>

      <div class="flex items-center space-x-2">
        <template v-for="(item, index) in actionItems" :key="index">
          <a-button
            :type="item.type || 'default'"
            @click="item.onClick"
          >
            <template #icon v-if="item.icon">
              <component :is="item.icon" />
            </template>
            {{ item.text }}
          </a-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps是编译器宏，不需要导入

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  integrationType: {
    type: String,
    default: ''
  },
  actionItems: {
    type: Array,
    default: () => []
  }
});

// 获取集成类型的显示名称
const getIntegrationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'TABLE': '高级表格',
    'SIMPLE_TABLE': '简单表格',
    'CHART': '图表'
  };
  return typeMap[type] || type;
};
</script>