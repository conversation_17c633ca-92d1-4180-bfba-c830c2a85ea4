<template>
  <section class="query-form-section">
    <div class="query-info-header mb-4">
      <div class="flex justify-between items-center">
        <h3 class="text-base font-medium mb-0">查询条件</h3>
        <a-tag v-if="integrationType" color="blue">{{ getIntegrationType(integrationType) }}</a-tag>
      </div>
    </div>

    <QueryForm
      v-if="conditions.length > 0"
      :conditions="conditions"
      :model-value="modelValue"
      @submit="$emit('submit')"
      @reset="$emit('reset')"
      class="query-form-content"
    />

    <div v-else class="empty-query-message p-4 mb-4 border border-dashed border-gray-300 rounded">
      <a-alert
        type="info"
        message="无查询条件可用"
        description="当前集成未配置查询条件，您可以继续查看数据或返回编辑集成添加查询条件。"
        show-icon
      />
    </div>
  </section>
</template>

<script setup lang="ts">
// defineProps和defineEmits是编译器宏，不需要导入
import QueryForm from '@/components/integration/preview/QueryForm.vue';

const props = defineProps({
  integrationType: {
    type: String,
    default: ''
  },
  conditions: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

defineEmits(['submit', 'reset', 'update:modelValue']);

// 获取集成类型的显示名称
const getIntegrationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'TABLE': '高级表格',
    'SIMPLE_TABLE': '简单表格',
    'CHART': '图表'
  };
  return typeMap[type] || type;
};
</script>

<style scoped>
.query-form-content {
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.query-info-header {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}
</style>