<template>
  <div>
    <!-- 本地存储恢复管理组件 - 无UI -->
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { resetTableConfig, resetChartConfig } from '@/components/integration/container/utils/configReset';

import type { IntegrationType, IntegrationStatus, IntegrationData, QueryParam } from '@/types/unified-integration';

// 定义组件属性
const props = defineProps<{
  integration: IntegrationData;
  queryParams: QueryParam[];
  paramValues: Record<string, any>;
  isCreateMode: boolean;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'params-loaded', params: QueryParam[]): void;
  (e: 'storage-restored'): void;
  (e: 'restore-error', error: Error): void;
}>();

// 本地状态

/**
 * 从本地存储恢复配置
 */
const restoreFromLocalStorage = async (integrationId: string) => {
  if (!integrationId) return false;
  
  try {
    // 首先尝试从localStorage获取数据
    const storageKey = `integration_config_${integrationId}`;
    const storedConfigJson = localStorage.getItem(storageKey);
    
    if (!storedConfigJson) {
      console.warn('[StorageRestore] 本地存储中未找到配置数据:', storageKey);
      return false;
    }
    
    const storedConfig = JSON.parse(storedConfigJson);
    console.log('[StorageRestore] 从本地存储中恢复的配置:', storedConfig);
    
    // 恢复查询参数
    if (storedConfig.queryParams && storedConfig.queryParams.length > 0) {
      console.log('[StorageRestore] 恢复queryParams数据');
      
      // 更新queryParams
      const restoredParams = storedConfig.queryParams.map((param: any) => ({
        ...param,
        isNewParam: false
      }));
      
      emit('params-loaded', restoredParams);
      
      // 初始化参数值
      restoredParams.forEach((param: any) => {
        props.paramValues[param.name] = param.defaultValue || '';
      });
    }
    
    // 恢复表格配置
    if (storedConfig.tableConfig && 
       (props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE')) {
      console.log('[StorageRestore] 恢复tableConfig数据');
      props.integration.tableConfig = storedConfig.tableConfig;
    }
    
    // 恢复图表配置
    if (storedConfig.chartConfig && props.integration.type === 'CHART') {
      console.log('[StorageRestore] 恢复chartConfig数据');
      props.integration.chartConfig = storedConfig.chartConfig;
    }
    
    console.log('[StorageRestore] 配置已从本地存储恢复完成');
    emit('storage-restored');
    return true;
  } catch (error) {
    console.error('[StorageRestore] 从本地存储恢复配置失败:', error);
    emit('restore-error', error as Error);
    return false;
  }
};

/**
 * 检查配置缺失情况并尝试恢复
 */
const checkAndRestoreConfig = async (data: any) => {
  if (!data) return;
  
  // 检查是否缺少关键配置数据
  const missingQueryParams = !data.queryParams || data.queryParams.length === 0;
  const missingTableConfig = (props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE') && 
                           (!data.tableConfig || !data.tableConfig.columns || data.tableConfig.columns.length === 0);
  const missingChartConfig = props.integration.type === 'CHART' && !data.chartConfig;
  
  // 添加详细日志信息
  console.log('[StorageRestore] 配置数据检查结果:', {
    missingQueryParams,
    missingTableConfig,
    missingChartConfig,
    hasQueryParams: data.queryParams ? data.queryParams.length : 0,
    hasTableConfig: data.tableConfig ? (data.tableConfig.columns ? data.tableConfig.columns.length : 0) : 0,
    hasChartConfig: !!data.chartConfig
  });
  
  // 如果缺少关键配置，尝试从本地存储中恢复
  if (missingQueryParams || missingTableConfig || missingChartConfig) {
    console.warn('[StorageRestore] 检测到API响应缺少关键配置数据，尝试从本地存储中恢复');
    
    // 尝试从localStorage恢复
    const restored = await restoreFromLocalStorage(data.id);
    
    // 如果未成功恢复，初始化默认配置
    if (!restored) {
      initializeDefaultConfigs();
    }
  } else {
    // 使用后端返回的配置
    console.log('[StorageRestore] 使用后端返回的配置数据');
    
    // 处理表格和图表配置
    if (data.tableConfig && (props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE')) {
      props.integration.tableConfig = data.tableConfig as any;
    }
    
    if (data.chartConfig && props.integration.type === 'CHART') {
      props.integration.chartConfig = data.chartConfig as any;
    }
    
    // 更新查询参数
    if (data.queryParams && data.queryParams.length > 0) {
      const params = data.queryParams.map((param: any) => ({
        ...param,
        isNewParam: false
      }));
      
      emit('params-loaded', params);
      
      // 初始化参数值
      params.forEach((param: any) => {
        props.paramValues[param.name] = param.defaultValue || '';
      });
    }
  }
};

/**
 * 初始化默认配置
 */
const initializeDefaultConfigs = () => {
  // 根据集成类型初始化默认配置
  if (props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE') {
    console.log('[StorageRestore] 初始化默认表格配置');
    props.integration.tableConfig = resetTableConfig();
  } else if (props.integration.type === 'CHART') {
    console.log('[StorageRestore] 初始化默认图表配置');
    props.integration.chartConfig = resetChartConfig();
  }
};

// 导出组件方法
defineExpose({
  restoreFromLocalStorage,
  checkAndRestoreConfig,
  initializeDefaultConfigs
});
</script>