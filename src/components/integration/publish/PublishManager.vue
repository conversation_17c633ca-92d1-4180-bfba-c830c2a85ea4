<template>
  <div>
    <!-- 发布到低代码状态弹窗 -->
    <PublishLowcodeModal
      :visible="showPublishModal"
      :progress="publishProgress"
      :current-step="publishStep"
      :error="publishError"
      :result="publishResult"
      @close="closePublishModal"
      @open-preview="openPreviewPage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { message } from '@/services/message';
import { useIntegrationStore } from '@/stores/integration';
import { transformFrontendIntegrationToApi } from '@/utils/apiTransformer';
import { convertToStandardConfig } from '@/utils/configConverter';
import PublishLowcodeModal from '@/components/integration/modals/PublishLowcodeModal.vue';
import type { IntegrationData } from '@/types/unified-integration';
// 导入集成服务
import { integrationService } from '@/services/integrationService';

// 引入工具函数
import { validateIntegrationForm } from '@/components/integration/container/utils/integrationValidation';

const props = defineProps<{
  integration: IntegrationData;
  queryParams: any[];
  tableConfig: any;
  chartConfig: any;
  validationErrors: Record<string, string>;
}>();

const emit = defineEmits<{
  (e: 'publish-started'): void;
  (e: 'publish-completed', result: { success: boolean, id?: string, message?: string }): void;
  (e: 'validate-form'): boolean;
  (e: 'save-integration'): Promise<void>;
}>();

// 消息Store
// 使用已导入的 message 服务
// 集成Store
const integrationStore = useIntegrationStore();

// 发布状态
const publishLoading = ref(false);
const showPublishModal = ref(false);
const publishProgress = ref(0);
const publishStep = ref('preparing');
const publishError = ref('');
const publishResult = ref<{ id: string, success: boolean, message: string } | null>(null);

// 组件挂载时添加调试日志
onMounted(() => {
  console.log('[PublishManager] 组件已挂载，showPublishModal初始值:', showPublishModal.value);
});

/**
 * 发布集成到低代码平台
 */
const publishToLowCode = async () => {
  console.log('[PublishManager] 开始发布流程');

  // 增加表单验证检查，如果未通过验证，不允许发布
  try {
    // 正确实现：我们需要等待验证结果
    const isValid = await new Promise<boolean>((resolve) => {
      // 触发验证表单事件
      emit('validate-form');

      // 给验证流程一点时间来完成
      setTimeout(() => {
        // 检查传入的验证错误对象是否为空
        const hasErrors = Object.keys(props.validationErrors).length > 0;
        console.log('[PublishManager] 验证完成，错误数量:', Object.keys(props.validationErrors).length);
        console.log('[PublishManager] 验证错误对象:', props.validationErrors);
        resolve(!hasErrors);
      }, 1000);
    });

    if (!isValid) {
      console.warn('[PublishManager] 表单验证未通过，中止发布流程');
      message.warning({
      content: '无法发布',
      description: '请先修复表单中的错误后再发布',
      duration: 4000
    });
      return;
    }

    console.log('[PublishManager] 表单验证通过，继续发布流程');
  } catch (error) {
    console.error('[PublishManager] 表单验证失败:', error);
    message.error({
      content: '验证失败',
      description: '验证表单时出错，请重试或联系管理员',
      duration: 5000
    });
    return;
  }

  // 重置状态
  publishProgress.value = 0;
  publishStep.value = 'preparing';
  publishError.value = '';
  publishResult.value = null;
  publishLoading.value = true;

  // 首先显示弹窗，让用户知道发布过程已经开始
  console.log('[PublishManager] 显示发布弹窗');
  showPublishModal.value = true;

  // 触发发布开始事件
  emit('publish-started');

  // 真实的发布流程实现
  try {
    // 准备阶段 - 10%
    publishProgress.value = 10;
    publishStep.value = 'preparing';
    console.log('[PublishManager] 准备发布配置数据');

    // 保存集成确保有ID
    if (!props.integration.id) {
      console.log('[PublishManager] 集成尚未保存，先触发保存');
      await emit('save-integration');
    }

    // 转换阶段 - 30%
    publishProgress.value = 30;
    publishStep.value = 'converting';
    console.log('[PublishManager] 转换为低代码格式');

    // 准备标准配置
    const standardConfig = await prepareStandardConfig();
    console.log('[PublishManager] 标准配置生成完成:', standardConfig);

    // 将标准配置转换为低代码平台格式
    const lowcodeConfig = await integrationService.convertToLowCode(standardConfig);
    console.log('[PublishManager] 低代码配置生成完成');

    // 发布阶段 - 60%
    publishProgress.value = 60;
    publishStep.value = 'publishing';
    console.log('[PublishManager] 开始发布到低代码平台');

    // 调用发布API
    try {
      const result = await integrationService.publishToLowCode(
        props.integration.id,
        props.integration.name,
        lowcodeConfig
      );

      // 完成阶段 - 100%
      publishProgress.value = 100;
      publishStep.value = 'completed';

      // 设置结果
      publishResult.value = result;

      if (result.success) {
        console.log('[PublishManager] 发布成功，ID:', result.id);
        emit('publish-completed', { success: true, id: result.id });
        message.success(result.message || '发布到低代码平台成功');
      } else {
        console.error('[PublishManager] 发布失败:', result.message);
        publishError.value = result.message || '发布失败';
        emit('publish-completed', { success: false, message: result.message });
        message.error(result.message || '发布失败');
      }
    } catch (apiError) {
      console.error('[PublishManager] 调用发布API失败:', apiError);
      publishProgress.value = 90;
      publishError.value = `发布API调用失败: ${apiError instanceof Error ? apiError.message : '未知API错误'}`;
      publishStep.value = 'error';

      emit('publish-completed', { success: false, message: publishError.value });
      message.error(publishError.value);
    }
  } catch (error) {
    console.error('[PublishManager] 发布过程中出错:', error);
    publishError.value = `发布过程中出错: ${error instanceof Error ? error.message : '未知错误'}`;
    emit('publish-completed', { success: false, message: publishError.value });
    message.error(`发布失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    console.log('[PublishManager] 发布流程结束');
    publishLoading.value = false;
  }
};

/**
 * 关闭发布弹窗
 */
const closePublishModal = () => {
  showPublishModal.value = false;
};

/**
 * 打开预览页面
 */
const openPreviewPage = (url: string) => {
  console.log('[PublishManager] 打开预览页面:', url);

  // 将预览URL保存到全局集成Store中
  integrationStore.lastPublishedPreviewUrl = url;

  // 在新窗口打开URL
  window.open(url, '_blank');
};

/**
 * 准备标准格式的配置数据
 * @returns 标准格式的配置对象
 */
const prepareStandardConfig = async () => {
  try {
    console.log('[PublishManager] prepareStandardConfig 开始生成标准配置');
    // 获取当前选择的queryId和versionId
    const queryId = props.integration.queryId || '';

    // 改进版本ID获取逻辑
    let versionId = '';

    // 如果versionId是对象（包含完整版本信息）
    if (typeof props.integration.versionId === 'object' && props.integration.versionId !== null) {
      // 从对象中获取ID
      versionId = props.integration.versionId.id || '';
      console.log('[PublishManager] 从对象中获取版本ID:', versionId);
    }
    // 如果是字符串，直接使用
    else if (typeof props.integration.versionId === 'string' && props.integration.versionId) {
      versionId = props.integration.versionId;
      console.log('[PublishManager] 使用字符串版本ID:', versionId);
    }
    // 如果没有版本ID，尝试从meta.apis中提取
    else if (props.integration.meta?.apis?.query?.path) {
      const apiPath = props.integration.meta.apis.query.path;
      console.log('[PublishManager] 尝试从API路径提取版本ID:', apiPath);

      // 使用正则表达式从API路径中提取版本ID
      const versionRegex = /\/versions\/([^\/]+)\/execute/;
      const match = apiPath.match(versionRegex);

      if (match && match[1] && match[1] !== queryId) {
        versionId = match[1];
        console.log('[PublishManager] 从API路径中提取到版本ID:', versionId);
      } else {
        console.warn('[PublishManager] 无法从API路径中提取有效的版本ID');
        versionId = ''; // 清空版本ID，避免使用查询ID作为版本ID
      }
    }
    // 如果没有版本ID，输出警告日志
    else {
      console.warn('[PublishManager] 未找到有效的版本ID，API请求可能无法正常工作');
      versionId = ''; // 清空版本ID，避免使用查询ID作为版本ID
    }

    // 检查网络请求中是否有实际的版本ID
    try {
      // 尝试从浏览器网络请求中查找最新的版本ID
      const findActualVersionId = () => {
        if (window.performance && window.performance.getEntriesByType) {
          const resources = window.performance.getEntriesByType('resource');

          for (const resource of resources) {
            const url = (resource as any).name;
            // 查找包含execute的API请求
            if (url.includes('/queries/') && url.includes('/versions/') && url.includes('/execute')) {
              console.log('[PublishManager] 找到可能的API请求:', url);

              // 使用正则表达式匹配版本ID
              const versionRegex = /\/versions\/([^\/]+)\/execute/;
              const match = url.match(versionRegex);

              if (match && match[1] && match[1] !== queryId) {
                const foundVersionId = match[1];
                console.log('[PublishManager] 从网络请求中找到版本ID:', foundVersionId);
                return foundVersionId;
              }
            }
          }
        }
        return null;
      };

      // 尝试从网络请求中获取版本ID
      const actualVersionId = findActualVersionId();
      if (actualVersionId) {
        console.log('[PublishManager] 使用从网络请求中发现的版本ID替换当前版本ID');
        versionId = actualVersionId;
      }
    } catch (e) {
      console.error('[PublishManager] 尝试从网络请求中获取版本ID时出错:', e);
    }

    // 当没有版本ID时通知用户
    if (!versionId) {
      console.warn('[PublishManager] 发布时未找到版本ID');
      message.warning({
      content: '查询版本信息不完整',
      description: '未找到查询版本ID，将使用基本查询路径',
      duration: 4000
    });
    }

    // 构建标准化的APIs定义
    const standardApis = {
      query: {
        method: 'POST', // 更新为POST方法
        path: versionId
          ? `/data-scope/api/queries/${queryId}/versions/${versionId}/${props.integration.id}/execute-append`
          : `/data-scope/api/queries/${queryId}/${props.integration.id}/execute-append` // 如果没有版本ID，使用无版本的路径
      },
      download: {
        method: 'POST', // 更新为POST方法
        path: versionId
          ? `/data-scope/api/excel/${queryId}/versions/${versionId}/${props.integration.id}/export`
          : `/data-scope/api/excel/${queryId}/${props.integration.id}/export`
      }
    };

    console.log('[PublishManager] prepareStandardConfig 构建集成数据对象');
    // 构建集成数据对象
    const integrationData: Record<string, any> = {
      id: props.integration.id,
      name: props.integration.name,
      description: props.integration.description,
      type: props.integration.type,
      status: props.integration.status,
      dataSourceId: props.integration.dataSourceId,
      queryId: props.integration.queryId,
      versionId: versionId,
      meta: {
        ...(props.integration.meta || {}),
        apis: standardApis
      }
    };

    // 添加查询参数
    integrationData.queryParams = props.queryParams || [];
    console.log('[PublishManager] prepareStandardConfig 添加查询参数:',
      integrationData.queryParams.length, '个参数');

    // 根据集成类型添加相应配置
    if (props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE') {
      console.log('[PublishManager] prepareStandardConfig 添加表格配置');
      if (!props.tableConfig) {
        console.error('[PublishManager] prepareStandardConfig 表格配置为空');
        throw new Error('表格配置不存在');
      }
      // 确保tableConfig包含所有必要的属性，尤其是rowActions
      integrationData.tableConfig = {
        ...props.tableConfig,
        // 如果tableConfig中没有显式包含rowActions，则将actions复制到rowActions
        rowActions: props.tableConfig.rowActions || props.tableConfig.actions || []
      };

      // 添加额外日志，用于诊断
      console.log('[PublishManager] 表格配置添加行操作按钮:');
      console.log('- tableConfig.actions数量:', props.tableConfig.actions?.length || 0);
      console.log('- tableConfig.actions内容:', JSON.stringify(props.tableConfig.actions || []));
      console.log('- 设置后的rowActions数量:', integrationData.tableConfig.rowActions?.length || 0);
    } else if (props.integration.type === 'CHART') {
      console.log('[PublishManager] prepareStandardConfig 添加图表配置');
      if (!props.chartConfig) {
        console.error('[PublishManager] prepareStandardConfig 图表配置为空');
        throw new Error('图表配置不存在');
      }
      integrationData.chartConfig = props.chartConfig;
    }

    // 使用API转换器转换数据
    console.log('[PublishManager] prepareStandardConfig 使用API转换器转换数据');
    const transformedData = transformFrontendIntegrationToApi(integrationData);
    console.log('[PublishManager] prepareStandardConfig 转换后数据:',
      Object.keys(transformedData), '类型:', props.integration.type);

    // 准备转换为标准格式的数据
    const standardInput = {
      queryId,
      meta: {
        database: integrationData.dataSourceId || '',
        schema: '',
        table: '',
        pageCode: typeof integrationData.id === 'string' ? integrationData.id : 'integration',
        // 包含 API 路径定义，确保使用标准化的APIs
        apis: standardApis
      },
      type: integrationData.type,
      queryParams: Array.isArray(transformedData.queryParams) ? transformedData.queryParams : [],
      tableConfig: props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE' ? {
        ...(transformedData.tableConfig || {}),
        columns: Array.isArray(transformedData.tableConfig?.columns) ? transformedData.tableConfig.columns : [],
        // 确保包含行操作按钮，优先使用已有的rowActions，如果没有则尝试使用actions
        rowActions: transformedData.tableConfig?.rowActions ||
                  transformedData.tableConfig?.actions || []
      } : null,
      chartConfig: props.integration.type === 'CHART' ? transformedData.chartConfig || null : null
    };

    console.log('[PublishManager] prepareStandardConfig 准备标准化数据成功');
    console.log('[PublishManager] 标准输入中的行操作来源检查:');
    console.log('- transformedData中的tableConfig.rowActions:',
                transformedData.tableConfig?.rowActions?.length || 0, '个');
    console.log('- transformedData中的tableConfig.actions:',
                transformedData.tableConfig?.actions?.length || 0, '个');
    console.log('- 最终使用的标准输入中的rowActions:',
                standardInput.tableConfig?.rowActions?.length || 0, '个');

    // 转换为标准格式
    try {
      console.log('[PublishManager] prepareStandardConfig 调用convertToStandardConfig');
      const result = await convertToStandardConfig(standardInput);
      console.log('[PublishManager] prepareStandardConfig 标准转换成功');

      // 添加详细的结果日志
      console.log('[PublishManager] 标准配置数据检查:');
      console.log('- filter字段数量:', result.filter ? result.filter.length : 0);
      console.log('- list字段数量:', result.list ? result.list.length : 0);
      console.log('- operation.rowActions详情:');
      console.log('  - 数量:', result.operation?.rowActions?.length || 0);
      console.log('  - 内容:', JSON.stringify(result.operation?.rowActions || []));
      console.log('- operation字段:', JSON.stringify(result.operation));

      return result;
    } catch (error) {
      console.error('[PublishManager] prepareStandardConfig 标准转换失败:', error);
      throw new Error(`标准格式转换失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  } catch (error) {
    console.error('[PublishManager] prepareStandardConfig 出错:', error);
    throw error;
  }
};

// 导出组件方法
defineExpose({
  publishToLowCode,
  closePublishModal
});
</script>
