<template>
  <div class="bg-white shadow rounded-lg overflow-visible mb-8">
    <div class="px-4 py-5 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-900 flex justify-between items-center">
      <div>查询条件配置</div>
      <div class="flex space-x-2">
        <button
          type="button"
          class="inline-flex items-center px-3 py-1 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          @click="openRequiredGroupsDrawer"
          title="管理必填组"
          data-testid="required-groups-button"
        >
          <i class="fas fa-layer-group mr-1"></i>
          管理必填组 ({{ fieldGroups.length }})
        </button>
        <button
          v-if="queryId"
          type="button"
          class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-indigo-600 bg-white hover:bg-gray-50 focus:outline-none"
          @click="loadParamsFromQuery"
          :disabled="!queryId"
        >
          <i class="fas fa-sync-alt mr-1"></i>
          初始化全部列
        </button>
      </div>
    </div>
    <div class="p-6">
      <div>
        <table class="min-w-full divide-y divide-gray-200 data-params-table">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">排序</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">字段</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">字段类型</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">中文名称</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 90px;">数据格式</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">表单类型</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 200px; max-width: 300px;">默认值</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">必填</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">必填组</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">操作</th>
            </tr>
          </thead>
          <draggable
            v-model="queryParams"
            tag="tbody"
            handle=".drag-handle"
            :animation="150"
            item-key="name"
            class="bg-white divide-y divide-gray-200"
            @change="onParamDragChange"
            :key="`params-list-${queryParams.length}`"
          >
            <template #item="{element: param, index}">
              <tr class="hover:bg-gray-50">
                <!-- 拖拽手柄 -->
                <td class="px-2 py-2 whitespace-nowrap">
                  <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 flex justify-center items-center w-full h-full">
                    <i class="fas fa-grip-vertical"></i>
                  </div>
                </td>

                <!-- 字段 -->
                <td class="px-4 py-2 text-sm text-gray-600" style="line-height: 1.3;">
                  <!-- 字段选择区域 - 统一使用可点击的下拉选择框 -->
                  <div class="space-y-1 relative">
                    <!-- 字段选择按钮 -->
                    <div
                      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md cursor-pointer field-selector"
                      :class="{
                        'active': activeDropdownIndex === index
                      }"
                      @click.stop="toggleFieldDropdown(index, $event)"
                    >
                      <div class="flex items-center p-2">
                        <div v-if="param.name" class="flex-1">
                          <div class="font-medium text-gray-900">{{ param.description || param.name || '' }}</div>
                          <div v-if="param.tableName" class="text-xs text-gray-500">{{ param.tableName }}</div>
                          <div class="text-xs text-gray-400">{{ param.name }}</div>
                        </div>
                        <span v-else class="flex-1 truncate">请选择字段</span>
                      </div>
                    </div>

                    <!-- 下拉搜索框 -->
                    <div
                      v-if="activeDropdownIndex === index"
                      class="absolute left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50"
                      style="width: 200px;"
                      @click.stop
                    >
                      <!-- 搜索输入框 -->
                      <div class="p-2 border-b">
                        <input
                          v-model="fieldSearchText"
                          :ref="el => setSearchInputRef(el, index)"
                          class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 field-search-input"
                          placeholder="搜索字段..."
                          @click.stop
                          @keydown.esc="closeFieldDropdown"
                        />
                      </div>

                      <!-- 字段列表 -->
                      <div class="max-h-60 overflow-y-auto p-0">
                        <div
                          v-for="field in filteredFieldsForSelection"
                          :key="field.name"
                          class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                          @click.stop="selectFieldFromDropdown(param, index, field)"
                          @mousedown.prevent
                        >
                          <div class="flex flex-col">
                            <span class="font-medium text-gray-900">{{ field.label || field.name }}</span>
                            <span class="text-xs text-gray-500">{{ field.tableName || '未知表' }}</span>
                            <span class="text-xs text-gray-400">{{ field.name }}</span>
                          </div>
                        </div>
                        <div
                          v-if="filteredFieldsForSelection.length === 0"
                          class="px-3 py-2 text-gray-500 text-sm"
                        >
                          无匹配字段
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 字段类型 -->
                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                  {{ param.type || '' }}
                </td>

                <!-- 中文名称 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <input
                    v-model="param.description"
                    class="uniform-height-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="参数描述"
                  />
                </td>

                <!-- 数据格式 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <div class="dropdown-menu-container">
                    <select
                      v-model="param.format"
                      class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      style="width: 100px;"
                    >
                      <option value="string">字符串</option>
                      <option value="int">整数</option>
                      <option value="decimal">小数</option>
                      <option value="enum">枚举</option>
                      <option value="date">日期</option>
                      <option value="date-time">日期时间</option>
                      <option value="card">身份证</option>
                      <option value="mobile">手机号</option>
                      <option value="uri">链接</option>
                      <option value="email">邮箱</option>
                      <option value="json">JSON</option>
                      <option value="boolean">布尔值</option>
                    </select>
                  </div>
                </td>

                <!-- 表单类型 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <div class="dropdown-menu-container">
                    <select
                      v-model="param.formType"
                      class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      @change="onFormTypeChange(param, index, $event)"
                    >
                      <option value="text">文本框</option>
                      <option value="textarea">多行文本</option>
                      <option value="number">数字输入框</option>
                      <option value="date">日期选择器</option>
                      <option value="date-range">日期时间区间</option>
                      <option value="select">下拉选择</option>
                      <option value="number_range">数字范围</option>
                    </select>
                  </div>
                </td>

                <!-- 参数值(原默认值列) -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <!-- 日期时间区间类型 -->
                  <div v-if="param.formType === 'date-range'" class="date-range-container">
                    <div class="date-range-input-group">
                      <div class="date-range-item">
                        <label class="date-range-label">开始日期</label>
                        <input
                          type="date"
                          v-model="paramValues[param.name + '_start']"
                          class="date-range-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          :class="{ 'border-red-300': validationErrors[param.name] }"
                          @change="onDateRangeChange(param.name, 'start', $event)"
                        />
                      </div>
                      <div class="date-range-item">
                        <label class="date-range-label">结束日期</label>
                        <input
                          type="date"
                          v-model="paramValues[param.name + '_end']"
                          class="date-range-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          :class="{ 'border-red-300': validationErrors[param.name] }"
                          @change="onDateRangeChange(param.name, 'end', $event)"
                        />
                      </div>
                    </div>
                  </div>
                  <!-- 单个日期类型 -->
                  <div v-else-if="param.formType === 'date' || param.type === 'date'">
                    <input
                      type="date"
                      v-model="paramValues[param.name]"
                      class="uniform-height-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      :class="{ 'border-red-300': validationErrors[param.name] }"
                      @change="onParamValueChange(param.name, $event)"
                    />
                  </div>
                  <!-- 枚举值特殊处理 -->
                  <div v-else-if="param.format === 'enum'" class="space-y-2">
                    <div class="flex space-x-2">
                      <select
                        v-model="paramValues[param.name]"
                        class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm flex-1"
                        :class="{ 'border-red-300': validationErrors[param.name] }"
                        @change="onParamValueChange(param.name, $event)"
                      >
                        <option value="">请选择</option>
                        <option
                          v-for="option in param.options || []"
                          :key="option.value"
                          :value="option.value"
                        >
                          {{ option.label || option.value }}
                        </option>
                      </select>
                      <button
                        @click="openEnumSelector(param, index)"
                        type="button"
                        class="px-2 py-1 border border-transparent text-xs rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none"
                        title="从枚举加载"
                      >
                        <i class="fas fa-database"></i>
                      </button>
                    </div>
                  </div>
                  <!-- 表单类型为下拉选择时，默认值也显示为下拉框 -->
                  <div v-else-if="param.formType === 'select'" class="space-y-2">
                    <select
                      v-model="paramValues[param.name]"
                      class="uniform-height-input ds-select shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm w-full"
                      :class="{ 'border-red-300': validationErrors[param.name] }"
                      @change="onParamValueChange(param.name, $event)"
                    >
                      <option value="">请选择</option>
                      <option
                        v-for="option in param.options || []"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label || option.value }}
                      </option>
                    </select>
                  </div>
                  <input
                    v-else-if="param.type !== 'boolean'"
                    v-model="paramValues[param.name]"
                    :type="(param.type === 'number' && param.formType !== 'number_range') ? 'number' : 'text'"
                    class="uniform-height-input ds-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    :class="{ 'border-red-300': validationErrors[param.name] }"
                    :placeholder="param.description || `请输入值`"
                    @input="onParamValueChange(param.name, $event)"
                  />
                  <div v-else class="flex items-center uniform-height-input-checkbox">
                    <input
                      type="checkbox"
                      v-model="paramValues[param.name]"
                      class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      :id="'param-value-' + param.name"
                      @change="onParamValueChange(param.name, $event)"
                    />
                    <label :for="'param-value-' + param.name" class="ml-2 text-sm text-gray-700">
                      {{ param.description || param.name }}
                    </label>
                  </div>
                  <!-- 验证错误提示 -->
                  <p v-if="validationErrors[param.name]" class="mt-1 text-sm text-red-600">
                    {{ validationErrors[param.name] }}
                  </p>
                </td>

                <!-- 必填 -->
                <td class="px-2 py-2 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    v-model="param.required"
                    class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    @change="onRequiredChange(param.name, $event)"
                  />
                </td>

                <!-- 必填组 -->
                <td class="px-4 py-2 whitespace-nowrap">
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="group in getParamRequiredGroups(param.name)"
                      :key="group.id"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      :class="getGroupColorClass(group.id)"
                      :title="`必填组 ${group.index + 1}: ${group.fields.map(f => f.label || f.name).join(', ')}`"
                    >
                      组{{ group.index + 1 }}
                    </span>
                    <span
                      v-if="getParamRequiredGroups(param.name).length === 0"
                      class="text-xs text-gray-400"
                    >
                      未分组
                    </span>
                  </div>
                </td>

                <!-- 操作 -->
                <td class="px-2 py-2 whitespace-nowrap text-center text-sm font-medium">
                  <div class="flex items-center justify-center space-x-2">
                    <button
                      @click="openAdvancedConfig(param, index)"
                      class="text-indigo-600 hover:text-indigo-900"
                      title="高级配置"
                    >
                      配置
                    </button>
                    <button
                      @click="removeQueryParam(index)"
                      class="text-red-600 hover:text-red-900"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </draggable>
          <tfoot>
            <tr>
              <td colspan="10" class="px-4 py-3">
                <button
                  v-if="shouldShowAddButton"
                  @click="handleAddParamClick"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
                >
                  <i class="fas fa-plus mr-1"></i> 添加参数
                </button>
                <span v-else-if="queryParams.length === 0" class="text-sm text-gray-500">
                  暂无参数，请初始化全部列
                </span>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>



    <!-- 必填组管理弹窗 -->
    <div
      v-if="showRequiredGroupsDrawer"
      class="fixed inset-0 z-[99999] overflow-hidden flex items-center justify-center"
      data-testid="required-groups-drawer"
      @click="closeRequiredGroupsDrawer"
      style="z-index: 99999 !important;"
    >
      <div class="absolute inset-0 bg-black bg-opacity-50" style="z-index: 99998 !important;"></div>
      <div class="relative bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden" @click.stop style="z-index: 99999 !important;">
        <!-- 弹窗头部 -->
        <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mr-3">
              <i class="fas fa-layer-group text-blue-600 text-sm"></i>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">必填组管理</h3>
              <p class="text-sm text-gray-600">配置查询条件的必填字段组合</p>
            </div>
          </div>
          <button
            @click="closeRequiredGroupsDrawer"
            class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-2 rounded-lg hover:bg-white/50"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 弹窗内容 -->
        <div class="overflow-y-auto p-6" style="max-height: calc(90vh - 140px);">
          <!-- 空状态 -->
          <div v-if="fieldGroups.length === 0" class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-layer-group text-gray-400 text-2xl"></i>
            </div>
            <h4 class="text-lg font-medium text-gray-900 mb-2">暂无必填组</h4>
            <p class="text-gray-500 mb-6 text-sm">
              创建必填组来定义用户必须填写的字段组合
            </p>
            <button
              @click="addFieldGroup"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <i class="fas fa-plus mr-2"></i>
              创建第一个组
            </button>
          </div>

          <!-- 必填组列表 -->
          <div v-else class="space-y-4">
            <div
              v-for="(group, groupIndex) in fieldGroups"
              :key="group.id"
              class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors duration-200"
            >
              <!-- 组头部 -->
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold mr-3"
                    :class="getGroupColorClass(group.id)"
                  >
                    {{ groupIndex + 1 }}
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">组 {{ groupIndex + 1 }}</h4>
                    <p class="text-sm text-gray-500">
                      {{ group.fields.length }} 个字段
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    @click="addFieldToGroup(groupIndex)"
                    class="text-blue-600 hover:text-blue-800 text-sm"
                    title="添加字段"
                  >
                    <i class="fas fa-plus"></i>
                  </button>
                  <button
                    v-if="fieldGroups.length > 1"
                    @click="removeFieldGroup(groupIndex)"
                    class="text-red-600 hover:text-red-800 text-sm"
                    title="删除组"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>

              <!-- 字段列表 -->
              <div v-if="group.fields.length === 0" class="text-center py-4 text-gray-500 text-sm">
                暂无字段，点击上方 + 号添加字段
              </div>
              <div v-else class="space-y-2">
                <div
                  v-for="(field, fieldIndex) in group.fields"
                  :key="field.id"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-200"
                >
                  <div class="flex-1">
                    <!-- 字段选择器 -->
                    <div v-if="!field.name" class="relative">
                      <select
                        @change="onGroupFieldSelect(groupIndex, fieldIndex, $event)"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                      >
                        <option value="">请选择字段</option>
                        <option
                          v-for="availableField in getAvailableFieldsForGroup(groupIndex, fieldIndex)"
                          :key="availableField.name"
                          :value="availableField.name"
                        >
                          {{ availableField.label || availableField.name }} ({{ availableField.type }})
                        </option>
                      </select>
                    </div>
                    <!-- 已选择的字段显示 -->
                    <div v-else>
                      <div class="font-medium text-sm text-gray-900">{{ field.label || field.name }}</div>
                      <div class="text-xs text-gray-500">{{ field.name }} • {{ field.type }}</div>
                    </div>
                  </div>
                  <button
                    @click="removeFieldFromGroup(groupIndex, fieldIndex)"
                    class="text-red-600 hover:text-red-800 text-sm ml-2 p-1 rounded hover:bg-red-50 transition-colors duration-200"
                    title="删除字段"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 添加新组按钮 -->
            <button
              @click="addFieldGroup"
              class="w-full py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-800 transition-colors duration-200"
            >
              <i class="fas fa-plus mr-2"></i>
              添加新组
            </button>
          </div>
        </div>

        <!-- 弹窗底部 -->
        <div class="border-t border-gray-200 px-6 py-4">
          <div class="flex justify-end space-x-3">
            <button
              @click="closeRequiredGroupsDrawer"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用新的参数配置组件 -->
    <ParamAdvancedConfig
      v-model="currentParam"
      :is-visible="showAdvancedConfigModal"
      :project-code="enumServiceConfig.projectCode"
      @close="closeAdvancedConfigModal"
      @save="saveAdvancedConfig"
    />

    <!-- 添加参数对话框 - 已废弃，现在直接在表格中添加 -->
    <div v-if="false" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-96 mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">添加参数</h3>
        </div>

        <div class="p-6">
          <div class="mb-4">
            <div class="flex items-center justify-between mb-1">
              <label class="block text-sm font-medium text-gray-700">选择字段</label>
              <button
                v-if="availableFields.length === 0"
                @click="loadFieldsInModal"
                :disabled="isLoadingFields"
                class="inline-flex items-center px-2 py-1 text-xs font-medium rounded text-blue-600 hover:text-blue-800 hover:bg-blue-50 focus:outline-none"
              >
                <i class="fas fa-sync-alt mr-1" :class="{ 'fa-spin': isLoadingFields }"></i>
                {{ isLoadingFields ? '加载中...' : '同步字段' }}
              </button>
            </div>
            <select
              v-model="selectedField"
              class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="">请选择字段...</option>
              <option
                v-for="field in nonAddedFields"
                :key="field.name"
                :value="field.name"
              >
                {{ field.label || field.name }}
              </option>
            </select>

            <!-- 如果没有可用字段，显示提示信息 -->
            <div v-if="nonAddedFields.length === 0" class="mt-2 text-sm text-gray-500">
              <div v-if="availableFields.length === 0" class="flex items-center">
                <i class="fas fa-info-circle mr-1"></i>
                暂无可用字段，请先点击"同步字段"按钮加载
              </div>
              <div v-else class="flex items-center">
                <i class="fas fa-check-circle mr-1"></i>
                所有可用字段都已添加为参数
              </div>
            </div>
          </div>

          <!-- 表名显示 -->
          <div class="mb-4" v-if="selectedField && selectedFieldInfo">
            <label class="block text-sm font-medium text-gray-700 mb-1">所属表</label>
            <div class="block w-full border border-gray-300 rounded-md py-2 px-3 bg-gray-50 text-gray-500 sm:text-sm">
              {{ selectedFieldInfo.tableName || '未知表' }}
            </div>
          </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="cancelAddParam"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            取消
          </button>
          <button
            @click="confirmAddParam"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none"
            :class="!selectedField || nonAddedFields.length === 0 ? 'bg-gray-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'"
            :disabled="!selectedField || nonAddedFields.length === 0"
          >
            确认
          </button>
        </div>
      </div>
    </div>

    <!-- 枚举选择器 -->
    <EnumSelector
      v-if="showEnumSelectorModal"
      :project-code="enumServiceConfig.projectCode"
      :is-debug="false"
      :current-enum-code="currentParam?.enumCode || ''"
      @select="handleEnumSelect"
      @close="showEnumSelectorModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import draggable from 'vuedraggable';
import ParamAdvancedConfig from './ParamAdvancedConfig.vue';
import EnumSelector from './enum/EnumSelector.vue';
import { message } from '@/services/message';
import type { QueryParam } from '@/types/unified-integration';
import { enumServiceConfig } from '@/utils/config';
import { getApiBaseUrl } from "@/services/query";
import instance from '@/utils/axios';
import { globalApiRequestManager } from '@/utils/requestManager';

// 定义组件接口
interface Props {
  modelValue: QueryParam[]; // 查询参数数组
  paramValues: Record<string, any>; // 参数值对象
  validationErrors: Record<string, string>; // 验证错误信息
  queryId?: string; // 查询ID
  dataSourceId?: string; // 数据源ID
  integrationType: string; // 集成类型
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'update:paramValues', 'loadParamsFromQuery', 'validate']);

// 存储查询参数的数组
const queryParams = ref<QueryParam[]>([]);

// 添加一个标记来跟踪是否正在进行表单类型切换
const isFormTypeChanging = ref(false);

// 监听相关代码
watch(
  () => props.modelValue,
  (val: QueryParam[]) => {
    if (val && JSON.stringify(val) !== JSON.stringify(queryParams.value)) {
      console.log('[QueryConditionsPanel] modelValue changed:', val);

      // 打印详细的参数信息，特别是数字类型的参数
      if (val && val.length > 0) {
        console.log('[QueryConditionsPanel] 接收到的参数详情:');
        val.forEach((param, index) => {
          console.log(`[QueryConditionsPanel] 参数${index+1}: name=${param.name}, type=${param.type}, format=${param.format}, defaultValue=${param.defaultValue}`);
          if (param.type === 'number') {
            console.log(`[QueryConditionsPanel] 数字参数详情: format=${param.format}, config=${JSON.stringify(param.config)}`);
          }
        });
      }

      // 深拷贝参数
      queryParams.value = JSON.parse(JSON.stringify(val));

      // 处理参数初始化
      queryParams.value.forEach(param => {
        if (param.type === 'number') {
          // 确保format值为'int'或'decimal'
          if (param.format !== 'int' && param.format !== 'decimal') {
            if (param.format && (param.format.includes('int') || param.format === 'integer' || param.format === 'number')) {
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 修正整数参数format: ${param.name}, format=${param.format}`);
            } else if (param.format && (param.format.includes('decimal') || param.format.includes('float') || param.format.includes('double'))) {
              param.format = 'decimal';
              console.log(`[QueryConditionsPanel] 修正小数参数format: ${param.name}, format=${param.format}`);
            } else {
              // 如果没有明确的类型信息，默认设置为int
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 未识别的数字类型参数format: ${param.name}, 默认设置为int`);
            }

            // 确保config对象存在
            if (!param.config) {
              param.config = {};
            }

            // 设置数字类型默认配置
            param.config.fixedPoint = param.format === 'decimal' ? "2" : "0";
            param.config.thousandSeparator = true;

            // 同步到顶级属性，以便在UI中显示
            param.fixedPoint = param.config.fixedPoint;
            param.thousandSeparator = param.config.thousandSeparator;

            console.log(`[QueryConditionsPanel] 初始化时设置数字格式配置: name=${param.name}, format=${param.format}, fixedPoint=${param.config.fixedPoint}, thousandSeparator=${param.config.thousandSeparator}`);
          }
        }

        // 处理日期区间类型的默认值
        if (param.formType === 'date-range' && param.defaultValue) {
          console.log(`[QueryConditionsPanel] 处理日期区间默认值: ${param.name} = ${param.defaultValue}`);

          // 解析默认值，支持逗号分隔或短横线分隔
          let startValue = '';
          let endValue = '';

          if (typeof param.defaultValue === 'string') {
            // 尝试用逗号分隔
            if (param.defaultValue.includes(',')) {
              const parts = param.defaultValue.split(',').map(s => s.trim());
              startValue = parts[0] || '';
              endValue = parts[1] || '';
              console.log(`[QueryConditionsPanel] 逗号分隔解析: start=${startValue}, end=${endValue}`);
            }
            // 尝试用短横线分隔
            else if (param.defaultValue.includes(' - ')) {
              const parts = param.defaultValue.split(' - ').map(s => s.trim());
              startValue = parts[0] || '';
              endValue = parts[1] || '';
              console.log(`[QueryConditionsPanel] 短横线分隔解析: start=${startValue}, end=${endValue}`);
            }
            // 如果只有一个值，作为开始日期
            else if (param.defaultValue.trim()) {
              startValue = param.defaultValue.trim();
              console.log(`[QueryConditionsPanel] 单个值作为开始日期: start=${startValue}`);
            }
          }

          // 初始化paramValues中的日期区间值
          if (props.paramValues) {
            const newParamValues = { ...props.paramValues };
            newParamValues[param.name + '_start'] = startValue;
            newParamValues[param.name + '_end'] = endValue;

            console.log(`[QueryConditionsPanel] 初始化日期区间值: ${param.name}_start=${startValue}, ${param.name}_end=${endValue}`);

            // 发出更新事件
            emit('update:paramValues', newParamValues);
          }
        }
        // 确保其他类型的defaultValue与paramValues保持同步
        else if (props.paramValues && param.name in props.paramValues) {
          const currentValue = props.paramValues[param.name];
          if (param.defaultValue !== currentValue) {
            console.log(`[QueryConditionsPanel] 初始化时同步参数 "${param.name}" 的默认值: ${param.defaultValue} -> ${currentValue}`);
            param.defaultValue = currentValue;
          }
        }
        // 如果paramValues中没有该参数的值，但参数有默认值，则初始化paramValues
        else if (param.defaultValue !== undefined && param.defaultValue !== '' && param.formType !== 'date-range') {
          if (props.paramValues) {
            const newParamValues = { ...props.paramValues };
            newParamValues[param.name] = param.defaultValue;
            console.log(`[QueryConditionsPanel] 从默认值初始化参数值: ${param.name} = ${param.defaultValue}`);
            emit('update:paramValues', newParamValues);
          }
        }
      });
    }
  },
  { immediate: true }
);

// 监听外部传入的paramValues变化
watch(
  () => props.paramValues,
  (newVal: Record<string, any>) => {
    console.log('[QueryConditionsPanel] paramValues changed:', newVal);

    // 同步paramValues的变化到对应参数的defaultValue字段
    if (newVal && queryParams.value) {
      queryParams.value.forEach((param, index) => {
        if (param.formType === 'date-range') {
          // 对于日期区间类型，处理_start和_end值
          const startKey = param.name + '_start';
          const endKey = param.name + '_end';

          if (startKey in newVal && endKey in newVal) {
            const startValue = newVal[startKey] || '';
            const endValue = newVal[endKey] || '';
            // 使用逗号分隔格式
            const fullRangeValue = startValue && endValue ? `${startValue}, ${endValue}` :
                                   startValue ? startValue :
                                   endValue ? `, ${endValue}` : '';

            if (param.defaultValue !== fullRangeValue) {
              console.log(`[QueryConditionsPanel] 同步日期区间参数 "${param.name}" 的默认值: ${param.defaultValue} -> ${fullRangeValue}`);
              param.defaultValue = fullRangeValue;
              // 触发响应式更新
              queryParams.value = [...queryParams.value];
            }
          }
        } else if (param.name in newVal) {
          const newValue = newVal[param.name];
          // 只有当值真正发生变化时才更新
          if (param.defaultValue !== newValue) {
            console.log(`[QueryConditionsPanel] 同步参数 "${param.name}" 的默认值: ${param.defaultValue} -> ${newValue}`);
            param.defaultValue = newValue;
            // 触发响应式更新
            queryParams.value = [...queryParams.value];
          }
        }
      });
    }
  },
  { deep: true } // 深度监听以捕获对象内部属性的变化
);

// 监听queryParams变化以发出更新事件
watch(
  () => queryParams.value,
  (newVal: QueryParam[]) => {
    console.log('[QueryConditionsPanel] queryParams changed:', newVal);

    // 在发出更新事件前，确保所有参数的defaultValue都已同步
    // 但是如果正在进行表单类型切换，则跳过同步，避免覆盖已清空的值
    if (newVal && props.paramValues && !isFormTypeChanging.value) {
      newVal.forEach((param) => {
        if (param.name in props.paramValues) {
          const currentValue = props.paramValues[param.name];
          if (param.defaultValue !== currentValue) {
            console.log(`[QueryConditionsPanel] 保存前同步参数 "${param.name}" 的默认值: ${param.defaultValue} -> ${currentValue}`);
            param.defaultValue = currentValue;
          }
        }
      });
    } else if (isFormTypeChanging.value) {
      console.log('[QueryConditionsPanel] 跳过defaultValue同步，因为正在进行表单类型切换');
    }

    emit('update:modelValue', newVal);
  }
);

// 组件挂载时记录初始数据
onMounted(() => {
  console.log('[QueryConditionsPanel] 组件挂载，初始化参数值');

  // 确保日期区间参数的值正确初始化和回显
  if (queryParams.value && props.paramValues) {
    queryParams.value.forEach(param => {
      if (param.formType === 'date-range') {
        const startKey = param.name + '_start';
        const endKey = param.name + '_end';

        // 如果paramValues中没有_start和_end值，但有defaultValue，则解析defaultValue
        if (!(startKey in props.paramValues) && !(endKey in props.paramValues) && param.defaultValue) {
          console.log(`[QueryConditionsPanel] 挂载时解析日期区间默认值: ${param.name} = ${param.defaultValue}`);

          let startValue = '';
          let endValue = '';

          if (typeof param.defaultValue === 'string') {
            // 尝试用逗号分隔
            if (param.defaultValue.includes(',')) {
              const parts = param.defaultValue.split(',').map(s => s.trim());
              startValue = parts[0] || '';
              endValue = parts[1] || '';
            }
            // 尝试用短横线分隔
            else if (param.defaultValue.includes(' - ')) {
              const parts = param.defaultValue.split(' - ').map(s => s.trim());
              startValue = parts[0] || '';
              endValue = parts[1] || '';
            }
            // 如果只有一个值，作为开始日期
            else if (param.defaultValue.trim()) {
              startValue = param.defaultValue.trim();
            }
          }

          if (startValue || endValue) {
            const newParamValues = { ...props.paramValues };
            newParamValues[startKey] = startValue;
            newParamValues[endKey] = endValue;

            console.log(`[QueryConditionsPanel] 挂载时初始化日期区间值: ${startKey}=${startValue}, ${endKey}=${endValue}`);
            emit('update:paramValues', newParamValues);
          }
        }
      }
      // 对于其他类型的参数，如果paramValues中没有值但有defaultValue，则初始化
      else if (!(param.name in props.paramValues) && param.defaultValue !== undefined && param.defaultValue !== '') {
        const newParamValues = { ...props.paramValues };
        newParamValues[param.name] = param.defaultValue;
        console.log(`[QueryConditionsPanel] 挂载时从默认值初始化参数: ${param.name} = ${param.defaultValue}`);
        emit('update:paramValues', newParamValues);
      }
    });
  }

  // 新增：加载搜索字段数据
  loadSearchFields();
});

// 配置相关状态
const showAdvancedConfigModal = ref(false);
const currentParam = ref<QueryParam | null>(null);
const currentParamIndex = ref<number>(-1);

// 枚举选择器相关状态
const showEnumSelectorModal = ref(false);

// 添加可用字段状态
const availableFields = ref<Array<{name: string, type: string, label: string, tableName?: string, isEncrypted?: boolean}>>([]);
// 选择的字段
const selectedField = ref('');
// 添加参数对话框显示状态
const showAddParamModal = ref(false);
// 字段加载状态
const isLoadingFields = ref(false);

// 新增：多组字段配置相关数据
interface FieldGroup {
  id: string;
  fields: Array<{
    id: string;
    name: string;
    label?: string;
    type?: string;
  }>;
}

const fieldGroups = ref<FieldGroup[]>([]);
const availableSearchFields = ref<Array<{name: string, label: string, type: string}>>([]);

// 新增：字段筛选相关状态
const fieldFilterTexts = ref<Record<string, string>>({});
const fieldDropdownVisible = ref<Record<string, boolean>>({});

// 必填组管理抽屉相关状态
const showRequiredGroupsDrawer = ref(false);

// 颜色类映射
const groupColorClasses = [
  'bg-blue-100 text-blue-800',
  'bg-green-100 text-green-800',
  'bg-yellow-100 text-yellow-800',
  'bg-purple-100 text-purple-800',
  'bg-pink-100 text-pink-800',
  'bg-indigo-100 text-indigo-800',
  'bg-red-100 text-red-800',
  'bg-gray-100 text-gray-800'
];

// 生成字段选择框的唯一键
const getFieldSelectKey = (groupIndex: number, fieldIndex: number) => {
  return `group_${groupIndex}_field_${fieldIndex}`;
};

// 必填组管理相关方法
const openRequiredGroupsDrawer = () => {
  console.log('[QueryConditionsPanel] 🔥 按钮被点击了！');
  console.log('[QueryConditionsPanel] 当前抽屉状态:', showRequiredGroupsDrawer.value);
  console.log('[QueryConditionsPanel] 当前字段组数量:', fieldGroups.value.length);

  showRequiredGroupsDrawer.value = true;

  console.log('[QueryConditionsPanel] 抽屉状态设置为:', showRequiredGroupsDrawer.value);
  console.log('[QueryConditionsPanel] DOM中应该显示抽屉');

  // 强制触发响应式更新
  nextTick(() => {
    console.log('[QueryConditionsPanel] nextTick后抽屉状态:', showRequiredGroupsDrawer.value);
    const drawerElement = document.querySelector('[data-testid="required-groups-drawer"]');
    console.log('[QueryConditionsPanel] 抽屉DOM元素:', drawerElement);
  });
};

const closeRequiredGroupsDrawer = () => {
  showRequiredGroupsDrawer.value = false;
};

// 获取参数所属的必填组
const getParamRequiredGroups = (paramName: string) => {
  return fieldGroups.value
    .map((group, index) => ({
      ...group,
      index,
      fields: group.fields
    }))
    .filter(group =>
      group.fields.some(field => field.name === paramName)
    );
};

// 获取组的颜色类
const getGroupColorClass = (groupId: string) => {
  const groupIndex = fieldGroups.value.findIndex(group => group.id === groupId);
  return groupColorClasses[groupIndex % groupColorClasses.length];
};

// 处理组中字段选择
const onGroupFieldSelect = (groupIndex: number, fieldIndex: number, event: Event) => {
  const target = event.target as HTMLSelectElement;
  const fieldName = target.value;

  if (!fieldName) {
    return;
  }

  // 从可用字段中找到选中的字段
  const selectedField = availableSearchFields.value.find(field => field.name === fieldName);
  if (!selectedField) {
    return;
  }

  // 更新字段信息
  fieldGroups.value[groupIndex].fields[fieldIndex] = {
    id: fieldGroups.value[groupIndex].fields[fieldIndex].id,
    name: selectedField.name,
    label: selectedField.label || selectedField.name,
    type: selectedField.type
  };

  console.log('[QueryConditionsPanel] 组字段选择:', {
    groupIndex,
    fieldIndex,
    selectedField: selectedField.name
  });

  // 检查该字段是否已存在于查询参数中，如果不存在则自动添加
  const existingParam = queryParams.value.find(param => param.name === selectedField.name);
  if (!existingParam) {
    console.log('[QueryConditionsPanel] 字段不存在于查询参数中，自动添加:', selectedField.name);

    // 创建新的查询参数
    const newParam: QueryParam = {
      name: selectedField.name,
      type: selectedField.type === 'number' ? 'number' : 'string',
      description: selectedField.label || selectedField.name,
      format: selectedField.type === 'number' ? 'int' : 'string',
      formType: 'text',
      required: false,
      isNewParam: true,
      options: [],
      displayOrder: queryParams.value.length,
      tableName: '',
      isEncrypted: false,
      defaultValue: ''
    };

    // 添加到参数列表中
    const newParams = [...queryParams.value, newParam];
    queryParams.value = newParams;

    // 发出更新事件
    emit('update:modelValue', newParams);

    message.success(`已自动添加字段 "${selectedField.label || selectedField.name}" 到查询条件配置`);
  }
};

// 新增：多组字段配置相关方法
const generateId = () => `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 添加字段组
const addFieldGroup = () => {
  const newGroup: FieldGroup = {
    id: generateId(),
    fields: []
  };
  fieldGroups.value.push(newGroup);
  console.log('[QueryConditionsPanel] 添加字段组:', newGroup);
};

// 删除字段组
const removeFieldGroup = (groupIndex: number) => {
  if (fieldGroups.value.length > 1) {
    fieldGroups.value.splice(groupIndex, 1);
    console.log('[QueryConditionsPanel] 删除字段组:', groupIndex);
  }
};

// 向指定组添加字段
const addFieldToGroup = (groupIndex: number) => {
  const newField = {
    id: generateId(),
    name: '',
    label: '',
    type: ''
  };
  fieldGroups.value[groupIndex].fields.push(newField);
  console.log('[QueryConditionsPanel] 向组添加字段:', groupIndex, newField);
};

// 从指定组删除字段
const removeFieldFromGroup = (groupIndex: number, fieldIndex: number) => {
  fieldGroups.value[groupIndex].fields.splice(fieldIndex, 1);
  console.log('[QueryConditionsPanel] 从组删除字段:', groupIndex, fieldIndex);
};

// 加载searchFields数据
const loadSearchFields = async () => {
  if (!props.queryId) {
    console.warn('[QueryConditionsPanel] 没有queryId，无法加载搜索字段');
    return;
  }

  try {
    console.log('[QueryConditionsPanel] 开始加载搜索字段:', props.queryId);
    const url = `/api/queries/${props.queryId}/parameters`;
    const response = await globalApiRequestManager.request(
      () => instance.get(url),
      url,
      { queryId: props.queryId },
      { cacheTime: 30000 }
    );
    const data = response.data;

    console.log('[QueryConditionsPanel] 获取到的搜索字段数据:', data);

    // 从响应中提取searchFields
    let searchFields: any[] = [];

    if (data.searchFields && Array.isArray(data.searchFields)) {
      searchFields = data.searchFields;
    } else if (data.data && data.data.searchFields && Array.isArray(data.data.searchFields)) {
      searchFields = data.data.searchFields;
    } else if (data.fields && Array.isArray(data.fields)) {
      searchFields = data.fields;
    } else if (data.data && data.data.fields && Array.isArray(data.data.fields)) {
      searchFields = data.data.fields;
    }

    // 处理搜索字段数据
    availableSearchFields.value = searchFields.map((field: any) => {
      if (typeof field === 'string') {
        return {
          name: field,
          label: field,
          type: 'string'
        };
      }
      return {
        name: field.name || field.fieldName || 'unnamed_field',
        label: field.label || field.name || field.fieldName || 'Unnamed Field',
        type: field.type || field.dataType || 'string'
      };
    });

    console.log('[QueryConditionsPanel] 处理后的搜索字段:', availableSearchFields.value);

    // 如果还没有字段组，初始化一个
    if (fieldGroups.value.length === 0) {
      addFieldGroup();
    }

  } catch (error) {
    console.error('[QueryConditionsPanel] 加载搜索字段失败:', error);
  }
};

// 字段搜索相关状态
const activeDropdownIndex = ref<number | null>(null);
const fieldSearchText = ref('');
const searchInputRefs = ref<Map<number, HTMLInputElement>>(new Map());

// 计算未添加的字段列表
const nonAddedFields = computed(() => {
  // 获取已添加字段名列表
  const addedFieldNames = new Set(queryParams.value.map(param => param.name));

  // 记录计算过程
  console.log('[QueryConditionsPanel] 计算未添加字段:', {
    '已添加字段数量': addedFieldNames.size,
    '已添加字段': Array.from(addedFieldNames).join(', '),
    '可用字段数量': availableFields.value.length,
    '可用字段': availableFields.value.map(f => f.name).join(', ')
  });

  // 过滤出未添加的字段
  const nonAdded = availableFields.value.filter(field => !addedFieldNames.has(field.name));

  console.log('[QueryConditionsPanel] 计算结果 - 未添加字段:', {
    '数量': nonAdded.length,
    '字段': nonAdded.map(f => f.name).join(', ')
  });

  return nonAdded;
});

// 计算可用于选择的字段列表（排除已选择的字段）
const availableFieldsForSelection = computed(() => {
  // 获取已添加字段名列表（排除空字段名）
  const addedFieldNames = new Set(
    queryParams.value
      .map(param => param.name)
      .filter(name => name && name.trim() !== '')
  );

  // 过滤出未添加的字段
  const availableForSelection = availableFields.value.filter(field =>
    !addedFieldNames.has(field.name)
  );

  console.log('[QueryConditionsPanel] 可用于选择的字段:', {
    '总字段数量': availableFields.value.length,
    '已添加字段': Array.from(addedFieldNames).join(', '),
    '可选择字段数量': availableForSelection.length,
    '可选择字段': availableForSelection.map(f => f.name).join(', ')
  });

  return availableForSelection;
});

// 计算当前下拉框可用的字段列表（包含当前正在编辑的字段）
const currentDropdownFields = computed(() => {
  if (activeDropdownIndex.value === null) {
    return availableFieldsForSelection.value;
  }

  // 获取当前正在编辑的参数
  const currentParam = queryParams.value[activeDropdownIndex.value];
  if (!currentParam) {
    return availableFieldsForSelection.value;
  }

  // 获取已添加字段名列表（排除当前正在编辑的字段）
  const addedFieldNames = new Set(
    queryParams.value
      .map((param, index) => index !== activeDropdownIndex.value ? param.name : null)
      .filter(name => name && name.trim() !== '')
  );

  // 过滤出可选择的字段（包含当前字段）
  const fieldsForCurrentDropdown = availableFields.value.filter(field =>
    !addedFieldNames.has(field.name)
  );

  return fieldsForCurrentDropdown;
});

// 计算过滤后的字段列表（用于搜索）
const filteredFieldsForSelection = computed(() => {
  if (!fieldSearchText.value) {
    return currentDropdownFields.value;
  }

  const query = fieldSearchText.value.toLowerCase();
  return currentDropdownFields.value.filter(field =>
    field.name.toLowerCase().includes(query) ||
    (field.label && field.label.toLowerCase().includes(query)) ||
    (field.tableName && field.tableName.toLowerCase().includes(query))
  );
});

// 计算当前选中字段的信息
const selectedFieldInfo = computed(() => {
  if (!selectedField.value) return null;
  return availableFields.value.find(field => field.name === selectedField.value) || null;
});

// 检查是否应该显示添加参数按钮
const shouldShowAddButton = computed(() => {
  // 只有当有可用字段时才显示"添加参数"按钮
  return availableFieldsForSelection.value.length > 0;
});

// 处理参数值变化，同步到defaultValue字段
const onParamValueChange = (paramName: string, event: Event) => {
  const target = event.target as HTMLInputElement | HTMLSelectElement;
  let newValue: any;

  // 根据输入类型处理值
  if (target.type === 'checkbox') {
    newValue = (target as HTMLInputElement).checked;
  } else if (target.type === 'number') {
    newValue = target.value === '' ? null : Number(target.value);
  } else {
    newValue = target.value;
  }

  console.log(`[QueryConditionsPanel] 参数值变化: ${paramName} = ${newValue} (类型: ${typeof newValue})`);

  // 找到对应的参数并更新其defaultValue
  const paramIndex = queryParams.value.findIndex(param => param.name === paramName);
  if (paramIndex >= 0) {
    // 只有当值真正发生变化时才更新
    if (queryParams.value[paramIndex].defaultValue !== newValue) {
      console.log(`[QueryConditionsPanel] 同步参数 "${paramName}" 的默认值: ${queryParams.value[paramIndex].defaultValue} -> ${newValue}`);
      queryParams.value[paramIndex].defaultValue = newValue;

      // 触发响应式更新
      emit('update:modelValue', [...queryParams.value]);
    }
  }
};

// 处理日期区间值变化
const onDateRangeChange = (paramName: string, rangeType: 'start' | 'end', event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.value;

  console.log(`[QueryConditionsPanel] 日期区间值变化: ${paramName}_${rangeType} = ${newValue}`);

  // 获取当前区间值（需要从最新的props.paramValues中获取）
  let startValue = '';
  let endValue = '';

  if (rangeType === 'start') {
    startValue = newValue;
    endValue = props.paramValues[paramName + '_end'] || '';
  } else {
    startValue = props.paramValues[paramName + '_start'] || '';
    endValue = newValue;
  }

  // 构建完整的日期区间值，使用逗号分隔格式
  const fullRangeValue = startValue && endValue ? `${startValue}, ${endValue}` :
                         startValue ? startValue :
                         endValue ? `, ${endValue}` : '';

  console.log(`[QueryConditionsPanel] 构建的日期区间值: ${fullRangeValue} (start: ${startValue}, end: ${endValue})`);

  // 更新参数的默认值
  const paramIndex = queryParams.value.findIndex(param => param.name === paramName);
  if (paramIndex >= 0) {
    console.log(`[QueryConditionsPanel] 同步日期区间参数 "${paramName}" 的默认值: ${queryParams.value[paramIndex].defaultValue} -> ${fullRangeValue}`);
    queryParams.value[paramIndex].defaultValue = fullRangeValue;

    // 触发响应式更新
    emit('update:modelValue', [...queryParams.value]);
  }
};

// 处理必填字段变化
const onRequiredChange = (paramName: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.checked;

  console.log(`[QueryConditionsPanel] 必填字段变化: ${paramName} = ${newValue}`);

  // 找到对应的参数并更新其required字段
  const paramIndex = queryParams.value.findIndex(param => param.name === paramName);
  if (paramIndex >= 0) {
    queryParams.value[paramIndex].required = newValue;

    // 触发响应式更新
    emit('update:modelValue', [...queryParams.value]);

    console.log(`[QueryConditionsPanel] 已更新参数 "${paramName}" 的必填状态: ${newValue}`);
  }
};

// 处理表单类型变化
const onFormTypeChange = async (param: QueryParam, index: number, event: Event) => {
  const target = event.target as HTMLSelectElement;
  const newFormType = target.value;
  const oldFormType = param.formType;

  console.log(`[QueryConditionsPanel] 表单类型变化: ${param.name} 从 ${oldFormType} 到 ${newFormType}`);

  // 设置标记，表示正在进行表单类型切换
  isFormTypeChanging.value = true;

  try {
    // 更新表单类型
    queryParams.value[index].formType = newFormType;

    // 创建新的paramValues对象以确保响应式更新
    const newParamValues = { ...props.paramValues };

    // 清空相关值
    if (oldFormType === 'date-range' && newFormType !== 'date-range') {
      // 从日期区间切换到其他类型，清空区间相关值
      console.log(`[QueryConditionsPanel] 清空日期区间相关值: ${param.name}_start, ${param.name}_end`);
      delete newParamValues[`${param.name}_start`];
      delete newParamValues[`${param.name}_end`];
      // 清空主参数值
      delete newParamValues[param.name];
      // 清空默认值
      param.defaultValue = '';
      console.log(`[QueryConditionsPanel] 清空默认值: ${param.name}`);
    } else if (oldFormType !== 'date-range' && newFormType === 'date-range') {
      // 从其他类型切换到日期区间，清空主参数值并初始化区间值
      console.log(`[QueryConditionsPanel] 从 ${oldFormType} 切换到日期区间，清空主参数值`);
      delete newParamValues[param.name];
      // 清空默认值
      param.defaultValue = '';
      console.log(`[QueryConditionsPanel] 清空默认值: ${param.name}`);
      // 初始化日期区间值
      newParamValues[`${param.name}_start`] = '';
      newParamValues[`${param.name}_end`] = '';
    } else {
      // 其他类型之间的切换，清空相关值
      console.log(`[QueryConditionsPanel] 清空参数值: ${param.name}`);
      delete newParamValues[param.name];
      // 清空默认值
      param.defaultValue = '';
      console.log(`[QueryConditionsPanel] 清空默认值: ${param.name}`);
    }

    console.log(`[QueryConditionsPanel] 发出paramValues更新:`, newParamValues);
    emit('update:paramValues', newParamValues);

    console.log(`[QueryConditionsPanel] 发出queryParams更新:`, queryParams.value);
    emit('update:modelValue', queryParams.value);

    // 等待DOM更新完成
    await nextTick();

  } finally {
    // 重置标记
    isFormTypeChanging.value = false;
    console.log('[QueryConditionsPanel] 表单类型切换完成，重置标记');
  }
};

// 打开配置
const openAdvancedConfig = (param: QueryParam, index: number) => {
  currentParam.value = JSON.parse(JSON.stringify(param)); // 深拷贝以便取消操作
  currentParamIndex.value = index;
  showAdvancedConfigModal.value = true;
};

// 关闭配置
const closeAdvancedConfigModal = () => {
  showAdvancedConfigModal.value = false;
  currentParam.value = null;
  currentParamIndex.value = -1;
};

// 保存配置
const saveAdvancedConfig = (param: QueryParam) => {
  console.log('[QueryConditionsPanel] 接收到配置保存事件:', JSON.stringify(param));

  if (param && currentParamIndex.value >= 0) {
    // 确保保留原始的displayOrder值
    const originalDisplayOrder = queryParams.value[currentParamIndex.value].displayOrder;

    // 更新参数对象
    queryParams.value[currentParamIndex.value] = {
      ...param,
      displayOrder: originalDisplayOrder // 保留原始的顺序值
    };

    console.log('[QueryConditionsPanel] 更新后的查询参数:', JSON.stringify(queryParams.value[currentParamIndex.value]));

    // 获取当前UI表单中的值，并设置为参数的默认值
    if (props.paramValues && param.name in props.paramValues && props.paramValues[param.name] !== undefined && props.paramValues[param.name] !== null) {
      const currentValue = props.paramValues[param.name];
      console.log(`[QueryConditionsPanel] 设置参数 "${param.name}" 的默认值为当前值:`, currentValue);
      // 更新查询参数的默认值
      queryParams.value[currentParamIndex.value].defaultValue = currentValue;
    }

    // 确保更新传递到父组件
    emit('update:modelValue', queryParams.value);

    // 更新对应参数的初始值（如果设置了默认值）- 修复：不直接修改props
    if (param.defaultValue !== undefined && props.paramValues) {
      const newParamValues = { ...props.paramValues };
      newParamValues[param.name] = param.defaultValue;
      emit('update:paramValues', newParamValues);
    }

    // 显示成功消息
    message.success(`参数 "${param.name}" 的配置已保存`);
  } else {
    console.error('[QueryConditionsPanel] 无法保存配置: 参数索引无效或参数对象为空');
  }

  closeAdvancedConfigModal();
};

// 添加一个强制刷新参数列表的方法
const refreshParamsList = () => {
  console.log('[QueryConditionsPanel] 开始刷新参数列表，当前参数:', queryParams.value);

  // 使用 nextTick 确保在 DOM 更新后执行
  nextTick(async () => {
    try {
      // 保存当前参数的深拷贝
      const currentParams = JSON.parse(JSON.stringify(queryParams.value));

      // 确保参数有正确的顺序
      currentParams.forEach((param: QueryParam, index: number) => {
        param.displayOrder = index;

        // 确保数字类型参数的format值正确设置
        if (param.type === 'number') {
          // 确保format值为'int'或'decimal'
          if (param.format !== 'int' && param.format !== 'decimal') {
            if (param.format && (param.format.includes('int') || param.format === 'integer' || param.format === 'number')) {
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 刷新时修正整数参数format: ${param.name}, format=${param.format}`);
            } else if (param.format && (param.format.includes('decimal') || param.format.includes('float') || param.format.includes('double'))) {
              param.format = 'decimal';
              console.log(`[QueryConditionsPanel] 刷新时修正小数参数format: ${param.name}, format=${param.format}`);
            } else {
              // 如果没有明确的类型信息，默认设置为int
              param.format = 'int';
              console.log(`[QueryConditionsPanel] 刷新时未识别的数字类型参数format: ${param.name}, 默认设置为int`);
            }
          }

          // 确保config对象存在
          if (!param.config) {
            param.config = {};
          }

          // 设置数字类型默认配置
          if (param.format === 'int') {
            param.config.fixedPoint = "0";
          } else if (param.format === 'decimal') {
            param.config.fixedPoint = "2";
          }
          param.config.thousandSeparator = true;

          // 同步到顶级属性，以便在UI中显示
          param.fixedPoint = param.config.fixedPoint;
          param.thousandSeparator = param.config.thousandSeparator;

          console.log(`[QueryConditionsPanel] 刷新时设置数字格式配置: name=${param.name}, format=${param.format}, fixedPoint=${param.config.fixedPoint}, thousandSeparator=${param.config.thousandSeparator}`);
        }
      });

      // 直接更新参数，避免中间状态
      queryParams.value = currentParams;

      // 触发验证
      if (currentParams.length > 0) {
        emit('validate');
      }

      // 通知父组件参数已更新
      emit('update:modelValue', queryParams.value);

      console.log('[QueryConditionsPanel] 参数列表刷新完成:', queryParams.value);

      // 打印刷新后的参数列表
      console.log('[QueryConditionsPanel] 刷新后的参数列表:');
      queryParams.value.forEach((param, index) => {
        console.log(`[QueryConditionsPanel] 刷新后参数${index+1}: name=${param.name}, type=${param.type}, format=${param.format}`);
        if (param.type === 'number') {
          console.log(`[QueryConditionsPanel] 刷新后数字参数详情: format=${param.format}, config=${JSON.stringify(param.config)}`);
        }
      });
    } catch (error) {
      console.error('[QueryConditionsPanel] 刷新参数列表时出错:', error);
      message.error('刷新参数列表失败');
    }
  });
};

// 从数据源加载参数
const loadParamsFromQuery = async () => {
  console.log('[QueryConditionsPanel] 触发loadParamsFromQuery事件');
  emit('loadParamsFromQuery');
  // 所有实际的参数加载逻辑由父组件通过DataSourceParamManager处理
};

// 处理添加参数按钮点击事件 - 直接在表格中添加一行
const handleAddParamClick = () => {
  console.log('[QueryConditionsPanel] 点击添加参数按钮 - 直接添加新行');

  // 创建新的参数对象
  const newParam: QueryParam = {
    name: '',
    type: 'string',
    description: '',
    format: 'string',
    formType: 'text',
    required: false,
    isNewParam: true,
    options: [],
    displayOrder: queryParams.value.length,
    tableName: '',
    isEncrypted: false,
    defaultValue: ''
  };

  // 直接添加到参数列表中
  const newParams = [...queryParams.value, newParam];
  queryParams.value = newParams;

  // 发出更新事件
  emit('update:modelValue', newParams);

  message.success('已添加新参数，请选择字段');
};

// 处理字段选择事件
const onFieldSelect = (param: QueryParam, index: number, event: Event) => {
  const target = event.target as HTMLSelectElement;
  const fieldName = target.value;

  console.log('[QueryConditionsPanel] 选择字段:', fieldName);

  if (!fieldName) {
    return;
  }

  // 查找字段信息
  const fieldInfo = availableFields.value.find(f => f.name === fieldName);
  if (!fieldInfo) {
    message.error('未找到字段信息');
    return;
  }

  // 检查是否已存在同名参数
  const existingParam = queryParams.value.find((p, i) => i !== index && p.name === fieldName);
  if (existingParam) {
    message.error(`字段 "${fieldName}" 已存在，请选择其他字段`);
    target.value = param.name || '';
    return;
  }

  // 更新参数信息
  const updatedParam = {
    ...param,
    name: fieldInfo.name,
    type: fieldInfo.type || 'string',
    description: fieldInfo.label || fieldInfo.name,
    tableName: fieldInfo.tableName || '',
    isEncrypted: fieldInfo.isEncrypted || false,
    isNewParam: false // 标记为非新参数
  };

  // 更新参数列表
  const newParams = [...queryParams.value];
  newParams[index] = updatedParam;
  queryParams.value = newParams;

  // 发出更新事件
  emit('update:modelValue', newParams);

  message.success(`已选择字段 "${fieldInfo.label || fieldInfo.name}"`);
};

// 切换字段下拉框
const toggleFieldDropdown = (index: number, evt?: Event) => {
  if (activeDropdownIndex.value === index) {
    closeFieldDropdown();
  } else {
    // 先移除所有其他单元格的高亮类
    removeAllHighlightClasses();

    activeDropdownIndex.value = index;
    fieldSearchText.value = '';

    // 添加当前单元格的高亮类，使用事件对象精确定位
    addHighlightClass(index, evt);

    // 聚焦搜索框
    nextTick(() => {
      const searchInput = searchInputRefs.value.get(index);
      if (searchInput) {
        searchInput.focus();
      }
    });
  }
};

// 关闭字段下拉框
const closeFieldDropdown = () => {
  activeDropdownIndex.value = null;
  fieldSearchText.value = '';
  removeAllHighlightClasses();
};

// 添加高亮样式
const addHighlightClass = (index: number, evt?: Event) => {
  // 优先使用事件对象精确定位td元素
  let parentTd: HTMLElement | null = null;

  if (evt?.target) {
    const selector = (evt.target as HTMLElement).closest('.field-selector');
    parentTd = selector ? selector.closest('td') as HTMLElement : null;
  }

  // 如果事件对象定位失败，回退到索引查找
  if (!parentTd) {
    parentTd = findParentTd(index);
  }

  if (parentTd) {
    parentTd.classList.add('field-dropdown-active');
    // 同时设置选择器的active类
    const fieldSelector = parentTd.querySelector('.field-selector');
    if (fieldSelector) {
      fieldSelector.classList.add('active');
    }
  }
};

// 移除所有高亮样式
const removeAllHighlightClasses = () => {
  document.querySelectorAll('.field-dropdown-active').forEach(el => {
    el.classList.remove('field-dropdown-active');
  });

  document.querySelectorAll('.field-selector.active').forEach(el => {
    el.classList.remove('active');
  });
};

// 查找父级td元素
const findParentTd = (index: number): HTMLElement | null => {
  // 通过表格行索引查找对应的td元素
  const table = document.querySelector('.data-params-table');
  if (!table) return null;

  const rows = table.querySelectorAll('tbody tr');
  if (index >= 0 && index < rows.length) {
    const row = rows[index];
    // 查找包含字段选择器的td（通常是第二列，索引为1）
    const fieldTd = row.querySelector('td:nth-child(2)');
    return fieldTd as HTMLElement;
  }

  return null;
};

// 从下拉框选择字段
const selectFieldFromDropdown = (param: QueryParam, index: number, field: any) => {
  console.log('[QueryConditionsPanel] 从下拉框选择字段:', field.name);

  // 检查是否已存在同名参数
  const existingParam = queryParams.value.find((p, i) => i !== index && p.name === field.name);
  if (existingParam) {
    message.error(`字段 "${field.name}" 已存在，请选择其他字段`);
    return;
  }

  // 根据字段类型自动设置数据格式和表单类型
  const fieldType = field.type || 'string';
  const autoFormat = getFormatFromType(fieldType);
  const autoFormType = getFormTypeFromType(fieldType);

  console.log(`[QueryConditionsPanel] 字段联动设置: 字段=${field.name}, 类型=${fieldType}, 格式=${autoFormat}, 表单类型=${autoFormType}`);

  // 更新参数信息
  const updatedParam = {
    ...param,
    name: field.name,
    type: fieldType,
    description: field.label || field.name,
    tableName: field.tableName || '',
    isEncrypted: field.isEncrypted || false,
    format: autoFormat, // 自动设置数据格式
    formType: autoFormType, // 自动设置表单类型
    isNewParam: false // 标记为非新参数
  };

  // 更新参数列表
  const newParams = [...queryParams.value];
  newParams[index] = updatedParam;
  queryParams.value = newParams;

  // 发出更新事件
  emit('update:modelValue', newParams);

  // 关闭下拉框
  closeFieldDropdown();

  message.success(`已选择字段 "${field.label || field.name}"`);
};

// 获取字段标签
const getFieldLabel = (fieldName: string) => {
  const field = availableFields.value.find(f => f.name === fieldName);
  return field ? (field.label || field.name) : fieldName;
};

// 设置搜索输入框引用
const setSearchInputRef = (el: HTMLInputElement | null, index: number) => {
  if (el) {
    searchInputRefs.value.set(index, el);
  } else {
    searchInputRefs.value.delete(index);
  }
};

/**
 * 根据类型获取格式
 */
const getFormatFromType = (type: string): string => {
  const lowerType = type.toLowerCase();

  if (lowerType.includes('int') || lowerType.includes('number')) {
    return 'int';
  } else if (lowerType.includes('float') || lowerType.includes('double') || lowerType.includes('decimal')) {
    return 'decimal';
  } else if (lowerType.includes('date') && lowerType.includes('time')) {
    return 'date-time';
  } else if (lowerType.includes('date')) {
    return 'date';
  } else if (lowerType.includes('bool')) {
    return 'boolean';
  }

  return 'string';
};

/**
 * 根据类型获取表单类型
 */
const getFormTypeFromType = (type: string): string => {
  const lowerType = type.toLowerCase();

  if (lowerType.includes('int') || lowerType.includes('number') ||
      lowerType.includes('float') || lowerType.includes('double') ||
      lowerType.includes('decimal')) {
    return 'number';
  } else if (lowerType.includes('date') && lowerType.includes('time')) {
    // 如果包含"range"或"区间"关键字，返回date-range
    if (lowerType.includes('range') || lowerType.includes('区间')) {
      return 'date-range';
    }
    return 'date';
  } else if (lowerType.includes('date')) {
    // 如果包含"range"或"区间"关键字，返回date-range
    if (lowerType.includes('range') || lowerType.includes('区间')) {
      return 'date-range';
    }
    return 'date';
  } else if (lowerType.includes('bool')) {
    return 'checkbox';
  } else if (lowerType.includes('enum')) {
    return 'select';
  }

  return 'text';
};

// 确认添加参数
const confirmAddParam = () => {
  if (!selectedField.value) return;

  // 获取选中的字段完整信息
  const fieldInfo = selectedFieldInfo.value;
  if (!fieldInfo) {
    message.error('无法获取字段信息');
    return;
  }

  // 确定正确的format值
  let format = (fieldInfo.type || 'string').toLowerCase();
  const paramType = convertDataTypeToParamType(fieldInfo.type || 'string');

  // 对于数字类型，明确设置为int或decimal
  if (paramType === 'number') {
    if (format === 'integer' || format === 'int' || format.includes('int') || format === 'number') {
      format = 'int';
    } else if (format.includes('decimal') ||
              format.includes('float') ||
              format.includes('double')) {
      format = 'decimal';
    } else {
      // 如果没有明确的类型信息，默认设置为int
      format = 'int';
      console.log(`[QueryConditionsPanel] 添加参数时未识别的数字类型: ${fieldInfo.type}, 默认设置为int`);
    }
  }

  // 创建新参数
  const newParam: QueryParam = {
    name: fieldInfo.name,
    type: paramType,
    description: fieldInfo.label || fieldInfo.name,
    format: format,
    formType: getFormTypeFromParamType(fieldInfo.type || 'string'),
    required: false,
    isNewParam: true,
    options: [],
    displayOrder: queryParams.value.length,
    tableName: fieldInfo.tableName || '',
    isEncrypted: fieldInfo.isEncrypted || false
  };

  // 针对特殊类型设置额外属性
  if (newParam.type === 'number') {
    // 为数字类型添加默认验证范围
    if (newParam.format === 'integer' || newParam.format === 'int') {
      newParam.minValue = -2147483648;  // INT的最小值
      newParam.maxValue = 2147483647;   // INT的最大值

      // 设置数字格式配置
      newParam.format = 'int';

      // 确保config对象存在
      if (!newParam.config) {
        newParam.config = {};
      }

      // 设置数字类型默认配置
      newParam.config.fixedPoint = "0";
      newParam.config.thousandSeparator = true;

      // 同步到顶级属性，以便在UI中显示
      newParam.fixedPoint = newParam.config.fixedPoint;
      newParam.thousandSeparator = newParam.config.thousandSeparator;

      console.log(`[QueryConditionsPanel] 设置整数格式配置: format=${newParam.format}, fixedPoint=${newParam.config.fixedPoint}, thousandSeparator=${newParam.config.thousandSeparator}`);
    } else if (newParam.format.includes('decimal') ||
              newParam.format.includes('float') ||
              newParam.format.includes('double')) {
      // 设置数字格式配置
      newParam.format = 'decimal';

      // 确保config对象存在
      if (!newParam.config) {
        newParam.config = {};
      }

      // 设置数字类型默认配置
      newParam.config.fixedPoint = "2";
      newParam.config.thousandSeparator = true;

      // 同步到顶级属性，以便在UI中显示
      newParam.fixedPoint = newParam.config.fixedPoint;
      newParam.thousandSeparator = newParam.config.thousandSeparator;

      console.log(`[QueryConditionsPanel] 设置小数格式配置: format=${newParam.format}, fixedPoint=${newParam.config.fixedPoint}, thousandSeparator=${newParam.config.thousandSeparator}`);
    }
  } else if (newParam.type === 'string') {
    // 为字符串类型添加默认最大长度
    newParam.maxLength = 255;
  } else if (newParam.type === 'date') {
    // 为日期类型设置默认格式
    newParam.dateFormat = 'YYYY-MM-DD';
  }

  // 添加到参数列表
  queryParams.value.push(newParam);

  // 初始化参数值
  if (props.paramValues) {
    const newParamValues = { ...props.paramValues };
    if (newParam.formType === 'date-range') {
      // 为日期区间类型初始化起始和结束日期值
      newParamValues[newParam.name + '_start'] = '';
      newParamValues[newParam.name + '_end'] = '';
      // 设置默认值为空，避免显示undefined
      newParam.defaultValue = '';
    } else {
      newParamValues[newParam.name] = newParam.defaultValue || '';
    }
    emit('update:paramValues', newParamValues);
  }

  // 关闭对话框并重置选择
  showAddParamModal.value = false;
  selectedField.value = '';

  message.success(`参数 "${newParam.name}" 添加成功`);
};

// 取消添加参数
const cancelAddParam = () => {
  showAddParamModal.value = false;
  selectedField.value = '';
};

// 从数据源加载字段并重试添加参数
const loadFieldsAndRetry = async () => {
  try {
    message.info('正在从数据源加载字段...');
    // 触发从数据源加载参数
    emit('loadParamsFromQuery');

    // 等待一段时间让数据加载完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 检查是否成功加载了字段
    if (availableFields.value.length > 0) {
      message.success('字段加载成功');
    } else {
      message.warning('未能从数据源获取到字段信息，请检查查询配置');
    }
  } catch (error) {
    console.error('[QueryConditionsPanel] 加载字段失败:', error);
    message.error('加载字段失败，请稍后重试');
  }
};

// 在弹窗中加载字段
const loadFieldsInModal = async () => {
  try {
    isLoadingFields.value = true;
    console.log('[QueryConditionsPanel] 在弹窗中加载字段...');

    // 直接调用字段获取API，而不是触发参数加载
    if (!props.queryId) {
      message.warning('请先选择查询');
      return;
    }

    // 调用API获取字段数据
    const url = `/api/queries/${props.queryId}/parameters`;
    const response = await globalApiRequestManager.request(
      () => instance.get(url),
      url,
      { queryId: props.queryId },
      { cacheTime: 30000 }
    );
    console.log('[QueryConditionsPanel] 字段API响应:', response.data);
    console.log('[QueryConditionsPanel] 响应数据类型:', typeof response.data);
    console.log('[QueryConditionsPanel] 响应数据结构:', Object.keys(response.data || {}));

    // 处理响应数据，提取字段信息
    let fields = [];
    const responseData = response.data;

    // 更详细的数据结构分析
    if (responseData) {
      console.log('[QueryConditionsPanel] responseData存在，分析结构...');

      // 直接从响应根级别获取字段数据
      // 优先使用 searchFields
      if (responseData.searchFields && Array.isArray(responseData.searchFields)) {
        fields = responseData.searchFields;
        console.log('[QueryConditionsPanel] 使用searchFields:', fields.length, '个字段');
      }
      // 其次使用 fields
      else if (responseData.fields && Array.isArray(responseData.fields)) {
        fields = responseData.fields;
        console.log('[QueryConditionsPanel] 使用fields:', fields.length, '个字段');
      }
      // 最后使用 parameters
      else if (responseData.parameters && Array.isArray(responseData.parameters)) {
        fields = responseData.parameters;
        console.log('[QueryConditionsPanel] 使用parameters:', fields.length, '个字段');
      }
      // 如果responseData本身就是数组
      else if (Array.isArray(responseData)) {
        fields = responseData;
        console.log('[QueryConditionsPanel] responseData本身是数组:', fields.length, '个字段');
      }
      else {
        console.log('[QueryConditionsPanel] 无法解析字段数据，responseData结构不符合预期');
        console.log('[QueryConditionsPanel] responseData内容:', responseData);
      }
    }

    console.log('[QueryConditionsPanel] 最终提取到的字段:', fields);

    if (fields.length > 0) {
      // 转换字段格式
      const formattedFields = fields.map((field: any) => {
        const fieldObj = typeof field === 'string' ? { name: field } : field;
        return {
          name: fieldObj.name,
          type: fieldObj.type || 'string',
          label: fieldObj.label || fieldObj.name,
          tableName: fieldObj.tableName || '',
          isEncrypted: fieldObj.isEncrypted || false
        };
      });

      // 直接更新 availableFields，不触发参数添加
      availableFields.value = formattedFields;

      message.success(`成功加载 ${formattedFields.length} 个字段，请从下拉框中选择`);
      console.log('[QueryConditionsPanel] 字段加载成功:', formattedFields.map(f => f.name).join(', '));
    } else {
      message.warning('未能从数据源获取到字段信息，请检查查询配置');
      console.log('[QueryConditionsPanel] 字段加载失败，fields为空');
    }
  } catch (error) {
    console.error('[QueryConditionsPanel] 在弹窗中加载字段失败:', error);
    message.error('加载字段失败，请稍后重试');
  } finally {
    isLoadingFields.value = false;
  }
};

// 将数据类型转换为参数类型
const convertDataTypeToParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
    case 'number':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'boolean':
    case 'bool':
      return 'boolean';

    case 'string':
    case 'varchar':
    case 'char':
    case 'text':
    default:
      return 'string';
  }
};

// 获取表单类型
const getFormTypeFromParamType = (type: string) => {
  // 统一转为小写进行比较
  const lowerType = type.toLowerCase();

  switch (lowerType) {
    case 'number':
    case 'integer':
    case 'int':
    case 'float':
    case 'double':
    case 'decimal':
      return 'number';

    case 'date':
    case 'datetime':
    case 'timestamp':
      return 'date';

    case 'daterange':
    case 'date-range':
    case 'date_range':
    case 'datetimerange':
    case 'datetime-range':
    case 'datetime_range':
      return 'date-range';

    case 'boolean':
    case 'bool':
      return 'checkbox';

    case 'select':
    case 'enum':
      return 'select';

    case 'textarea':
    case 'text_area':
      return 'textarea';

    case 'password':
      return 'password';

    default:
      return 'text';
  }
};

// 移除查询参数
const removeQueryParam = (index: number) => {
  // 保存待删除的参数名，用于调试
  const deletedParam = queryParams.value[index];
  console.log('[QueryConditionsPanel] 删除参数:', deletedParam.name);

  // 删除参数
  queryParams.value.splice(index, 1);

  // 更新显示顺序
  queryParams.value.forEach((param, idx) => {
    param.displayOrder = idx;
  });

  // 注意：当前实现中，删除参数后不会自动将其恢复到可用字段列表(availableFields)中
  // 这可能导致用户无法重新添加已删除的字段，除非重新从数据源加载
  // TODO: 考虑在删除参数时，将对应字段加回到availableFields列表

  // 强制发送更新事件，确保计算属性重新计算
  emit('update:modelValue', [...queryParams.value]);

  console.log('[QueryConditionsPanel] 删除后状态:', {
    '参数数量': queryParams.value.length,
    '可用字段数量': availableFields.value.length,
    '未添加字段数量': nonAddedFields.value.length,
    '显示添加按钮': shouldShowAddButton.value
  });
};

// 处理拖拽排序
const onParamDragChange = () => {
  // 更新显示顺序
  queryParams.value.forEach((param, index) => {
    param.displayOrder = index;
  });

  // 发出事件通知父组件排序已更改
  emit('update:modelValue', queryParams.value);

  // 提示用户排序已更新
  message.success('参数顺序已更新');

  console.log('[QueryConditionsPanel] 参数排序已更新，新的顺序:',
    queryParams.value.map(p => `${p.name}:${p.displayOrder}`).join(', '));
};

// 验证参数
const validateParams = (): boolean => {
  console.log('[QueryConditionsPanel] 开始参数验证, 集成类型:', props.integrationType);
  let isValid = true;
  const errors: Record<string, string> = {};
  const isChartMode = props.integrationType === 'CHART';

  // 在图表模式下，不验证是否有查询条件
  if (!isChartMode && (!queryParams.value || queryParams.value.length === 0)) {
    console.log('[QueryConditionsPanel] 验证失败: 至少需要一个查询条件');
    isValid = false;
    errors['_global'] = '至少需要一个查询条件';
  } else {
    console.log('[QueryConditionsPanel] 验证查询条件', isChartMode ? '(图表模式不要求条件)' : '(需要至少一个条件)');

    // 始终验证格式和类型，即使在图表模式下
    queryParams.value.forEach((param: QueryParam, index: number) => {
      const paramId = `param_${index}`;

      // 检查名称是否为空
      if (!param.name) {
        isValid = false;
        errors[paramId] = '参数名称不能为空';
      }

      // 检查类型是否合法
      if (!param.type) {
        isValid = false;
        errors[`${paramId}_type`] = '参数类型不能为空';
      }

      // 对于数值参数，检查范围
      if (param.type === 'number' && param.min !== undefined && param.max !== undefined && param.min > param.max) {
        isValid = false;
        errors[`${paramId}_range`] = '最小值不能大于最大值';
      }

      // 对于字符串参数，检查长度
      if (param.type === 'string' && param.minLength !== undefined && param.maxLength !== undefined && param.minLength > param.maxLength) {
        isValid = false;
        errors[`${paramId}_length`] = '最小长度不能大于最大长度';
      }

      // 枚举参数必须有选项
      if (param.type === 'enum' && (!param.options || param.options.length === 0)) {
        isValid = false;
        errors[`${paramId}_options`] = '枚举参数必须有选项';
      }
    });
  }

  console.log('[QueryConditionsPanel] 验证结果:', isValid ? '通过' : '失败', errors);
  emit('validate', errors);
  return isValid;
};

// 更新可用字段列表
const updateAvailableFields = (fields: Array<{name: string, type: string, label: string, tableName?: string, isEncrypted?: boolean}>) => {
  console.log('[QueryConditionsPanel] 更新可用字段开始:', {
    '传入字段数量': fields.length,
    '传入字段列表': fields.map(f => f.name).join(', ')
  });
  availableFields.value = fields;

  // 在更新后立即计算和记录状态
  nextTick(() => {
    console.log('[QueryConditionsPanel] 更新可用字段完成后状态:', {
      '可用字段数量': availableFields.value.length,
      '当前参数数量': queryParams.value.length,
      '未添加字段数量': nonAddedFields.value.length,
      '未添加字段': nonAddedFields.value.map(f => f.name).join(', '),
      '是否应显示添加按钮': shouldShowAddButton.value
    });
  });
};

// 打开枚举选择器
const openEnumSelector = (param: QueryParam, index: number) => {
  currentParam.value = param;
  currentParamIndex.value = index;
  showEnumSelectorModal.value = true;
};

// 处理枚举选择
const handleEnumSelect = (result: {
  options: Array<{label: string, value: string}>;
  enumId: string;
  enumName: string;
  enumCode: string;
}) => {
  if (currentParam.value && currentParamIndex.value >= 0) {
    console.log('[QueryConditionsPanel] 处理枚举选择，原始数据:', JSON.stringify(result));

    // 确保options有效
    if (!Array.isArray(result.options) || result.options.length === 0) {
      console.error('[QueryConditionsPanel] 枚举选项无效:', result.options);
      message.error('枚举选项无效，请选择包含有效选项的枚举或创建新枚举');
      return;
    }

    // 更新参数的选项列表
    queryParams.value[currentParamIndex.value].options = result.options;

    // 额外打印选项列表，确认数据正确
    console.log('[QueryConditionsPanel] 枚举选项已更新:',
      result.options.map(o => `${o.label} (${o.value})`).join(', '));

    // 保存枚举相关信息
    queryParams.value[currentParamIndex.value].enumId = result.enumId;
    queryParams.value[currentParamIndex.value].enumName = result.enumName;
    queryParams.value[currentParamIndex.value].enumCode = result.enumCode;

    // 确保格式为enum
    queryParams.value[currentParamIndex.value].format = 'enum';

    // 如果是当前没有选择值，则设置第一个选项为默认值
    if (props.paramValues && !props.paramValues[queryParams.value[currentParamIndex.value].name]) {
      console.log('[QueryConditionsPanel] 当前参数无值，设置默认值:', result.options[0].value);
      const newParamValues = { ...props.paramValues };
      newParamValues[queryParams.value[currentParamIndex.value].name] = result.options[0].value;
      emit('update:paramValues', newParamValues);
    }

    // 重新触发更新
    emit('update:modelValue', [...queryParams.value]);

    // 显示成功消息
    message.success(`已从枚举"${result.enumName}"加载${result.options.length}个选项`);

    // 关闭枚举选择器
    showEnumSelectorModal.value = false;

    // 重置状态
    currentParam.value = null;
    currentParamIndex.value = -1;
  } else {
    console.error('[QueryConditionsPanel] 当前没有选中参数，无法应用枚举');
  }
};

// 新增：获取指定组可用的字段列表（过滤掉已添加的字段）
const getAvailableFieldsForGroup = computed(() => {
  return (groupIndex: number, currentFieldIndex?: number) => {
    if (!fieldGroups.value[groupIndex]) {
      return availableSearchFields.value;
    }

    // 只收集当前组内已选择的字段名（排除当前正在编辑的字段）
    const currentGroupSelectedFields = new Set<string>();

    const currentGroup = fieldGroups.value[groupIndex];
    currentGroup.fields.forEach((field, fIndex) => {
      // 排除当前正在编辑的字段位置
      const isCurrentField = fIndex === currentFieldIndex;

      // 如果字段有名称且不为空，且不是当前正在编辑的字段，则添加到已选择列表
      if (field.name && field.name.trim() !== '' && !isCurrentField) {
        currentGroupSelectedFields.add(field.name);
      }
    });

    console.log(`[QueryConditionsPanel] 组${groupIndex + 1}内已选择字段(排除当前):`, Array.from(currentGroupSelectedFields));

    // 过滤出未被当前组其他位置选择的字段（允许跨组重复）
    const availableFields = availableSearchFields.value.filter(field =>
      !currentGroupSelectedFields.has(field.name)
    );

    console.log(`[QueryConditionsPanel] 组${groupIndex + 1}可用字段:`, availableFields.map(f => f.name));

    return availableFields;
  };
});

// 新增：获取筛选后的字段列表
const getFilteredFieldsForGroup = computed(() => {
  return (groupIndex: number, fieldIndex: number) => {
    const selectKey = getFieldSelectKey(groupIndex, fieldIndex);
    const filterText = fieldFilterTexts.value[selectKey] || '';
    const availableFields = getAvailableFieldsForGroup.value(groupIndex, fieldIndex);

    if (!filterText.trim()) {
      return availableFields;
    }

    // 根据筛选文本过滤字段
    const filtered = availableFields.filter(field => {
      const searchText = filterText.toLowerCase();
      return (
        field.name.toLowerCase().includes(searchText) ||
        (field.label && field.label.toLowerCase().includes(searchText))
      );
    });

    console.log(`[QueryConditionsPanel] 组${groupIndex + 1}字段${fieldIndex + 1}筛选结果:`,
      `"${filterText}" -> ${filtered.length}个字段`);

    return filtered;
  };
});

// 新增：字段筛选相关方法
const onFieldFilterInput = (groupIndex: number, fieldIndex: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const selectKey = getFieldSelectKey(groupIndex, fieldIndex);
  fieldFilterTexts.value[selectKey] = target.value;

  // 显示下拉列表
  fieldDropdownVisible.value[selectKey] = true;

  console.log(`[QueryConditionsPanel] 字段筛选输入:`, {
    groupIndex,
    fieldIndex,
    filterText: target.value
  });
};

const onFieldFilterFocus = (groupIndex: number, fieldIndex: number) => {
  const selectKey = getFieldSelectKey(groupIndex, fieldIndex);
  fieldDropdownVisible.value[selectKey] = true;
};

const onFieldFilterBlur = (groupIndex: number, fieldIndex: number) => {
  const selectKey = getFieldSelectKey(groupIndex, fieldIndex);
  // 延迟隐藏，允许点击选项
  setTimeout(() => {
    fieldDropdownVisible.value[selectKey] = false;
  }, 200);
};

const onFieldOptionSelect = (groupIndex: number, fieldIndex: number, field: any) => {
  const selectKey = getFieldSelectKey(groupIndex, fieldIndex);

  // 更新字段选择
  const targetField = fieldGroups.value[groupIndex].fields[fieldIndex];
  targetField.name = field.name;
  targetField.label = field.label;
  targetField.type = field.type;

  // 更新筛选文本为选中的字段名
  fieldFilterTexts.value[selectKey] = field.label || field.name;

  // 隐藏下拉列表
  fieldDropdownVisible.value[selectKey] = false;

  // 强制触发响应式更新
  fieldGroups.value = [...fieldGroups.value];

  console.log('[QueryConditionsPanel] 字段选择完成:', {
    groupIndex,
    fieldIndex,
    field: { name: field.name, label: field.label, type: field.type }
  });
};

const clearFieldSelection = (groupIndex: number, fieldIndex: number) => {
  const selectKey = getFieldSelectKey(groupIndex, fieldIndex);

  // 清空字段选择
  const targetField = fieldGroups.value[groupIndex].fields[fieldIndex];
  targetField.name = '';
  targetField.label = '';
  targetField.type = '';

  // 清空筛选文本
  fieldFilterTexts.value[selectKey] = '';

  // 强制触发响应式更新
  fieldGroups.value = [...fieldGroups.value];

  console.log('[QueryConditionsPanel] 清空字段选择:', { groupIndex, fieldIndex });
};

// 自动加载字段（不显示加载状态）
const autoLoadFields = async () => {
  try {
    console.log('[QueryConditionsPanel] 自动加载字段...');

    if (!props.queryId) {
      return;
    }

    // 调用API获取字段数据
    const url = `/api/queries/${props.queryId}/parameters`;
    const response = await globalApiRequestManager.request(
      () => instance.get(url),
      url,
      { queryId: props.queryId },
      { cacheTime: 30000 }
    );
    console.log('[QueryConditionsPanel] 自动加载字段API响应:', response.data);

    // 处理响应数据，提取字段信息
    let fields = [];
    const responseData = response.data;

    if (responseData) {
      // 直接从响应根级别获取字段数据
      if (responseData.searchFields && Array.isArray(responseData.searchFields)) {
        fields = responseData.searchFields;
        console.log('[QueryConditionsPanel] 自动加载使用searchFields:', fields.length, '个字段');
      }
      else if (responseData.fields && Array.isArray(responseData.fields)) {
        fields = responseData.fields;
        console.log('[QueryConditionsPanel] 自动加载使用fields:', fields.length, '个字段');
      }
      else if (responseData.parameters && Array.isArray(responseData.parameters)) {
        fields = responseData.parameters;
        console.log('[QueryConditionsPanel] 自动加载使用parameters:', fields.length, '个字段');
      }
      else if (Array.isArray(responseData)) {
        fields = responseData;
        console.log('[QueryConditionsPanel] 自动加载使用responseData数组:', fields.length, '个字段');
      }
    }

    if (fields.length > 0) {
      // 转换字段格式
      const formattedFields = fields.map((field: any) => {
        const fieldObj = typeof field === 'string' ? { name: field } : field;
        return {
          name: fieldObj.name,
          type: fieldObj.type || 'string',
          label: fieldObj.label || fieldObj.name,
          tableName: fieldObj.tableName || '',
          isEncrypted: fieldObj.isEncrypted || false
        };
      });

      // 直接更新 availableFields
      availableFields.value = formattedFields;

      console.log('[QueryConditionsPanel] 自动加载字段成功:', formattedFields.length, '个字段');
    } else {
      console.log('[QueryConditionsPanel] 自动加载字段失败，fields为空');
    }
  } catch (error) {
    console.error('[QueryConditionsPanel] 自动加载字段失败:', error);
  }
};

// 监听queryId变化，自动加载字段
watch(() => props.queryId, async (newQueryId) => {
  if (newQueryId && newQueryId !== '') {
    console.log('[QueryConditionsPanel] queryId变化，自动加载字段:', newQueryId);
    // 延迟一点时间确保组件完全初始化
    await nextTick();
    setTimeout(() => {
      autoLoadFields();
    }, 500);
  } else {
    // 清空字段列表
    availableFields.value = [];
    console.log('[QueryConditionsPanel] queryId为空，清空字段列表');
  }
});

// 组件挂载时也自动加载字段
onMounted(() => {
  if (props.queryId) {
    console.log('[QueryConditionsPanel] 组件挂载，自动加载字段');
    setTimeout(() => {
      autoLoadFields();
    }, 1000);
  }

  // 添加点击外部关闭下拉框的事件监听
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 处理点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const isSelector = target.closest('.field-selector') !== null;
  const isDropdown = target.closest('.absolute') !== null && !isSelector;

  // 如果点击在下拉菜单和选择器之外，关闭下拉菜单
  if (!isSelector && !isDropdown && activeDropdownIndex.value !== null) {
    closeFieldDropdown();
  }
};

// 将方法暴露给父组件
defineExpose({
  addQueryParam: confirmAddParam,
  removeQueryParam,
  validateParams,
  refreshParamsList,
  updateAvailableFields,
  // 新增：必填组配置相关方法
  loadSearchFields,
  addFieldGroup,
  removeFieldGroup,
  addFieldToGroup,
  removeFieldFromGroup,
  getFieldGroups: () => fieldGroups.value,
  setFieldGroups: (groups: FieldGroup[]) => {
    console.log('[QueryConditionsPanel] 设置字段组数据:', groups);
    fieldGroups.value = groups || [];
  },
  getAvailableSearchFields: () => availableSearchFields.value,
  // 必填组管理抽屉相关方法
  openRequiredGroupsDrawer,
  closeRequiredGroupsDrawer,
  getParamRequiredGroups,
  getGroupColorClass,
  onGroupFieldSelect
});
</script>

<style scoped>
.data-params-table th,
.data-params-table td {
  vertical-align: middle;
  white-space: normal;
  word-break: break-word;
}

.force-border {
  border: 1px solid #d1d5db !important;
}

/* 统一输入框和下拉框高度 */
.uniform-height-input {
  height: 38px;
  min-height: 38px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.uniform-height-input-checkbox {
  height: 38px;
  min-height: 38px;
  display: flex;
  align-items: center;
}

/* 确保所有表单元素使用相同的边框和内边距 */
input.uniform-height-input,
select.uniform-height-input,
.uniform-height-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  line-height: 1.25;
}

/* 确保下拉菜单在所有浏览器中具有一致的外观 */
select.uniform-height-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  min-width: 100px;
  width: 100%;
}

/* 确保表格内容不会被截断 */
.data-params-table {
  table-layout: fixed;
  width: 100%;
}

/* 日期区间样式 */
.date-range-container {
  min-width: 180px;
  max-width: 280px;
  width: 100%;
}

.date-range-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-range-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-range-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  margin: 0;
  padding: 0;
}

.date-range-input {
  height: 36px;
  min-height: 36px;
  padding: 0.4rem 0.6rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25;
  width: 100%;
  box-sizing: border-box;
  background-color: white;
  color: #374151;
}

.date-range-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 1px #6366f1;
}

.date-range-input::-webkit-calendar-picker-indicator {
  cursor: pointer;
  opacity: 0.7;
}

.date-range-input::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

.date-range-separator {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
  padding: 0 2px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .date-range-container {
    min-width: 160px;
    max-width: 240px;
  }

  .date-range-input-group {
    gap: 6px;
  }

  .date-range-item {
    gap: 3px;
  }

  .date-range-input {
    font-size: 0.8rem;
    padding: 0.3rem 0.5rem;
    height: 34px;
    min-height: 34px;
  }

  .date-range-label {
    font-size: 0.7rem;
  }
}

.query-conditions-panel {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.field-select-container {
  position: relative;
}

.field-select-container .form-control {
  width: 100%;
  padding: 8px 30px 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.field-select-container .form-control:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.field-select-container .clear-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  font-size: 12px;
}

.field-select-container .clear-icon:hover {
  color: #666;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dropdown-item {
  cursor: pointer;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item strong {
  display: block;
  font-weight: 500;
  color: #333;
}

.dropdown-item .text-muted {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.field-group {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: white;
}

.field-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.field-group-title {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.btn-group-action {
  display: flex;
  gap: 8px;
}

.btn-group-action .btn {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.field-item:last-child {
  margin-bottom: 0;
}

.btn-remove-field {
  padding: 2px 6px;
  font-size: 12px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.btn-remove-field:hover {
  background-color: #c82333;
}

.btn-add-field {
  margin-top: 10px;
  padding: 6px 12px;
  font-size: 13px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-add-field:hover {
  background-color: #218838;
}

.btn-add-group {
  margin-top: 15px;
  padding: 8px 16px;
  font-size: 14px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-add-group:hover {
  background-color: #0056b3;
}

/* 字段选择器样式 - 与查询结果配置保持一致 */
.field-selector {
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 10;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
}

.field-selector.active {
  z-index: 9999;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* 确保下拉菜单显示在最上层 */
.relative {
  position: relative;
  z-index: 10;
}

.relative:focus-within {
  z-index: 9999;
}

/* 下拉菜单内容 - 使用更高的z-index */
.absolute {
  z-index: 10000;
  position: absolute;
  width: 100%;
  left: 0;
  top: 100%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.max-h-60 {
  max-height: 240px;
}

/* 搜索框 */
.field-search-input {
  z-index: 10001;
  position: relative;
}

/* 确保表格行在下拉框激活时有正确的层级 */
.data-params-table tbody tr {
  position: relative;
}

.data-params-table tbody tr:has(.field-selector.active) {
  z-index: 9999;
}

/* 字段下拉框激活状态 - 与查询结果配置保持一致 */
.field-dropdown-active,
.field-dropdown-active .relative {
  z-index: 1000 !important;
  position: relative;
}

/* 确保下拉框在表格中正确显示 */
.data-params-table .field-dropdown-active {
  z-index: 1000 !important;
}

.data-params-table .field-dropdown-active .absolute {
  z-index: 1001 !important;
}
</style>
