<template>
  <div class="enum-selector">
    <!-- 调试按钮 -->
    <button
      v-if="debug"
      @click="debug = false"
      class="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center rounded-full bg-gray-200 hover:bg-gray-300 text-xs"
      title="关闭调试模式"
    >
      <i class="fas fa-bug"></i>
    </button>

    <!-- 选择枚举对话框 -->
    <div v-if="showSelectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center enum-selector-dialog" style="z-index: 9999 !important;">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-hidden">
        <!-- 对话框标题 -->
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">选择枚举</h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 对话框内容 -->
        <div class="px-6 py-4 flex-1 overflow-y-auto">
          <!-- 搜索和筛选 -->
          <div class="flex items-center mb-4 space-x-4">
            <div class="w-1/3">
              <label class="block text-sm font-medium text-gray-700 mb-1">项目代码</label>
              <input
                v-model="filterProjectCode"
                class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :placeholder="`请输入有效项目代码，默认：${enumServiceConfig.projectCode}`"
              />
            </div>
            <div class="flex-1">
              <label class="block text-sm font-medium text-gray-700 mb-1">关键词搜索</label>
              <div class="relative rounded-md shadow-sm">
                <input
                  v-model="searchKeyword"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md pl-10"
                  placeholder="搜索枚举名称或代码..."
                />
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fas fa-search text-gray-400"></i>
                </div>
              </div>
            </div>
            <div class="flex items-end">
              <button
                @click="handleSearch"
                type="button"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
              >
                <i class="fas fa-sync mr-1"></i> 刷新枚举列表
              </button>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center items-center py-8">
            <i class="fas fa-spinner fa-spin text-indigo-500 text-2xl"></i>
            <span class="ml-2 text-gray-600">加载中...</span>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && enumerations.length === 0" class="py-10 text-center">
            <div class="text-gray-400 mb-3">
              <i class="fas fa-search fa-3x"></i>
            </div>
            <p class="text-gray-500 font-medium">{{ error || '未找到匹配的枚举' }}</p>
            <p class="text-sm text-gray-400 mt-2">
              请尝试以下操作：
            </p>
            <div class="flex flex-col items-center gap-2 mt-4">
              <button
                @click="loadEnumerations('')"
                class="px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 flex items-center"
              >
                <i class="fas fa-sync-alt mr-1"></i> 重新加载枚举列表
              </button>
              <button
                @click="emitCreateNew"
                class="px-4 py-2 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 flex items-center"
              >
                <i class="fas fa-plus mr-1"></i> 创建新枚举
              </button>
            </div>
          </div>

          <!-- 枚举列表 -->
          <div v-if="enumerations.length > 0" class="space-y-3">
            <div v-for="(enumeration, index) in enumerations" :key="enumeration.id || index"
                 class="border border-gray-200 rounded-md p-3 hover:bg-gray-50"
                 :class="{ 'ring-2 ring-green-500 bg-green-50': enumeration.code === props.currentEnumCode }">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h4 class="font-medium text-gray-900">{{ enumeration.name }}</h4>
                    <!-- 当前使用标记 -->
                    <span v-if="enumeration.code === props.currentEnumCode" 
                          class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      <i class="fas fa-check-circle mr-1"></i>
                      当前使用
                    </span>
                  </div>
                  <p class="text-sm text-gray-500">代码: {{ enumeration.code }}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="text-xs bg-gray-100 rounded px-2 py-1">
                    {{ enumeration.content ? enumeration.content.length : 0 }} 个选项
                  </div>
                  <!-- 操作按钮组 -->
                  <div class="flex space-x-1">
                    <button
                      @click.stop="previewEnumeration(enumeration)"
                      class="p-1 text-gray-500 hover:text-blue-600 focus:outline-none"
                      title="预览"
                    >
                      <i class="fas fa-eye"></i>
                    </button>
                    <button
                      @click.stop="editEnumeration(enumeration)"
                      class="p-1 text-gray-500 hover:text-orange-600 focus:outline-none"
                      title="编辑枚举"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      @click.stop="selectEnumeration(enumeration)"
                      class="p-1 text-gray-500 hover:text-green-600 focus:outline-none"
                      :class="{ 'text-green-600': enumeration.code === props.currentEnumCode }"
                      :title="enumeration.code === props.currentEnumCode ? '当前使用' : '使用'"
                    >
                      <i class="fas fa-check"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 选项预览 -->
              <div v-if="enumeration.content && enumeration.content.length > 0" class="mt-2 pt-2 border-t border-gray-100">
                <p class="text-xs text-gray-500 mb-1">选项预览:</p>
                <div class="flex flex-wrap gap-1">
                  <div v-for="(item, i) in enumeration.content.slice(0, 5)" :key="i"
                       class="bg-gray-100 text-gray-600 text-xs px-2 py-0.5 rounded">
                    <span class="font-medium">{{ item.value }}</span>
                    <span class="text-gray-400 mx-1">|</span>
                    <span class="text-gray-500">{{ item.key }}</span>
                  </div>
                  <div v-if="enumeration.content.length > 5"
                       class="bg-gray-100 text-gray-600 text-xs px-2 py-0.5 rounded cursor-pointer"
                       @click.stop="previewEnumeration(enumeration)">
                    +{{ enumeration.content.length - 5 }} 个选项
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="enumerations.length > 0" class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
            <div class="text-sm text-gray-500">
              共 {{ enumerations.length }} 条记录
            </div>
          </div>
        </div>

        <!-- 对话框底部 -->
        <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
          <div>
            <button
              @click="debug = !debug"
              class="px-2 py-1 text-xs text-gray-500 hover:text-gray-700 flex items-center"
              title="调试模式"
            >
              <i class="fas fa-bug mr-1"></i> 调试
            </button>
          </div>
          <button
            @click="closeModal"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center" style="z-index: 10000 !important;">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">枚举预览</h3>
          <button @click="closePreviewModal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="px-6 py-4 flex-1 overflow-y-auto">
          <div v-if="previewEnum" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">名称</label>
                <div class="mt-1 text-sm text-gray-900">{{ previewEnum.name }}</div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">代码</label>
                <div class="mt-1 text-sm text-gray-900">{{ previewEnum.code }}</div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">项目</label>
                <div class="mt-1 text-sm text-gray-900">{{ previewEnum.projectCode }}</div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">选项数量</label>
                <div class="mt-1 text-sm text-gray-900">{{ previewEnum.content?.length || 0 }}</div>
              </div>
            </div>

            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">选项列表</label>
              <div class="border border-gray-200 rounded-md overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">标签</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">值</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(item, index) in previewEnum.content" :key="index">
                      <td class="px-4 py-2 text-sm text-gray-900">{{ item.value }}</td>
                      <td class="px-4 py-2 text-sm text-gray-900">{{ item.key }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="closePreviewModal"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            关闭
          </button>
          <button
            v-if="previewEnum"
            @click="selectEnumeration(previewEnum)"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
          >
            使用此枚举
          </button>
        </div>
      </div>
    </div>

    <!-- 调试信息 -->
    <div v-if="debug" class="mt-4 p-2 bg-gray-100 rounded text-xs">
      <div><strong>调试信息:</strong></div>
      <div>项目代码: {{ props.projectCode || '(未设置)' }}</div>
      <div>过滤项目代码: {{ filterProjectCode }}</div>
      <div>加载状态: {{ loading ? '加载中' : '已完成' }}</div>
      <div>枚举项数量: {{ enumerations.length }}</div>

      <div class="mt-2 border-t border-gray-300 pt-2">
        <div class="font-bold">API调试</div>
        <div class="flex space-x-2 mt-1">
          <button
            @click="loadEnumerations('')"
            class="px-2 py-1 bg-blue-200 text-blue-800 rounded text-xs"
          >
            刷新枚举
          </button>
          <button
            @click="fetchDirectFromAPI()"
            class="px-2 py-1 bg-green-200 text-green-800 rounded text-xs"
          >
            直接请求API
          </button>
          <button
            @click="debug = false"
            class="px-2 py-1 bg-red-200 text-red-800 rounded text-xs"
          >
            关闭调试
          </button>
        </div>
      </div>

      <div v-if="rawApiResponse" class="mt-2 border-t border-gray-300 pt-2">
        <div class="font-bold">原始API响应</div>
        <div class="text-xs mt-1 overflow-auto max-h-40 bg-white p-2 rounded">
          code: {{ rawApiResponse.code }}, message: {{ rawApiResponse.message }}
          <div v-if="rawApiResponse.data">
            <div>data类型: {{ typeof rawApiResponse.data }}</div>
            <div v-if="typeof rawApiResponse.data === 'object'">
              <div>data结构: {{ Object.keys(rawApiResponse.data).join(', ') }}</div>
            </div>
            <div v-if="Array.isArray(rawApiResponse.data)">
              <div>array长度: {{ rawApiResponse.data.length }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  listEnumerations,
  getEnumerationById,
  convertEnumerationToOptions,
  type Enumeration
} from '@/api/enumerationService';
import { message } from '@/services/message';
import { enumServiceConfig } from '@/utils/config';
import { enumCache } from '@/utils/enumCache';
import axios from 'axios';
import http from "@/utils/http";

const props = withDefaults(defineProps<{
  disabled?: boolean;
  projectCode: string;
  isDebug?: boolean;
  currentEnumCode?: string;
}>(), {
  disabled: false,
  projectCode: enumServiceConfig.projectCode,
  isDebug: false,
  currentEnumCode: '',
});

interface EnumSelectionResult {
  options: Array<{label: string, value: string}>;
  enumId: string;
  enumName: string;
  enumCode: string;
}

const emit = defineEmits<{
  (e: 'select', result: { options: Array<{label: string, value: string}>; enumId: string; enumName: string; enumCode: string }): void;
  (e: 'close'): void;
  (e: 'create-new'): void;
  (e: 'edit', enumeration: Enumeration): void;
}>();

// 组件状态
const loading = ref(false);
const enumerations = ref<Enumeration[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchKeyword = ref('');
const filterProjectCode = ref(props.projectCode || enumServiceConfig.projectCode);
const showSelectModal = ref(true);
const showPreviewModal = ref(false);
const previewEnum = ref<Enumeration | null>(null);
const error = ref('');
// 使用活跃的消息组件，不需要初始化messageStore
// 调试模式
const debug = ref(false);
// 用于防抖搜索的计时器
let searchTimer: number | null = null;
// API原始响应数据
const rawApiResponse = ref<any>(null);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(total.value / pageSize.value);
});

// 加载枚举列表
const loadEnumerations = async (searchKeyword = '') => {
  // 始终使用有效的项目代码
  const projectCode = filterProjectCode.value || props.projectCode || enumServiceConfig.projectCode;

  if (!projectCode) {
    message.error({
      content: '项目代码不能为空',
      description: '请输入有效的项目代码再尝试加载枚举列表',
      duration: 5000
    });
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    // 强制清除缓存，确保获取最新数据
    enumCache.clear(projectCode);

    console.log('[枚举选择] 开始请求枚举列表，项目代码:', projectCode);

    // 获取枚举列表 - 直接请求所有数据，忽略页码
    const result = await listEnumerations(
      projectCode,
      1,  // 固定页码为1
      1000,  // 设置一个足够大的值来获取所有数据
      searchKeyword
    );

    console.log('[枚举选择] 枚举服务返回结果:', result);

    // 直接检查返回的数据
    if (result && result.list) {
      // 检查返回数据是否为数组
      if (Array.isArray(result.list)) {
        console.log('[枚举选择] 获取到枚举列表, 数量:', result.list.length);

        // 打印数据内容
        if (result.list.length > 0) {
          console.log('[枚举选择] 第一项数据:', JSON.stringify(result.list[0]));
        }

        // 设置到组件状态
        enumerations.value = result.list;
      } else {
        console.error('[枚举选择] 列表数据不是数组类型:', typeof result.list);
        enumerations.value = [];
      }
    } else {
      console.warn('[枚举选择] 未获取到枚举列表数据');
      enumerations.value = [];
    }

    // 如果列表为空，设置提示
    if (enumerations.value.length === 0) {
      error.value = '未找到匹配的枚举，请检查项目代码是否正确，或创建新的枚举';
    }

  } catch (err) {
    console.error('[枚举选择] 加载枚举列表出错:', err);
    enumerations.value = [];
    error.value = `加载枚举列表失败: ${err instanceof Error ? err.message : '未知错误'}`;
    message.error(`加载枚举失败: ${error.value}`);
  } finally {
    loading.value = false;
    console.log('[枚举选择] 枚举列表数据:', enumerations.value);
  }
};

// 选择枚举
const selectEnumeration = (enumeration: Enumeration) => {
  try {
    // 直接使用枚举的content数据
    const options = enumeration.content.map(item => ({
      label: item.value,
      value: item.key
    }));

    if (options.length === 0) {
      message.warning(`所选枚举 "${enumeration.name}" 没有可用选项，请选择其他枚举`);
      return;
    }

    // 发出选择事件
    emit('select', {
      options,
      enumId: enumeration.id || '',
      enumName: enumeration.name || '未命名枚举',
      enumCode: enumeration.code
    });

    // 关闭对话框
    closeModal();

    // 不再显示消息，避免与创建枚举消息重复
    // message.success(`已选择枚举 "${enumeration.name}"，包含 ${options.length} 个选项`);
  } catch (error) {
    console.error('处理枚举选项失败:', error);
    message.error(`处理枚举 "${enumeration.name}" 的选项时发生错误，请重试或选择其他枚举`);
  }
};

// 关闭对话框
const closeModal = () => {
  console.log('关闭枚举选择器对话框');
  showSelectModal.value = false;
  // 重置搜索条件
  currentPage.value = 1;
  searchKeyword.value = '';
  // 发出关闭事件
  emit('close');
};

// 预览枚举
const previewEnumeration = (enumeration: Enumeration) => {
  previewEnum.value = enumeration;
  showPreviewModal.value = true;
};

// 关闭预览
const closePreviewModal = () => {
  showPreviewModal.value = false;
  previewEnum.value = null;
};

// 搜索枚举
const handleSearch = () => {
  // 清除之前的计时器
  if (searchTimer) {
    window.clearTimeout(searchTimer);
  }

  // 设置新的计时器，防止频繁请求
  searchTimer = window.setTimeout(() => {
    // 直接搜索，不涉及分页
    loadEnumerations(searchKeyword.value);
  }, 300);
};

// 监听搜索关键词变化
watch([searchKeyword, filterProjectCode], () => {
  handleSearch();
});

// 在组件挂载时设置初始项目代码
onMounted(() => {
  // 确保有有效的项目代码
  filterProjectCode.value = props.projectCode || enumServiceConfig.projectCode;
  console.log('枚举选择器初始化，项目代码:', filterProjectCode.value);
  console.log('配置的枚举服务项目代码:', enumServiceConfig.projectCode);

  // 确保弹窗显示
  showSelectModal.value = true;

  // 加载枚举列表
  loadEnumerations();
});

// 监听项目代码变化，重新加载枚举列表
watch(() => props.projectCode, (newProjectCode) => {
  if (newProjectCode) {
    console.log('项目代码变化，重新加载枚举列表:', newProjectCode);
    loadEnumerations();
  }
});

// 发送创建新枚举的事件
const emitCreateNew = () => {
  // 关闭选择器
  closeModal();
  // 通知父组件可以直接打开创建枚举对话框
  emit('create-new');
};

// 直接从API获取数据进行调试
const fetchDirectFromAPI = async () => {
  try {
    loading.value = true;
    const timestamp = new Date().getTime();
    const projectCode = filterProjectCode.value || props.projectCode || enumServiceConfig.projectCode;

    // 构建API URL
    const url = `${enumServiceConfig.baseUrl}${enumServiceConfig.apiBasePath}/list?_t=${timestamp}&projectCode=${projectCode}&page=1&pageSize=1000`;

    console.log('[直接API调试] 请求URL:', url);

    // 发起原始请求
    const response = await http.get(url);

    console.log('[直接API调试] 原始响应:', response.data);

    // 保存原始响应
    rawApiResponse.value = response.data;

    // 显示给用户
    message.success({
      content: '原始数据已输出到控制台',
      description: '请使用浏览器开发者工具查看枚举详细数据',
      duration: 3000
    });

    // 尝试解析数据
    if (response.data && response.data.data) {
      // 检查data的类型
      if (Array.isArray(response.data.data)) {
        enumerations.value = response.data.data;
        console.log('[直接API调试] 设置数组数据:', response.data.data.length);
      } else if (response.data.data.list && Array.isArray(response.data.data.list)) {
        enumerations.value = response.data.data.list;
        console.log('[直接API调试] 设置列表数据:', response.data.data.list.length);
      } else if (typeof response.data.data === 'number') {
        console.log('[直接API调试] data为数字:', response.data.data);
      } else {
        console.log('[直接API调试] 未知数据结构:', typeof response.data.data);
      }
    }

  } catch (err) {
    console.error('[直接API调试] 请求失败:', err);
    message.error({
      content: '请求失败',
      description: '请查看浏览器控制台了解详细错误信息',
      duration: 5000
    });
  } finally {
    loading.value = false;
  }
};

// 编辑枚举
const editEnumeration = (enumeration: Enumeration) => {
  emit('edit', enumeration);
  closeModal();
};
</script>

<style scoped>
.uniform-height-input {
  height: 38px;
  min-height: 38px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.enum-selector-dialog {
  z-index: 9999 !important;
}

.enum-selector-dialog button {
  cursor: pointer;
  outline: none;
}

/* 优化弹窗布局 */
.enum-selector-dialog .bg-white {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

/* 确保底部按钮始终可见 */
.px-6.py-4.border-t {
  background-color: white;
  position: sticky;
  bottom: 0;
  z-index: 10;
}
</style>
