<template>
  <div v-if="isVisible" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center" style="z-index: 5000;">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
      <!-- 对话框标题 -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">参数配置 - {{ paramName }}</h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 对话框内容 -->
      <div class="px-6 py-4 flex-grow overflow-y-auto param-advanced-config">
        <div v-if="localParam" class="space-y-4">
          <!-- 通用配置 -->
          <div class="mb-4 pb-4 border-b border-gray-200">
            <h4 class="text-md font-medium text-gray-900 mb-2">通用配置</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">标签文本</label>
                <a-input
                  v-model:value="localParam.label"
                  placeholder="表单中的标签文本"
                  class="w-full"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">占位符</label>
                <a-input
                  v-model:value="localParam.placeholder"
                  placeholder="输入框的占位文本"
                  class="w-full"
                />
              </div>
            </div>
          </div>

          <!-- 选择框特有配置 -->
          <div v-if="isSelectType" class="mb-4 pb-4 border-b border-gray-200">
            <h4 class="text-md font-medium text-gray-900 mb-2">选项配置</h4>
            <div class="grid grid-cols-1 gap-4">
              <div class="flex items-center uniform-height-input-checkbox">
                <a-checkbox
                  id="searchable"
                  v-model:checked="localParam.searchable"
                >
                  支持搜索
                </a-checkbox>
              </div>
              <div class="flex items-center uniform-height-input-checkbox">
                <a-checkbox
                  id="multiSelect"
                  v-model:checked="localParam.multiSelect"
                >
                  支持多选
                </a-checkbox>
              </div>

              <!-- 选项列表 -->
              <div class="options-list space-y-2">
                <div class="flex justify-between items-center">
                  <h5 class="text-sm font-medium text-gray-700">选项列表</h5>
                  <div class="space-x-2 flex items-center">
                    <a-button
                      type="primary"
                      ghost
                      @click="openEnumSelector"
                    >
                      <template #icon><i class="fas fa-database mr-1"></i></template>
                      使用已有枚举
                    </a-button>
                    <a-button
                      v-if="!localParam.enumId"
                      @click="showCreateEnumModal = true"
                      title="新建枚举"
                    >
                      <template #icon><i class="fas fa-plus mr-1"></i></template>
                      新建枚举
                    </a-button>
                  </div>
                </div>

                <!-- 已有枚举信息展示 -->
                <div v-if="localParam.enumId && localParam.enumName" class="bg-blue-50 rounded-md p-3 mb-2">
                  <div class="flex justify-between items-center">
                    <div>
                      <span class="text-sm font-medium text-blue-700">当前使用枚举: </span>
                      <span class="text-sm text-blue-800">{{ localParam.enumName }}</span>
                      <span class="text-xs text-gray-500 ml-2">({{ localParam.enumCode }})</span>
                    </div>
                    <a-button
                      @click="clearSelectedEnum"
                      type="link"
                      size="small"
                      danger
                    >
                      <template #icon><i class="fas fa-times-circle"></i></template>
                      清除
                    </a-button>
                  </div>
                </div>

                <!-- 枚举选项内容展示 -->
                <div v-if="localParam.enumId && localParam.options && localParam.options.length > 0">
                  <div class="options-content">
                    <div class="options-header flex space-x-2 text-xs text-gray-500 mb-2 px-2">
                      <div class="flex-1">选项值</div>
                      <div class="flex-1">选项标签</div>
                    </div>
                    <div class="options-list-wrapper max-h-60 overflow-y-auto border border-gray-200 rounded-md">
                      <div v-for="(option, index) in localParam.options" :key="index"
                        class="flex space-x-2 p-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 option-row">
                        <input
                          v-model="option.value"
                          class="uniform-height-input flex-1 shadow-sm focus:ring-indigo-500 border-gray-300 rounded-md bg-gray-50"
                          placeholder="选项值"
                          disabled
                        />
                        <input
                          v-model="option.label"
                          class="uniform-height-input flex-1 shadow-sm focus:ring-indigo-500 border-gray-300 rounded-md bg-gray-50"
                          placeholder="选项标签"
                          disabled
                        />
                      </div>
                    </div>
                    <div class="options-footer flex justify-between items-center mt-2 text-xs text-gray-500">
                      <div>共 {{ localParam.options.length }} 个选项</div>
                    </div>
                  </div>
                </div>
                <div v-else class="text-sm text-center py-8 bg-gray-50 rounded-md border border-gray-200 flex flex-col items-center">
                  <i class="fas fa-database text-gray-300 text-2xl mb-2"></i>
                  <p class="text-gray-600">请从已有枚举中选择</p>
                  <p class="text-gray-500 text-xs mt-1">或点击"新建枚举"创建</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 文本输入特有配置 -->
          <div v-if="isTextType" class="mb-4 pb-4 border-b border-gray-200">
            <h4 class="text-md font-medium text-gray-900 mb-2">文本输入配置</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">最小长度</label>
                <input
                  v-model="localParam.minLength"
                  type="number"
                  min="0"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">最大长度</label>
                <input
                  v-model="localParam.maxLength"
                  type="number"
                  min="0"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <div class="flex items-center uniform-height-input-checkbox">
                <input
                  type="checkbox"
                  id="allowMultiple"
                  v-model="localParam.allowMultiple"
                  class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <label for="allowMultiple" class="ml-2 text-sm text-gray-700">支持多选(以逗号分隔)</label>
              </div>
              <div class="flex items-center uniform-height-input-checkbox">
                <input
                  type="checkbox"
                  id="fuzzyMatch"
                  v-model="localParam.fuzzyMatch"
                  class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <label for="fuzzyMatch" class="ml-2 text-sm text-gray-700">支持模糊匹配</label>
              </div>

              <!-- 正则表达式验证配置 -->
              <div class="col-span-2 mt-3">
                <div class="border-t border-gray-200 pt-4">
                  <h5 class="text-sm font-medium text-gray-900 mb-3">正则表达式验证</h5>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">验证表达式</label>
                      <input
                        v-model="localParam.validationRegex"
                        class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="例如: ^[a-zA-Z0-9]+$"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">验证失败提示</label>
                      <input
                        v-model="localParam.validationMessage"
                        class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="请输入验证失败时的提示信息"
                      />
                    </div>
                    <div class="col-span-2">
                      <div class="flex items-center uniform-height-input-checkbox">
                        <button
                          @click="testRegexValidation"
                          class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
                          :disabled="!localParam.validationRegex"
                        >
                          <i class="fas fa-check-circle mr-1"></i> 测试正则表达式
                        </button>
                        <span v-if="regexTestResult !== null" class="ml-3 text-sm" :class="regexTestResult ? 'text-green-600' : 'text-red-600'">
                          {{ regexTestResult ? '正则表达式有效' : '正则表达式无效，请检查语法' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 数字相关输入配置 -->
          <div v-if="isNumberType" class="mb-4 pb-4 border-b border-gray-200">
            <h4 class="text-md font-medium text-gray-900 mb-2">数值配置</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">最小值</label>
                <input
                  v-model="localParam.minValue"
                  type="number"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">最大值</label>
                <input
                  v-model="localParam.maxValue"
                  type="number"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">步长</label>
                <input
                  v-model="localParam.step"
                  type="number"
                  min="0.01"
                  step="0.01"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">小数位数</label>
                <input
                  v-model="localParam.precision"
                  type="number"
                  min="0"
                  max="10"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>

          <!-- 日期类型特有配置 -->
          <div v-if="isDateType" class="mb-4 pb-4 border-b border-gray-200">
            <h4 class="text-md font-medium text-gray-900 mb-2">日期时间配置</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">最大可选跨度(天)</label>
                <input
                  v-model="localParam.maxDateSpan"
                  type="number"
                  min="0"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">日期格式</label>
                <select
                  v-model="localParam.dateFormat"
                  class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option v-for="option in dateFormatOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
              </div>
              <div class="flex items-center uniform-height-input-checkbox">
                <input
                  type="checkbox"
                  id="disablePastDates"
                  v-model="localParam.disablePastDates"
                  class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <label for="disablePastDates" class="ml-2 text-sm text-gray-700">禁用过去日期</label>
              </div>
              <div class="flex items-center uniform-height-input-checkbox">
                <input
                  type="checkbox"
                  id="disableFutureDates"
                  v-model="localParam.disableFutureDates"
                  class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <label for="disableFutureDates" class="ml-2 text-sm text-gray-700">禁用未来日期</label>
              </div>
              <div v-if="localParam.disablePastDates && localParam.disableFutureDates" class="col-span-2 mt-2">
                <div class="text-red-500 text-sm">
                  <i class="fas fa-exclamation-triangle mr-1"></i>
                  警告：同时禁用过去日期和未来日期将导致没有可选日期
                </div>
              </div>

              <!-- 日期时间区间特有配置 -->
              <div v-if="isDateRangeType" class="col-span-2">
                <div class="border-t border-gray-200 pt-4 mt-2">
                  <h5 class="text-sm font-medium text-gray-900 mb-3">区间特有设置</h5>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">默认起始日期偏移(天)</label>
                      <input
                        v-model="localParam.defaultStartOffset"
                        type="number"
                        class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="-7 表示默认7天前"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">默认结束日期偏移(天)</label>
                      <input
                        v-model="localParam.defaultEndOffset"
                        type="number"
                        class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="0 表示今天"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">区间分隔符</label>
                      <select
                        v-model="localParam.rangeSeparator"
                        class="uniform-height-input shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option v-for="option in rangeSeparatorOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="flex items-center justify-center h-40">
          <p class="text-gray-500">加载参数配置中...</p>
        </div>
      </div>

      <!-- 对话框底部按钮 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
        <a-button
          @click="closeModal"
          class="mr-3"
        >
          取消
        </a-button>
        <a-button
          type="primary"
          @click="saveConfig"
          :disabled="!localParam"
        >
          保存
        </a-button>
      </div>
    </div>
  </div>

  <!-- 枚举选择器模态窗口 -->
  <EnumSelector
    v-if="showEnumSelectorModal"
    :project-code="enumProjectCode"
    :is-debug="false"
    @select="handleEnumSelect"
    @edit="handleEditEnum"
    @close="showEnumSelectorModal = false"
  />

  <!-- 枚举创建组件 -->
  <EnumCreator
    v-model:isVisible="showCreateEnumModal"
    :project-code="enumProjectCode"
    :param-name="localParam?.name"
    @save="handleEnumCreate"
    @close="showCreateEnumModal = false"
  />

  <!-- 枚举编辑组件 -->
  <EnumCreator
    v-model:isVisible="showEditEnumModal"
    :project-code="enumProjectCode"
    :enum-data="editingEnum"
    @save="handleEnumCreate"
    @close="showEditEnumModal = false"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { watch, nextTick } from '@/plugins/vue-types-fix';
import type { QueryParam } from '@/types/unified-integration';
// 使用活跃的消息通知组件
import { message } from '@/services/message';
import { enumServiceConfig } from '@/utils/config';
import draggable from 'vuedraggable';
import EnumSelector from './enum/EnumSelector.vue';
import EnumCreator from './enum/EnumCreator.vue';
import { convertOptionsToEnumeration, createEnumeration, updateEnumeration } from '@/api/enumerationService';

const props = defineProps<{
  modelValue: QueryParam | null;
  isVisible: boolean;
  projectCode: string;
}>();

const emit = defineEmits(['update:isVisible', 'update:modelValue', 'close', 'save']);

// 本地参数状态，用于编辑
const localParam = ref<QueryParam | null>(null);
// 正则表达式测试结果
const regexTestResult = ref<boolean | null>(null);

// 保存枚举相关状态
const showSaveEnumModal = ref(false);
const showEnumSelectorModal = ref(false);
const showEditEnumModal = ref(false);
const editingEnum = ref<any>(null);
const enumName = ref('');
const enumCode = ref('');
const enumProjectCode = ref(enumServiceConfig.projectCode);
const isSavingEnum = ref(false);
const enumNameError = ref('');
const enumCodeError = ref('');
// 使用活跃的消息服务，不需要初始化messageStore

// 在variables区域附近添加这个变量
const showCreateEnumModal = ref(false);

// 复制参数以进行编辑
watch(
  () => props.isVisible,
  (newVisible: boolean) => {
    if (newVisible && props.modelValue) {
      // 深拷贝参数对象以便于编辑
      localParam.value = JSON.parse(JSON.stringify(props.modelValue));
      localParam.value.fuzzyMatch = localParam.value?.exportConfig?.config?.isFuzzyMatch;

      // 确保options数组存在
      if (localParam.value && !localParam.value.options) {
        localParam.value.options = [];
      }

      // 设置日期区间默认值
      if (localParam.value && (localParam.value.formType === 'date-range' || localParam.value.formType === 'date')) {
        // 如果日期格式未设置，设为默认值
        if (!localParam.value.dateFormat) {
          localParam.value.dateFormat = 'YYYY-MM-DD';
        }

        if (localParam.value.formType === 'date-range') {
          // 如果区间分隔符未设置，设为默认值
          if (!localParam.value.rangeSeparator) {
            localParam.value.rangeSeparator = '至';
          }
        }
      }

      // 重置正则测试结果
      regexTestResult.value = null;
    } else if (!newVisible) {
      // 关闭模态框时清空本地状态
      localParam.value = null;
      regexTestResult.value = null;
    }
  },
  { immediate: true } // 立即执行一次
);

// 确保首次打开时也会加载参数
onMounted(() => {
  if (props.isVisible && props.modelValue) {
    localParam.value = JSON.parse(JSON.stringify(props.modelValue));

    // 确保options数组存在
    if (localParam.value && !localParam.value.options) {
      localParam.value.options = [];
    }

    // 设置日期区间默认值
    if (localParam.value && (localParam.value.formType === 'date-range' || localParam.value.formType === 'date')) {
      // 如果日期格式未设置，设为默认值
      if (!localParam.value.dateFormat) {
        localParam.value.dateFormat = 'YYYY-MM-DD';
      }

      if (localParam.value.formType === 'date-range') {
        // 如果默认起始日期偏移未设置，设为-7（7天前）
        if (localParam.value.defaultStartOffset === undefined) {
          localParam.value.defaultStartOffset = -7;
        }

        // 如果默认结束日期偏移未设置，设为0（今天）
        if (localParam.value.defaultEndOffset === undefined) {
          localParam.value.defaultEndOffset = 0;
        }

        // 如果区间分隔符未设置，设为默认值
        if (!localParam.value.rangeSeparator) {
          localParam.value.rangeSeparator = '至';
        }
      }
    }
  }
});

// 创建新的空参数对象的函数
const createEmptyParam = (): QueryParam => {
  return {
    name: '',
    type: 'string',
    description: '',
    required: false,
    formType: 'text',
    format: 'string',
    displayOrder: 0,
    options: [],
    // 日期区间默认值
    defaultStartOffset: -7,  // 默认7天前
    defaultEndOffset: 0,     // 默认今天
    rangeSeparator: '至',    // 默认分隔符
    dateFormat: 'YYYY-MM-DD' // 默认日期格式
  };
};

// 清除选择的枚举
const clearSelectedEnum = () => {
  if (localParam.value) {
    const enumName = localParam.value.enumName || '当前枚举';

    localParam.value.enumId = undefined;
    localParam.value.enumName = undefined;
    localParam.value.enumCode = undefined;
    localParam.value.options = [];

    // 使用更丰富的全局消息提示
    message.info(`已断开与枚举 "${enumName}" 的关联，您可以选择或创建其他枚举`);
  }
};

// 表单类型选项
const formTypeOptions = [
  { label: '文本框', value: 'text' },
  { label: '数字框', value: 'number' },
  { label: '下拉选择', value: 'select' },
  { label: '日期选择', value: 'date' },
  { label: '日期范围', value: 'date-range' }
];

// 通用日期格式选项
const dateFormatOptions = [
  { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
  { label: 'YYYY/MM/DD', value: 'YYYY/MM/DD' },
  { label: 'DD-MM-YYYY', value: 'DD-MM-YYYY' },
  { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
  { label: 'MM-DD-YYYY', value: 'MM-DD-YYYY' },
  { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' }
];

// 范围分隔符选项
const rangeSeparatorOptions = [
  { label: '至', value: '至' },
  { label: 'to', value: 'to' },
  { label: '-', value: '-' },
  { label: '~', value: '~' }
];

// 计算属性：参数名称
const paramName = computed(() => {
  return localParam.value?.name || '参数';
});

// 检查是否为日期类型
const isDateType = computed(() => {
  return localParam.value?.formType === 'date' || localParam.value?.formType === 'date-range';
});

// 检查是否为日期范围类型
const isDateRangeType = computed(() => {
  return localParam.value?.formType === 'date-range';
});

// 检查是否为选择类型
const isSelectType = computed(() => {
  return localParam.value?.formType === 'select';
});

// 检查是否为文本类型
const isTextType = computed(() => {
  return localParam.value?.formType === 'text';
});

// 检查是否为数字类型
const isNumberType = computed(() => {
  return localParam.value?.formType === 'number';
});

// 测试正则表达式有效性
const testRegexValidation = () => {
  if (!localParam.value || !localParam.value.validationRegex) {
    regexTestResult.value = false;
    return;
  }

  try {
    // 尝试创建正则表达式对象，检查语法是否正确
    new RegExp(localParam.value.validationRegex);
    regexTestResult.value = true;
  } catch (e) {
    regexTestResult.value = false;
  }
};

// 监听正则表达式变化
watch(() => localParam.value?.validationRegex, () => {
  // 当正则表达式变化时重置测试结果
  regexTestResult.value = null;
});

// 监听日期限制选项变化
watch([
  () => localParam.value?.disablePastDates,
  () => localParam.value?.disableFutureDates
], () => {
  // 当两个选项都被选中时，在控制台输出警告
  if (localParam.value?.disablePastDates && localParam.value?.disableFutureDates) {
    console.warn('警告：同时禁用过去日期和未来日期将导致没有可选日期');
  }
});

// 添加日期范围快捷选项
const addRangePreset = () => {
  if (!localParam.value) return;

  if (!localParam.value.rangePresets) {
    localParam.value.rangePresets = [];
  }

  // 添加一个默认的快捷选项
  localParam.value.rangePresets.push({
    label: '最近7天',
    startOffset: -7,
    endOffset: 0
  });

  // 重新赋值触发更新
  localParam.value.rangePresets = [...localParam.value.rangePresets];
};

// 打开枚举选择器的方法
const openEnumSelector = () => {
  // 设置当前项目代码，确保枚举选择器加载正确的枚举列表
  enumProjectCode.value = props.projectCode || enumServiceConfig.projectCode;

  // 显示枚举选择器模态窗口
  showEnumSelectorModal.value = true;
};

// 处理枚举选择事件
const handleEnumSelect = (result: {options: Array<{label: string, value: string}>, enumId: string, enumName: string, enumCode: string}) => {
  if (!localParam.value) return;

  try {
    // 更新本地参数的选项
    localParam.value.options = result.options;

    // 更新枚举相关信息
    localParam.value.enumId = result.enumId;
    localParam.value.enumName = result.enumName;
    localParam.value.enumCode = result.enumCode;

    // 提示用户 - 使用全局消息通知
    message.success(`已成功选择枚举 "${result.enumName}"，已加载 ${result.options.length} 个选项`);

    // 关闭枚举选择器
    showEnumSelectorModal.value = false;

    // 应用配置
    applyToParam();
  } catch (error) {
    console.error('选择枚举时发生错误:', error);
    message.error(`选择枚举失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 重置枚举表单
const resetEnumForm = () => {
  enumName.value = '';
  enumCode.value = '';
  enumNameError.value = '';
  enumCodeError.value = '';
};

// 生成枚举默认值
const generateEnumDefaults = () => {
  try {
    // 如果有现有的枚举ID，保留当前的枚举名称和代码
    if (localParam.value?.enumId && localParam.value?.enumName && localParam.value?.enumCode) {
      enumName.value = localParam.value.enumName;
      enumCode.value = localParam.value.enumCode;
      return;
    }

    // 否则生成默认值
    if (localParam.value?.name) {
      enumName.value = localParam.value.name + '枚举';
      // 生成代码
      enumCode.value = localParam.value.name
        .replace(/\s+/g, '_')
        .replace(/[^\w\s]/g, '')
        .toUpperCase() + '_ENUM';
    } else {
      enumName.value = '';
      enumCode.value = '';
    }
    // 清除错误
    enumNameError.value = '';
    enumCodeError.value = '';
  } catch (e) {
    console.error('生成枚举默认值错误:', e);
  }
};

// 验证枚举名称
const validateEnumName = () => {
  if (!enumName.value) {
    enumNameError.value = '枚举名称不能为空';
    return false;
  }
  if (enumName.value.length > 50) {
    enumNameError.value = '枚举名称不能超过50个字符';
    return false;
  }
  enumNameError.value = '';
  return true;
};

// 验证枚举代码
const validateEnumCode = () => {
  if (!enumCode.value) {
    enumCodeError.value = '枚举代码不能为空';
    return false;
  }
  if (enumCode.value.length > 50) {
    enumCodeError.value = '枚举代码不能超过50个字符';
    return false;
  }

  // 枚举代码只能包含字母、数字和下划线，且不能以数字开头
  const codeRegex = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
  if (!codeRegex.test(enumCode.value)) {
    enumCodeError.value = '枚举代码只能包含字母、数字和下划线，且不能以数字开头';
    return false;
  }

  enumCodeError.value = '';
  return true;
};

// 保存枚举
const saveEnum = async () => {
  try {
    // 标记为正在保存
    isSavingEnum.value = true;

    // 首先验证表单
    const nameValid = validateEnumName();
    const codeValid = validateEnumCode();

    if (!nameValid || !codeValid) {
      isSavingEnum.value = false;
      return; // 有验证错误，不继续处理
    }

    // 验证选项列表不为空
    if (!localParam.value?.options || localParam.value.options.length === 0) {
      message.error('选项列表不能为空，请添加至少一个选项以创建有效的枚举');
      isSavingEnum.value = false;
      return;
    }

    // 准备保存数据
    const options = localParam.value.options.map(opt => ({
      label: opt.label || opt.value,
      value: opt.value
    }));

    // 设置项目代码
    enumProjectCode.value = props.projectCode || enumServiceConfig.projectCode;

    // 将选项转换为枚举格式
    const enumOptionsData = convertOptionsToEnumeration(
      localParam.value.options,
      enumName.value,
      enumCode.value,
      enumProjectCode.value
    );

    let response;

    try {
      // 根据是否有enumId判断是更新还是创建
      if (localParam.value.enumId) {
        // 更新已有枚举
        const updateData = {
          ...enumOptionsData,
          id: localParam.value.enumId
        };

        response = await updateEnumeration(updateData);

        // 检查响应是否成功
        if (response && (response.code === '000000' || response.success)) {
          message.success(`枚举 "${enumName.value}" 更新成功，包含 ${localParam.value.options.length} 个选项`);
        } else {
          // 处理更新失败情况
          const errorMsg = response?.message || '更新枚举失败，请检查枚举编码是否已存在';
          message.error(`更新枚举失败: ${errorMsg}`);
          return; // 返回避免继续执行
        }
      } else {
        // 创建新枚举
        response = await createEnumeration(enumOptionsData);

        // 检查响应是否成功
        if (response && (response.code === '000000' || response.success)) {
          message.success(`枚举 "${enumName.value}" 创建成功，包含 ${localParam.value.options.length} 个选项`);

          // 保存枚举ID到参数
          if (response && response.id) {
            localParam.value.enumId = response.id;
          }
        } else {
          // 处理创建失败情况，特别是编码已存在的情况
          if (response?.code === '230409') {
            message.error(`编码 "${enumCode.value}" 已存在，请使用其他编码`);
          } else {
            const errorMsg = response?.message || '创建枚举失败，请稍后重试';
            message.error(`创建枚举失败: ${errorMsg}`);
          }
          return; // 返回避免继续执行
        }
      }
    } catch (error) {
      console.error('枚举操作失败:', error);
      message.error(`枚举操作失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return; // 返回避免继续执行
    }

    // 更新本地参数
    if (localParam.value) {
      localParam.value.enumName = enumName.value;
      localParam.value.enumCode = enumCode.value;

      // 应用配置
      applyToParam();
    }

    // 关闭保存枚举弹窗
    closeSaveEnumModal();
    isSavingEnum.value = false;

  } catch (error: any) {
    console.error('保存枚举错误:', error);

    // 处理特定的错误类型
    if (error.response && error.response.data) {
      // 处理错误响应
      const errorMsg = error.response.data.message || error.response.data.error;

      // 检查是否是枚举代码重复的错误
      if (errorMsg && errorMsg.includes('已存在')) {
        message.error(`枚举代码 "${enumCode.value}" 已被使用，请尝试其他代码或使用现有枚举`);
      } else {
        message.error(errorMsg || '保存枚举时发生错误，请检查枚举数据格式是否正确');
      }
    } else {
      message.error(error.message || '保存枚举时发生未知错误，请稍后重试');
    }

    isSavingEnum.value = false;
  }
};

// 打开保存枚举弹窗
const openSaveEnumModal = () => {
  generateEnumDefaults();
  showSaveEnumModal.value = true;
};

// 关闭保存枚举弹窗
const closeSaveEnumModal = () => {
  showSaveEnumModal.value = false;
  resetEnumForm();
};

// 应用枚举配置到参数
const applyToParam = () => {
  if (!localParam.value) return;

  // 更新本地参数的导出配置
  if (localParam.value.enumCode) {
    const exportConfig = {
      key: localParam.value.name,
      label: localParam.value.label || localParam.value.name,
      fieldType: 'string',
      dataFormat: 'string',
      displayType: 'select',
      config: {
        required: localParam.value.required || false,
        isMultiValue: localParam.value.multiSelect || false,
        enumKey: localParam.value.enumCode
      }
    };

    // 添加日志输出
    console.log('应用枚举配置:', JSON.stringify(exportConfig, null, 2));
    console.log('使用真实枚举代码:', localParam.value.enumCode);

    localParam.value.exportConfig = exportConfig;
  }
};

// 处理新创建的枚举
const handleEnumCreate = (result: {
  enumId: string;
  enumName: string;
  enumCode: string;
  options: Array<{label: string, value: string}>;
}) => {
  if (!localParam.value) return;

  try {
    // 更新本地参数的选项和枚举信息
    localParam.value.options = result.options;
    localParam.value.enumId = result.enumId;
    localParam.value.enumName = result.enumName;
    localParam.value.enumCode = result.enumCode;

    // 注意：EnumCreator.vue 组件已经显示了成功消息，这里不再重复显示
    // message.success(`已创建枚举 "${result.enumName}" 并启用 ${result.options.length} 个选项`);

    // 应用配置
    applyToParam();
  } catch (error) {
    console.error('处理枚举创建结果失败:', error);
    message.error(`应用枚举失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 关闭模态框
const closeModal = () => {
  emit('update:isVisible', false);
  emit('close');
};

// 保存配置
const saveConfig = () => {
  try {
    if (localParam.value) {
      // 确保选项列表的每个选项都有value和label
      if (localParam.value.options) {
        // 过滤掉空选项
        const validOptions = localParam.value.options.filter(option =>
          option.value?.trim() !== '' && option.label?.trim() !== ''
        );

        // 更新选项列表
        localParam.value.options = validOptions;
      }

      // 构建导出配置
      const exportConfig = {
        key: localParam.value.name,
        label: localParam.value.label || localParam.value.name,
        fieldType: 'string',
        dataFormat: getDataFormat(localParam.value),
        displayType: getDisplayType(localParam.value),
        config: buildConfigObject(localParam.value)
      };

      // 添加日志输出
      console.log('保存前的最终配置:', JSON.stringify(exportConfig, null, 2));
      console.log('选择器类型是否使用了ID_ENUM2:',
                 localParam.value.formType === 'select' ?
                 exportConfig.config.enumKey === 'ID_ENUM2' : 'N/A');

      // 更新本地参数
      localParam.value.exportConfig = exportConfig;

      // 先发出update:modelValue事件
      emit('update:modelValue', localParam.value);

      // 然后发出save事件
      emit('save', localParam.value);

      // 使用更丰富的全局提示样式
      message.success(`参数"${localParam.value.name || '当前参数'}"的配置已成功保存`, 3);

      // 最后关闭模态框
      closeModal();
    } else {
      // 统一错误提示风格
      message.error({
      content: '参数配置数据无效',
      description: '请检查输入并确保所有必填字段已正确填写',
      duration: 5000
    });
    }
  } catch (error: any) {
    console.error('保存参数配置错误:', error);

    // 使用更丰富的错误提示
    let errorMessage = '保存参数配置失败，请稍后重试';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    message.error(errorMessage, 5);
  }
};

// 获取数据格式
const getDataFormat = (param: QueryParam) => {
  switch (param.format) {
    case 'int':
    case 'integer':
      return 'int';
    case 'decimal':
    case 'float':
    case 'double':
      return 'decimal';
    case 'date':
    case 'date-time':
      return 'date';
    case 'enum':
      return 'string';
    default:
      return 'string';
  }
};

// 获取显示类型
const getDisplayType = (param: QueryParam) => {
  switch (param.formType) {
    case 'text':
      return 'input';
    case 'number':
      return 'number-input';
    case 'select':
      return 'select';
    case 'date':
      return 'date-picker';
    case 'date-range':
      return 'date-range-picker';
    default:
      return 'input';
  }
};

// 构建配置对象
const buildConfigObject = (param: QueryParam) => {
  const config: Record<string, any> = {
    required: param.required || false
  };

  // 选择框配置
  if (param.formType === 'select') {
    config.isMultiValue = param.multiSelect || false;
    // 使用枚举编码作为 enumKey
    if (param.enumCode) {
      config.enumKey = param.enumCode;
    }
  }

  // 文本类型配置
  if (param.formType === 'text' || param.formType === 'textarea') {
    if (param.minLength) config.minLength = param.minLength;
    if (param.maxLength) config.maxLength = param.maxLength;
    if (param.validationRegex) config.pattern = param.validationRegex;

    // 添加模糊匹配和多选支持
    if (param.fuzzyMatch !== undefined) {
      config.isFuzzyMatch = !!param.fuzzyMatch;
      console.log(`[ParamAdvancedConfig] 文本框设置模糊匹配: ${param.name}, fuzzyMatch=${param.fuzzyMatch}, config.isFuzzyMatch=${config.isFuzzyMatch}`);
    }
    if (param.allowMultiple !== undefined) {
      config.isMultiValue = !!param.allowMultiple;
      console.log(`[ParamAdvancedConfig] 文本框设置多选支持: ${param.name}, allowMultiple=${param.allowMultiple}, config.isMultiValue=${config.isMultiValue}`);
    }
  }

  // 数字类型配置
  if (param.formType === 'number') {
    if (param.minValue !== undefined) config.min = param.minValue;
    if (param.maxValue !== undefined) config.max = param.maxValue;
  }

  // 日期区间配置
  if (param.formType === 'date-range') {
    if (param.maxDateSpan) config.maxDaysRange = String(param.maxDateSpan);
    if (param.disablePastDates) config.disablePastDates = true;
    if (param.disableFutureDates) config.disableFutureDates = true;
    if (param.defaultStartOffset !== undefined) config.defaultStartOffset = param.defaultStartOffset;
    if (param.defaultEndOffset !== undefined) config.defaultEndOffset = param.defaultEndOffset;
    if (param.rangeSeparator) config.rangeSeparator = param.rangeSeparator;
    if (param.rangePresets && param.rangePresets.length > 0) config.rangePresets = param.rangePresets;
    if (param.dateFormat) config.dateFormat = param.dateFormat;
  }

  // 添加日期类型通用配置（包括单个日期选择器和日期范围）
  if (param.formType === 'date' || param.formType === 'date-range') {
    if (param.dateFormat && !config.dateFormat) config.dateFormat = param.dateFormat;
  }

  // 添加日志输出，调试配置对象
  console.log('生成的配置对象:', JSON.stringify(config, null, 2));

  return config;
};

// 处理编辑枚举
const handleEditEnum = (enumeration: any) => {
  editingEnum.value = enumeration;
  showEditEnumModal.value = true;
};
</script>

<style scoped>
/* 使用Ant Design Vue样式变量 */
:deep(.ant-input),
:deep(.ant-select),
:deep(.ant-checkbox),
:deep(.ant-radio),
:deep(.ant-button) {
  margin-bottom: 0;
}

/* 保留部分原有自定义样式 */
.uniform-height-input-checkbox {
  height: 38px;
  min-height: 38px;
  display: flex;
  align-items: center;
}

/* 统一所有输入控件的高度和样式 */
input.uniform-height-input,
select.uniform-height-input,
.uniform-height-input {
  height: 38px;
  min-height: 38px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  line-height: 1.25;
}

/* 确保下拉菜单在所有浏览器中具有一致的外观 */
select.uniform-height-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Ant Design Vue组件样式覆盖 */
.param-advanced-config .ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

.param-advanced-config .ant-input,
.param-advanced-config .ant-select,
.param-advanced-config .ant-textarea,
.param-advanced-config .ant-input-number,
.param-advanced-config .ant-picker,
.param-advanced-config .ant-switch,
.param-advanced-config .ant-checkbox-wrapper {
  width: 100%;
}

/* 确保Ant Design Vue下拉菜单层级正确 */
:deep(.ant-select-dropdown) {
  z-index: var(--z-notification) !important; /* 使用与notification相同的层级 */
}

/* 在模态框中的Ant Design Vue下拉菜单 */
.modal :deep(.ant-select-dropdown),
.dialog :deep(.ant-select-dropdown) {
  z-index: var(--z-modal-dropdown) !important;
}

.options-list {
  margin-top: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 0.375rem;
  padding: 10px;
  background-color: #f9fafb;
}

.options-list .option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.options-list .option-item:last-child {
  margin-bottom: 0;
}

.options-list .option-item .ant-input {
  margin-right: 10px;
  flex: 1;
}

.options-list .add-option-btn,
.options-list .remove-option-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  min-width: 38px;
  padding: 0 10px;
  border-radius: 0.375rem;
  font-size: 14px;
  transition: background-color 0.2s;
}

.options-list .add-option-btn {
  background-color: #e0f2fe;
  color: #0284c7;
  border: 1px solid #bae6fd;
}

.options-list .add-option-btn:hover {
  background-color: #bae6fd;
}

.options-list .remove-option-btn {
  background-color: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
}

.options-list .remove-option-btn:hover {
  background-color: #fecaca;
}

/* 表单底部按钮区 */
.form-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  margin-top: 20px;
}

.form-footer button {
  margin-left: 10px;
}

/* 折叠面板样式 */
.param-advanced-config .ant-collapse {
  border: none;
}

.param-advanced-config .ant-collapse-header {
  background-color: #f9fafb;
  padding: 12px 16px;
  font-weight: 600;
  color: #374151;
  border-radius: 0.375rem;
  margin-bottom: 10px;
}

.param-advanced-config .ant-collapse-content {
  border: none;
}

.param-advanced-config .ant-collapse-content-box {
  padding: 0 16px 16px;
}

.use-enum-btn,
.save-enum-btn {
  display: inline-flex;
  align-items: center;
  padding: 0 10px;
  height: 38px;
  border-radius: 0.375rem;
  font-size: 14px;
  transition: background-color 0.2s;
}

.use-enum-btn {
  background-color: #e0f2fe;
  color: #0284c7;
  border: 1px solid #bae6fd;
}

.use-enum-btn:hover {
  background-color: #bae6fd;
}

.save-enum-btn {
  background-color: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
}

.save-enum-btn:hover {
  background-color: #fecaca;
}

.save-enum-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
