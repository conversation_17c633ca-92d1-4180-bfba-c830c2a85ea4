<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-20">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
      <!-- 弹窗标题 -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">编辑操作按钮</h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-500 focus:outline-none">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="px-6 py-4 flex-grow overflow-y-auto">
        <div class="space-y-4">
          <!-- 图标配置 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">图标类名</label>
            <input
              v-model="action.icon"
              class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              placeholder="例如: fas fa-edit"
            />
          </div>

          <!-- 配置 -->
          <div class="border-t border-gray-200 pt-4">
            <h4 class="text-md font-medium text-gray-900 mb-3">显示与权限配置</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">显示条件</label>
                <select
                  v-model="action.showCondition"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="always">始终显示</option>
                  <option value="expression">条件表达式</option>
                  <option value="permission">权限控制</option>
                </select>
              </div>

              <div v-if="action.showCondition === 'expression'">
                <label class="block text-sm font-medium text-gray-700 mb-1">表达式</label>
                <input
                  v-model="action.showExpression"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="例如: row.status === 'ACTIVE'"
                />
              </div>

              <div v-if="action.showCondition === 'permission'">
                <label class="block text-sm font-medium text-gray-700 mb-1">所需权限</label>
                <input
                  v-model="action.requiredPermission"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="例如: EDIT_ITEM"
                />
              </div>
            </div>
          </div>

          <!-- 样式配置 -->
          <div class="border-t border-gray-200 pt-4">
            <h4 class="text-md font-medium text-gray-900 mb-3">样式配置</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">按钮类型</label>
                <select
                  v-model="action.type"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="button">按钮</option>
                  <option value="link">链接</option>
                  <option value="menu">菜单项</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">按钮样式</label>
                <select
                  v-model="action.style"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="primary">主要按钮</option>
                  <option value="secondary">次要按钮</option>
                  <option value="info">信息按钮</option>
                  <option value="warning">警告按钮</option>
                  <option value="danger">危险按钮</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">按钮大小</label>
                <select
                  v-model="action.size"
                  class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="default">默认大小</option>
                  <option value="small">小型按钮</option>
                  <option value="mini">迷你按钮</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end">
        <button
          @click="$emit('close')"
          class="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3 focus:outline-none"
        >
          取消
        </button>
        <button
          @click="$emit('save', action)"
          class="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
        >
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { TableAction } from '@/types/integration';

// 接收props
const props = defineProps<{
  modelValue: TableAction;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', action: TableAction): void;
  (e: 'save', action: TableAction): void;
  (e: 'close'): void;
}>();

// 内部编辑状态
const action = ref<TableAction>(JSON.parse(JSON.stringify(props.modelValue)));

// 确保编辑窗口中的action初始化时有默认值
if (!action.value.type) action.value.type = 'button';
if (!action.value.showCondition) action.value.showCondition = 'always';
if (!action.value.size) action.value.size = 'default';
if (!action.value.icon) action.value.icon = 'fas fa-cog';
if (action.value.confirm && !action.value.confirmText) {
  action.value.confirmText = '确定要执行此操作吗？';
}
</script>

<style scoped>
/* 弹窗样式微调 */
.fixed {
  z-index: 100;
}

.fixed > div {
  position: relative;
  z-index: 101;
}

/* 确保下拉菜单在弹窗内部正确显示 */
.fixed select {
  position: relative;
  z-index: 110;
}

.fixed select:focus {
  z-index: 120;
}
</style>
