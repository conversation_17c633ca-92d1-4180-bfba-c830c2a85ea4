<template>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">排序</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作名称</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作事件</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需二次确认</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">显示条件</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用条件</th>
          <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <draggable 
        :model-value="actions" 
        @update:model-value="updateActions"
        tag="tbody"
        handle=".drag-handle"
        :animation="150"
        item-key="label"
        class="bg-white divide-y divide-gray-200"
        @change="onDragChange"
      >
        <template #item="{element: action, index}">
          <tr class="hover:bg-gray-50">
            <!-- 拖拽手柄 -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 flex justify-center items-center w-full h-full">
                <i class="fas fa-grip-vertical"></i>
              </div>
            </td>
            
            <!-- 操作名称 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <input 
                v-model="action.label" 
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="操作名称"
              />
            </td>
            
            <!-- 操作事件 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <input 
                v-model="action.handler" 
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="处理函数名"
              />
            </td>
            
            <!-- 需二次确认 -->
            <td class="px-4 py-2 whitespace-nowrap text-left">
              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  v-model="action.confirm" 
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  :id="'table-confirm-checkbox-' + index"
                />
                <label :for="'table-confirm-checkbox-' + index" class="ml-2 text-sm text-gray-700">
                  {{ action.confirm ? '是' : '否' }}
                </label>
              </div>
            </td>
            
            <!-- 显示条件 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <select 
                v-model="action.showCondition"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              >
                <option value="always">始终显示</option>
                <option value="expression">条件表达式</option>
                <option value="permission">权限控制</option>
              </select>
            </td>
            
            <!-- 启用条件 -->
            <td class="px-4 py-2 whitespace-nowrap">
              <select 
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              >
                <option value="always">始终启用</option>
                <option value="expression">条件表达式</option>
              </select>
            </td>
            
            <!-- 操作 -->
            <td class="px-4 py-2 whitespace-nowrap text-left text-sm font-medium">
              <button 
                @click="$emit('edit', action)"
                class="text-indigo-600 hover:text-indigo-900 mr-2"
              >
                <i class="fas fa-cog mr-1"></i>更多设置
              </button>
              <button 
                @click="removeAction(index)" 
                class="text-red-600 hover:text-red-900"
              >
                <i class="fas fa-trash-alt mr-1"></i>删除
              </button>
            </td>
          </tr>
        </template>
      </draggable>
      <tfoot>
        <tr>
          <td colspan="7" class="px-4 py-3">
            <button 
              @click="addAction" 
              class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none"
            >
              <i class="fas fa-plus mr-1"></i> 添加操作按钮
            </button>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { TableAction } from '@/types/integration';
import { message } from '@/services/message';
import draggable from 'vuedraggable';

// 接收props
const props = defineProps<{
  actions: TableAction[];
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update:actions', actions: TableAction[]): void;
  (e: 'edit', action: TableAction): void;
}>();

// 添加操作按钮
const addAction = () => {
  const newAction: TableAction = {
    type: 'button',
    label: '新操作',
    handler: '',
    icon: 'fas fa-cog',
    confirm: false,
    showCondition: 'always',
    displayOrder: props.actions.length
  };
  
  emit('update:actions', [...props.actions, newAction]);
  message.success('已添加新操作按钮', 3000);
};

// 移除操作按钮
const removeAction = (index: number) => {
  const updatedActions = [...props.actions];
  updatedActions.splice(index, 1);
  emit('update:actions', updatedActions);
  message.success('已删除操作按钮', 3000);
};

// 更新操作数据
const updateActions = (value: TableAction[]) => {
  emit('update:actions', value);
};

// 处理拖拽变化
const onDragChange = (evt: any) => {
  // 更新排序顺序
  const updatedActions = props.actions.map((action, index) => ({
    ...action,
    displayOrder: index
  }));
  emit('update:actions', updatedActions);
  message.success(`操作按钮显示顺序已更新: 共 ${updatedActions.length} 个操作的显示顺序已更新`, 3000);
};
</script>

<style scoped>
.overflow-x-auto {
  position: relative;
  max-width: 100%;
  overflow-x: auto;
}

/* 表格配置相关样式 */
table {
  border-collapse: collapse;
  width: 100%;
}

select, input {
  min-height: 38px;
  line-height: 1.5;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
}

/* 确保表格有适当高度 */
tbody tr {
  height: 50px;
}

thead th {
  height: 40px;
  vertical-align: middle;
}

/* 改善表格头部可见度 */
.bg-gray-50 {
  background-color: #f9fafb;
}

/* 拖拽排序相关样式 */
.sortable-ghost {
  opacity: 0.5;
  background-color: var(--ghost-bg, #f1f5f9);
}

.sortable-drag {
  opacity: 0.9;
  background-color: var(--drag-bg, #ffffff);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.drag-handle {
  cursor: move;
  display: inline-flex;
  align-items: center;
  color: var(--handle-color, #9ca3af);
}

.drag-handle:hover {
  color: var(--handle-hover-color, #6b7280);
}
</style>