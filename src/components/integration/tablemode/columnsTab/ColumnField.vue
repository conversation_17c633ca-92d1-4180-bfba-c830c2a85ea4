<template>
  <div class="relative">
    <div 
      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md cursor-pointer border border-gray-300 field-selector"
      :class="{
        'bg-gray-100': !column.isNewColumn, 
        'cursor-not-allowed': !column.isNewColumn, 
        'cursor-pointer': column.isNewColumn,
        'active': isActive
      }"
      @click.stop="toggleFieldDropdown"
    >
      <div class="flex items-center p-2">
        <span class="flex-1 truncate">{{ column.field || '请选择字段' }}</span>
      </div>
    </div>
    
    <div 
      v-if="isActive && column.isNewColumn" 
      class="absolute left-0 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50"
      @click.stop
    >
      <div class="p-2 border-b">
        <input 
          v-model="searchText" 
          class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 field-search-input" 
          placeholder="搜索字段..."
          @click.stop
          @keydown.esc="closeDropdown"
          ref="searchInput"
        />
      </div>
      <div class="max-h-60 overflow-y-auto p-0">
        <div 
          v-for="field in filteredFields" 
          :key="field" 
          class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
          @click.stop="selectField(field); $event.preventDefault();"
          @mousedown.prevent
        >
          {{ field }}
        </div>
        <div 
          v-if="filteredFields.length === 0" 
          class="px-3 py-2 text-gray-500 text-sm"
        >
          无匹配字段
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';
import type { TableColumn } from '@/types/integration';

// 接收props
const props = defineProps<{
  column: TableColumn;
  index: number;
  availableFields: string[];
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'field-selected', column: TableColumn, field: string): void;
}>();

// 内部状态
const isActive = ref(false);
const searchText = ref('');
const searchInput = ref<HTMLInputElement | null>(null);

// 过滤字段列表
const filteredFields = computed(() => {
  if (!searchText.value) {
    return props.availableFields;
  }
  
  const query = searchText.value.toLowerCase();
  return props.availableFields.filter((field) => 
    field.toLowerCase().includes(query)
  );
});

// 切换下拉菜单
const toggleFieldDropdown = () => {
  if (!props.column.isNewColumn) {
    return;
  }
  
  // 切换状态
  isActive.value = !isActive.value;
  
  // 如果打开下拉菜单，聚焦搜索框
  if (isActive.value) {
    addHighlightClass();
    // 下一个tick聚焦
    nextTick(() => {
      if (searchInput.value) {
        searchInput.value.focus();
      }
    });
  } else {
    removeHighlightClass();
  }
};

// 关闭下拉菜单
const closeDropdown = () => {
  isActive.value = false;
  searchText.value = '';
  removeHighlightClass();
};

// 添加高亮样式
const addHighlightClass = () => {
  // 找到当前元素的父级td元素
  const parentTd = findParentTd();
  if (parentTd) {
    parentTd.classList.add('field-dropdown-active');
  }
};

// 移除高亮样式
const removeHighlightClass = () => {
  document.querySelectorAll('.field-dropdown-active').forEach(el => {
    el.classList.remove('field-dropdown-active');
  });
};

// 查找父级td元素
const findParentTd = (): HTMLElement | null => {
  let element = document.querySelector(`.field-selector.active`)?.parentElement;
  
  while (element && element.tagName !== 'TD') {
    element = element.parentElement;
  }
  
  return element as HTMLElement;
};

// 选择字段
const selectField = (field: string) => {
  emit('field-selected', props.column, field);
  closeDropdown();
};

// 点击事件监听
const handleDocumentClick = (event: MouseEvent) => {
  // 检查点击事件是否发生在下拉菜单内部
  const target = event.target as HTMLElement;
  const isSelector = target.closest('.field-selector') !== null;
  const isDropdown = target.closest('.absolute') !== null && !isSelector;
  
  // 如果点击在下拉菜单和选择器之外，关闭下拉菜单
  if (!isSelector && !isDropdown && isActive.value) {
    closeDropdown();
  }
};

// ESC键监听
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isActive.value) {
    closeDropdown();
  }
};

// 添加和移除事件监听器
onMounted(() => {
  document.addEventListener('click', handleDocumentClick);
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick);
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.field-selector {
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 10;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
}

.field-selector.active {
  z-index: 1000;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* 确保下拉菜单显示在最上层 */
.relative {
  position: relative;
  z-index: 10;
}

.relative:focus-within {
  z-index: 1000;
}

/* 下拉菜单内容 */
.absolute {
  z-index: 1001;
  position: absolute;
  width: 100%;
  left: 0;
  top: 100%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.max-h-60 {
  max-height: 240px;
}

/* 搜索框 */
.field-search-input {
  z-index: 1003;
  position: relative;
}
</style>