<template>
  <div class="column-edit-form">
    <a-form
      layout="vertical"
      :model="formState"
      ref="formRef"
      @finish="handleSubmit"
    >
      <!-- 标题与字段 -->
      <div class="form-section">
        <a-form-item
          name="label"
          :label="$t('integration.table.columns.form.label')"
          :rules="[{ required: true, message: $t('integration.table.columns.form.labelRequired') }]"
        >
          <a-input v-model:value="formState.label" :placeholder="$t('integration.table.columns.form.labelPlaceholder')" />
        </a-form-item>
        
        <a-form-item
          name="field"
          :label="$t('integration.table.columns.form.field')"
          :rules="[{ required: true, message: $t('integration.table.columns.form.fieldRequired') }]"
        >
          <a-select
            v-model:value="formState.field"
            :placeholder="$t('integration.table.columns.form.fieldPlaceholder')"
            :options="fieldOptions"
            show-search
            :filter-option="filterOption"
            :disabled="!isNewColumn"
          >
            <template #notFoundContent>
              <div class="custom-field-input">
                <a-input 
                  v-model:value="customField" 
                  :placeholder="$t('integration.table.columns.form.customFieldPlaceholder')"
                  @press-enter="handleCustomFieldEnter"
                />
                <a-button @click="handleCustomFieldEnter" type="primary" size="small">
                  {{ $t('common.add') }}
                </a-button>
              </div>
            </template>
          </a-select>
        </a-form-item>
      </div>
      
      <!-- 显示设置 -->
      <div class="form-section">
        <div class="section-title">{{ $t('integration.table.columns.form.displaySettings') }}</div>
        
        <a-form-item
          name="displayType"
          :label="$t('integration.table.columns.form.displayType')"
        >
          <a-select
            v-model:value="formState.displayType"
            :placeholder="$t('integration.table.columns.form.displayTypePlaceholder')"
            :options="displayTypeOptions"
          />
        </a-form-item>
        
        <!-- 枚举类型特有设置 -->
        <template v-if="formState.displayType === 'ENUM'">
          <a-form-item
            name="enumValues"
            :label="$t('integration.table.columns.form.enumValues')"
            :rules="[{ required: formState.displayType === 'ENUM', message: $t('integration.table.columns.form.enumValuesRequired') }]"
          >
            <a-select
              v-model:value="formState.enumValues"
              mode="tags"
              :placeholder="$t('integration.table.columns.form.enumValuesPlaceholder')"
            />
          </a-form-item>
        </template>
        
        <!-- 链接类型特有设置 -->
        <template v-if="formState.displayType === 'LINK'">
          <a-form-item
            name="linkConfig.urlTemplate"
            :label="$t('integration.table.columns.form.urlTemplate')"
            :rules="[{ required: formState.displayType === 'LINK', message: $t('integration.table.columns.form.urlTemplateRequired') }]"
          >
            <a-input
              v-model:value="formState.linkConfig.urlTemplate"
              :placeholder="$t('integration.table.columns.form.urlTemplatePlaceholder')"
            />
          </a-form-item>
          
          <a-form-item
            name="linkConfig.text"
            :label="$t('integration.table.columns.form.linkText')"
          >
            <a-input
              v-model:value="formState.linkConfig.text"
              :placeholder="$t('integration.table.columns.form.linkTextPlaceholder')"
            />
          </a-form-item>
        </template>
        
        <!-- 自定义渲染特有设置 -->
        <template v-if="formState.displayType === 'CUSTOM'">
          <a-form-item
            name="customRenderTemplate"
            :label="$t('integration.table.columns.form.customTemplate')"
            :rules="[{ required: formState.displayType === 'CUSTOM', message: $t('integration.table.columns.form.customTemplateRequired') }]"
          >
            <a-textarea
              v-model:value="formState.customRenderTemplate"
              :placeholder="$t('integration.table.columns.form.customTemplatePlaceholder')"
              :auto-size="{ minRows: 3, maxRows: 8 }"
            />
          </a-form-item>
        </template>
        
        <a-form-item name="visible" class="switch-item">
          <template #label>
            <span>{{ $t('integration.table.columns.form.visible') }}</span>
          </template>
          <a-switch v-model:checked="formState.visible" />
        </a-form-item>
        
        <a-form-item name="sortable" class="switch-item">
          <template #label>
            <span>{{ $t('integration.table.columns.form.sortable') }}</span>
          </template>
          <a-switch v-model:checked="formState.sortable" />
        </a-form-item>
        
        <a-form-item name="filterable" class="switch-item">
          <template #label>
            <span>{{ $t('integration.table.columns.form.filterable') }}</span>
          </template>
          <a-switch v-model:checked="formState.filterable" />
        </a-form-item>
      </div>
      
      <!-- 按钮组 -->
      <div class="form-section form-actions">
        <a-button v-if="!isNewColumn" @click="$emit('cancel')">
          {{ $t('common.cancel') }}
        </a-button>
        <a-button type="primary" html-type="submit">
          {{ $t(isNewColumn ? 'common.create' : 'common.save') }}
        </a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { v4 as uuidv4 } from 'uuid';
import type { TableColumn } from '@/types/integration';
import { ColumnDisplayType } from '@/types/integration';

interface Props {
  column?: TableColumn | null;
  availableFields: string[];
  isNewColumn?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  column: null,
  availableFields: () => [],
  isNewColumn: false
});

const emit = defineEmits<{
  (e: 'save', column: TableColumn): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 自定义字段输入
const customField = ref('');

// 表单状态
const defaultFormState = (): TableColumn => ({
  id: uuidv4(),
  label: '',
  field: '',
  displayType: ColumnDisplayType.TEXT,
  visible: true,
  sortable: true,
  filterable: true,
  width: 'auto',
  enumValues: [],
  linkConfig: {
    urlTemplate: '',
    text: ''
  },
  customRenderTemplate: '',
  order: 0
});

const formState = reactive<TableColumn>({
  ...defaultFormState(),
  ...(props.column || {})
});

// 监听编辑的列变化
watch(() => props.column, (newColumn) => {
  if (newColumn) {
    Object.assign(formState, defaultFormState(), newColumn);
  } else {
    Object.assign(formState, defaultFormState());
  }
}, { deep: true });

// 可用字段选项
const fieldOptions = computed(() => {
  return props.availableFields.map(field => ({
    value: field,
    label: field
  }));
});

// 显示类型选项
const displayTypeOptions = computed(() => {
  return Object.entries(ColumnDisplayType).map(([key, value]) => ({
    value,
    label: key.charAt(0) + key.slice(1).toLowerCase()
  }));
});

// 过滤选项方法
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 处理自定义字段
const handleCustomFieldEnter = () => {
  if (customField.value && customField.value.trim()) {
    formState.field = customField.value.trim();
    customField.value = '';
  }
};

// 表单提交
const handleSubmit = () => {
  formRef.value?.validate().then(() => {
    emit('save', { ...formState });
  }).catch(errors => {
    console.error('Validation failed:', errors);
  });
};
</script>

<style scoped>
.column-edit-form {
  padding: 0 4px;
}

.form-section {
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #262626;
}

.switch-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.switch-item :deep(.ant-form-item-label) {
  flex: 1;
}

.custom-field-input {
  display: flex;
  gap: 8px;
  padding: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>