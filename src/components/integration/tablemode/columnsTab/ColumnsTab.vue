<template>
  <div class="columns-tab">
    <a-spin :spinning="loading">
      <div v-if="!showForm" class="columns-list-container">
        <ColumnsList
          :columns="columns"
          :loading="loading"
          @add="handleAddColumn"
          @edit="handleEditColumn"
          @delete="handleDeleteColumn"
          @reorder="handleReorderColumns"
          @toggle-visibility="handleToggleVisibility"
        />
      </div>
      <div v-else class="column-form-container">
        <ColumnForm
          :column="currentColumn"
          :existing-fields="existingFieldNames"
          :saving="saving"
          :allow-field-edit="false"
          @save="handleSaveColumn"
          @cancel="handleCancelForm"
          @open-enum-selector="openEnumSelector"
          @open-enum-config="openParamConfigModal"
        />
      </div>
    </a-spin>

    <!-- 枚举选择器对话框 -->
    <EnumSelector
      v-if="showEnumSelector"
      :projectCode="enumProjectCode"
      @select="handleEnumSelected"
      @close="closeEnumSelector"
    />

    <!-- 参数配置弹窗 - 独立于列编辑弹窗 -->
    <div v-if="showParamConfigModal" class="param-config-modal">
      <ParamAdvancedConfig
        v-if="columnParam"
        v-model="columnParam"
        :isVisible="showParamConfigModal"
        :projectCode="enumProjectCode"
        @save="handleParamSave"
        @close="closeParamConfigModal"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { message } from '@/services/message';
import ColumnsList from './ColumnsList.vue';
import ColumnForm from './ColumnForm.vue';
import type { TableColumn } from '@/types/integration';
import { ColumnDisplayType, ColumnAlign } from '@/types/integration';
import EnumSelector from '@/components/integration/queryparams/enum/EnumSelector.vue';
import ParamAdvancedConfig from '@/components/integration/queryparams/ParamAdvancedConfig.vue';
import { enumServiceConfig } from '@/utils/config';
import { syncConfigToTopLevel, syncTopLevelToConfig } from '@/utils/columnConfigSync';

// Props
const props = defineProps<{
  integrationId?: string;
  dataSource?: string;
  tableConfig?: any;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:columns', columns: TableColumn[]): void;
  (e: 'save', columns: TableColumn[]): void;
}>();

// 列表数据
const loading = ref(false);
const saving = ref(false);
const columns = ref<TableColumn[]>([]);

// 表单控制
const showForm = ref(false);
const currentColumn = ref<TableColumn | undefined>(undefined);

// 枚举选择器和参数配置模态框控制
const showEnumSelector = ref(false);
const showParamConfigModal = ref(false);
const enumProjectCode = ref(enumServiceConfig.projectCode);

// 现有字段名称列表（用于唯一性验证）
const existingFieldNames = computed(() => {
  return columns.value.map(col => col.field);
});

// 初始化数据
onMounted(async () => {
  if (props.integrationId && props.dataSource) {
    await fetchColumns();
  }
});

// 获取列配置数据
const fetchColumns = async () => {
  loading.value = true;

  try {
    // 从父组件获取列配置
    if (props.tableConfig && props.tableConfig.columns) {
      columns.value = [...props.tableConfig.columns];
    } else {
      // 如果没有从父组件获取到，则创建空数组或者使用默认配置
      columns.value = [];

      // 可以添加一些默认列配置
      if (columns.value.length === 0) {
        // 默认列仅用于示例，实际生产环境应移除
        columns.value = [
          {
            id: 'col_1',
            field: 'id',
            label: 'ID',
            displayType: 'text',
            visible: true,
            align: ColumnAlign.LEFT,
            width: 150,
            sortable: false,
            filterable: false
          },
          {
            id: 'col_2',
            field: 'name',
            label: '名称',
            displayType: 'text',
            visible: true,
            align: ColumnAlign.LEFT,
            width: 200,
            sortable: false,
            filterable: false
          }
        ];
      }
    }

    message.success('列配置加载成功');
  } catch (error) {
    console.error('获取列配置失败', error);
    message.error('获取列配置失败');
  } finally {
    loading.value = false;
  }
};

// 保存列配置数据
const saveColumns = async () => {
  if (!props.integrationId || !props.dataSource) return;

  loading.value = true;

  try {
    // 向父组件发送更新事件
    emit('update:columns', columns.value);
    emit('save', columns.value);

    message.success('列配置保存成功');
  } catch (error) {
    console.error('保存列配置失败', error);
    message.error('保存列配置失败');
  } finally {
    loading.value = false;
  }
};

// 添加列
const handleAddColumn = () => {
  currentColumn.value = undefined;
  showForm.value = true;
};

// 编辑列
const handleEditColumn = (column: TableColumn) => {
  // 创建深拷贝，防止直接修改原始对象
  currentColumn.value = JSON.parse(JSON.stringify(column));

  // 将config对象中的属性同步到顶级属性，以便编辑界面能直接访问它们
  if (currentColumn.value && typeof syncConfigToTopLevel === 'function') {
    syncConfigToTopLevel(currentColumn.value);
  }

  showForm.value = true;
};

// 取消表单
const handleCancelForm = () => {
  showForm.value = false;
  currentColumn.value = undefined;

  // 同时关闭相关联的模态框
  showEnumSelector.value = false;
  showParamConfigModal.value = false;
};

// 删除列
const handleDeleteColumn = async (column: TableColumn) => {
  try {
    loading.value = true;
    // 移除列
    columns.value = columns.value.filter(c => c.id !== column.id);
    await saveColumns();
    message.success(`列 "${column.label || column.field}" 已删除`);
  } catch (error) {
    console.error('删除列失败', error);
    message.error('删除列失败');
  } finally {
    loading.value = false;
  }
};

// 切换列可见性
const handleToggleVisibility = async (column: TableColumn, visible: boolean) => {
  try {
    loading.value = true;
    const index = columns.value.findIndex(c => c.id === column.id);
    if (index !== -1) {
      columns.value[index] = { ...columns.value[index], visible };
      await saveColumns();
      message.success(`列 "${column.label || column.field}" 可见性已${visible ? '开启' : '关闭'}`);
    }
  } catch (error) {
    console.error('切换可见性失败', error);
    message.error('更新失败');
  } finally {
    loading.value = false;
  }
};

// 重新排序列
const handleReorderColumns = async (reorderedColumns: TableColumn[]) => {
  try {
    loading.value = true;
    columns.value = [...reorderedColumns];

    // 更新displayOrder属性
    columns.value = columns.value.map((col, index) => ({
      ...col,
      displayOrder: index
    }));

    await saveColumns();
    message.success('列顺序已更新');
  } catch (error) {
    console.error('重新排序失败', error);
    message.error('重新排序失败');
  } finally {
    loading.value = false;
  }
};

// 保存列
const handleSaveColumn = async (column: TableColumn) => {
  saving.value = true;

  try {
    // 在保存前，如果有同步函数，则将顶级属性同步回config对象
    if (typeof syncTopLevelToConfig === 'function') {
      syncTopLevelToConfig(column);
    }

    const index = columns.value.findIndex(c => c.id === column.id);

    if (index !== -1) {
      // 更新现有列
      columns.value[index] = column;
    } else {
      // 添加新列
      columns.value.push(column);
    }

    await saveColumns();
    showForm.value = false;
    currentColumn.value = undefined;
    message.success(index !== -1 ?
      `列 "${column.label || column.field}" 已更新` :
      `列 "${column.label || column.field}" 已添加`);
  } catch (error) {
    console.error('保存列失败', error);
    message.error('保存列失败');
  } finally {
    saving.value = false;
  }
};

// 打开枚举选择器
const openEnumSelector = () => {
  if (currentColumn.value) {
    showEnumSelector.value = true;
  }
};

// 处理枚举选择
const handleEnumSelected = (result: { options: Array<{label: string, value: string}>; enumId: string; enumName: string; enumCode: string; }) => {
  if (currentColumn.value && result) {
    console.log('[ColumnsTab] 选择枚举:', result);
    currentColumn.value.enumCode = result.enumCode || '';
    currentColumn.value.enumId = result.enumId;
    currentColumn.value.enumName = result.enumName;

    // 设置默认展示方式为文本
    if (!currentColumn.value.enumDisplay) {
      currentColumn.value.enumDisplay = 'text';
    }

    // 如果有选项，设置默认选中第一个
    if (result.options && result.options.length > 0) {
      currentColumn.value.defaultEnumValue = result.options[0].value;
    }

    // 使用丰富的消息通知
    const optionCount = result.options?.length || 0;
    message.success(`列 "${currentColumn.value.label || currentColumn.value.field}" 已关联到枚举 "${result.enumName}"${optionCount > 0 ? `，包含 ${optionCount} 个选项` : ''}`);
  }
  showEnumSelector.value = false;
};

// 关闭枚举选择器
const closeEnumSelector = () => {
  showEnumSelector.value = false;
};

// 打开参数配置弹窗
const openParamConfigModal = () => {
  if (currentColumn.value && (currentColumn.value.format === 'enum' || currentColumn.value.enumId)) {
    showParamConfigModal.value = true;
  }
};

// 关闭参数配置弹窗
const closeParamConfigModal = () => {
  showParamConfigModal.value = false;
};

// 将表格列转换为参数对象，用于ParamAdvancedConfig组件
const columnParam = computed(() => {
  if (!currentColumn.value) return null;

  return {
    name: currentColumn.value.field,
    label: currentColumn.value.label,
    type: 'string',
    format: 'enum',
    formType: 'select',
    required: false,
    description: currentColumn.value.label || currentColumn.value.field,
    displayOrder: currentColumn.value.displayOrder,
    enumId: currentColumn.value.enumId,
    enumName: currentColumn.value.enumName,
    enumCode: currentColumn.value.enumCode,
    options: [], // 这里需要从枚举服务获取
    multiSelect: false
  };
});

// 处理ParamAdvancedConfig保存事件
const handleParamSave = (param: any) => {
  if (!currentColumn.value) return;

  // 保存之前的枚举码，用于判断是否有变化
  const previousEnumCode = currentColumn.value.enumCode;

  // 更新编辑中列的枚举信息
  currentColumn.value.enumId = param.enumId;
  currentColumn.value.enumName = param.enumName;
  currentColumn.value.enumCode = param.enumCode;
  currentColumn.value.enumDisplay = param.exportConfig?.config?.displayType === 'select' ? 'text' : param.enumDisplay || 'text';

  // 如果参数有默认值，更新列的默认枚举值
  if (param.defaultValue) {
    currentColumn.value.defaultEnumValue = param.defaultValue;
  }

  // 关闭参数配置弹窗
  closeParamConfigModal();

  // 使用丰富的消息通知
  if (previousEnumCode !== param.enumCode) {
    // 枚举有变更
    message.success(`枚举关联已更新: 列「${currentColumn.value.label || currentColumn.value.field}」已关联到枚举「${param.enumName || param.enumCode}」`);
  } else {
    // 其他配置变更
    message.success(`显示配置已更新: 列「${currentColumn.value.label || currentColumn.value.field}」的显示配置已更新`);
  }
};

// 从数据字段导入列
const importFieldsFromData = async () => {
  try {
    if (!props.integrationId || !props.dataSource) {
      message.warning('无法导入字段: 必须选择查询才能导入字段');
      return;
    }

    console.log('[ColumnsTab] 开始从数据导入字段');
    message.info('正在加载字段数据，请稍候...');
    loading.value = true;

    // 这里应从API获取可用字段
    // 示例代码，实际中应替换为API调用
    const availableFields = ['id', 'name', 'age', 'email', 'created_at'];

    // 获取已存在的字段列表，避免重复添加
    const existingFields = new Set(columns.value.map(col => col.field));

    // 过滤出未添加的字段
    const newFields = availableFields.filter(field => !existingFields.has(field));

    if (newFields.length === 0) {
      message.info('所有字段已导入，无需重复导入字段');
      loading.value = false;
      return;
    }

    // 添加新字段
    for (const field of newFields) {
      columns.value.push({
        id: `col_${Date.now()}_${field}`,
        field,
        label: field,
        displayType: 'text',
        visible: true,
        align: ColumnAlign.LEFT,
        width: 150,
        sortable: false,
        filterable: false,
        displayOrder: columns.value.length,
        isNewColumn: false,
        helpText: '',
        type: 'string'
      });
    }

    await saveColumns();
    message.success(`成功导入 ${newFields.length} 个字段`);
  } catch (error) {
    console.error('导入字段失败', error);
    message.error('导入字段失败');
  } finally {
    loading.value = false;
  }
};

// 导出方法给父组件使用
defineExpose({
  importFieldsFromData
});
</script>

<style scoped>
.columns-tab {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.columns-list-container,
.column-form-container {
  width: 100%;
  flex: 1;
}

.param-config-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3500 !important; /* 确保比其他弹窗更高 */
}
</style>
