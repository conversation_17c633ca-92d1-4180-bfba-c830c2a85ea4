<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-20">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
      <!-- 弹窗标题 -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">编辑列配置</h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-500 focus:outline-none">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="px-6 py-4 flex-grow overflow-y-auto">
        <div class="space-y-4">
          <!-- 配置区域 -->
          <div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 可见性 -->
              <div class="flex items-center">
                <input
                  id="column-visible"
                  type="checkbox"
                  v-model="column.visible"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                />
                <label for="column-visible" class="ml-2 block text-sm text-gray-900">显示该列</label>
              </div>

              <!-- 可排序 -->
              <div class="flex items-center">
                <input
                  id="column-sortable"
                  type="checkbox"
                  v-model="column.sortable"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                />
                <label for="column-sortable" class="ml-2 block text-sm text-gray-900">允许排序</label>
              </div>

              <!-- 可过滤 -->
              <div class="flex items-center">
                <input
                  id="column-filterable"
                  type="checkbox"
                  v-model="column.filterable"
                  class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                />
                <label for="column-filterable" class="ml-2 block text-sm text-gray-900">允许筛选</label>
              </div>
            </div>

            <!-- 数据格式配置区域 -->
            <div class="mt-4 border-t border-gray-200 pt-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 帮助文本 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">帮助文本</label>
                  <input
                    v-model="column.helpText"
                    type="text"
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    placeholder="输入对此列的说明文字"
                  />
                </div>

                <!-- 自动省略 -->
                <div v-if="column.displayType === 'TEXT'">
                  <div class="flex items-center">
                    <input
                      id="auto-ellipsis"
                      type="checkbox"
                      v-model="column.truncate"
                      class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    />
                    <label for="auto-ellipsis" class="ml-2 block text-sm text-gray-900">自动省略</label>
                  </div>
                </div>

                <!-- 小数位数 -->
                <div v-if="column.format === 'decimal'">
                  <label class="block text-sm font-medium text-gray-700 mb-1">小数位数</label>
                  <input
                    v-model.number="column.fixedPoint"
                    type="number"
                    min="0"
                    max="10"
                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    placeholder="保留小数位数"
                  />
                </div>

                <!-- 千位分隔符 -->
                <div v-if="column.format === 'int' || column.format === 'decimal'">
                  <div class="flex items-center">
                    <input
                      id="thousand-separator"
                      type="checkbox"
                      v-model="column.thousandSeparator"
                      class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    />
                    <label for="thousand-separator" class="ml-2 block text-sm text-gray-900">启用千位分隔符</label>
                  </div>
                </div>
              </div>
            </div>

            <!-- 枚举配置 -->
            <div v-if="column.format === 'enum'" class="mt-4 border-t border-gray-200 pt-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">枚举配置</h4>

              <!-- 枚举关联信息展示 -->
              <div v-if="column.enumId && column.enumName" class="bg-blue-50 rounded-md p-3 mb-3">
                <div class="flex justify-between items-center">
                  <div>
                    <span class="text-sm font-medium text-blue-700">当前关联枚举: </span>
                    <span class="text-sm text-blue-800">{{ column.enumName }}</span>
                    <span class="text-xs text-gray-500 ml-2">({{ column.enumCode }})</span>
                  </div>
                </div>
              </div>

              <!-- 打开配置按钮 -->
              <button
                @click="$emit('open-enum-config')"
                class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <i class="fas fa-cog mr-2"></i> 配置枚举关联
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button
          @click="$emit('close')"
          class="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3 focus:outline-none"
          >
          取消
          </button>
        <button
          @click="$emit('save', column)"
          class="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
        >
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { TableColumn } from '@/types/integration';
import { syncConfigToTopLevel } from '@/utils/columnConfigSync';

// 接收props
const props = defineProps<{
  modelValue: TableColumn;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', column: TableColumn): void;
  (e: 'save', column: TableColumn): void;
  (e: 'close'): void;
  (e: 'open-enum-config'): void;
}>();

// 内部编辑状态
const column = ref<TableColumn>(JSON.parse(JSON.stringify(props.modelValue)));

// 初始化时同步配置
onMounted(() => {
  // 将config对象中的属性同步到顶级属性，以便编辑界面能直接访问它们
  syncConfigToTopLevel(column.value);
});
</script>

<style scoped>
/* 弹窗样式微调 */
.fixed {
  z-index: 100;
}

.fixed > div {
  position: relative;
  z-index: 101;
}

/* 确保下拉菜单在弹窗内部正确显示 */
.fixed select {
  position: relative;
  z-index: 110;
}

.fixed select:focus {
  z-index: 120;
}
</style>
