<template>
  <div class="p-6">
    <div class="space-y-6">
      <!-- 分页配置 -->
      <div>
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-lg font-medium text-gray-900"><i class="fas fa-pager mr-2 text-indigo-500"></i>分页设置</h3>
          <div class="flex items-center">
            <span class="mr-2 text-sm text-gray-500">启用分页</span>
            <label class="switch">
              <input type="checkbox" :checked="pagination.enabled" @click="togglePagination">
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div v-if="pagination.enabled" class="bg-gray-50 p-4 rounded-md">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 默认每页条数 -->
            <div>
              <label for="page-size" class="block text-sm font-medium text-gray-700 mb-1">默认每页条数</label>
              <input 
                id="page-size"
                v-model.number="pagination.pageSize" 
                type="number" 
                min="1"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            
            <!-- 可选每页条数 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">可选每页条数</label>
              <div class="flex flex-wrap gap-2">
                <div v-for="(size, index) in pagination.pageSizeOptions" :key="index" class="flex items-center">
                  <input 
                    v-model.number="pagination.pageSizeOptions[index]" 
                    type="number" 
                    min="1"
                    class="w-16 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 rounded-md mr-1"
                  />
                  <button 
                    @click="removePaginationOption(index)" 
                    class="text-red-600 hover:text-red-900"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <button 
                  @click="addPaginationOption" 
                  class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
                >
                  <i class="fas fa-plus mr-1"></i> 添加
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 数据导出配置 -->
      <div>
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-lg font-medium text-gray-900"><i class="fas fa-file-export mr-2 text-indigo-500"></i>数据导出</h3>
          <div class="flex items-center">
            <span class="mr-2 text-sm text-gray-500">启用导出</span>
            <label class="switch">
              <input type="checkbox" :checked="exportConfig.enabled" @click="toggleExport">
              <span class="slider"></span>
            </label>
          </div>
        </div>
        
        <div v-if="exportConfig.enabled" class="bg-gray-50 p-4 rounded-md">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 导出格式 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">导出格式</label>
              <div class="space-y-2">
                <div class="flex items-center">
                  <input 
                    id="export-csv" 
                    type="checkbox" 
                    :checked="exportConfig.formats.includes('CSV')"
                    @change="toggleExportFormat('CSV')"
                    class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="export-csv" class="ml-2 block text-sm text-gray-900">CSV</label>
                </div>
                <div class="flex items-center">
                  <input 
                    id="export-excel" 
                    type="checkbox" 
                    :checked="exportConfig.formats.includes('EXCEL')"
                    @change="toggleExportFormat('EXCEL')"
                    class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="export-excel" class="ml-2 block text-sm text-gray-900">Excel</label>
                </div>
                <div class="flex items-center">
                  <input 
                    id="export-json" 
                    type="checkbox" 
                    :checked="exportConfig.formats.includes('JSON')"
                    @change="toggleExportFormat('JSON')"
                    class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                  />
                  <label for="export-json" class="ml-2 block text-sm text-gray-900">JSON</label>
                </div>
              </div>
            </div>
            
            <!-- 最大导出行数 -->
            <div>
              <label for="max-rows" class="block text-sm font-medium text-gray-700 mb-1">最大导出行数</label>
              <input 
                id="max-rows"
                v-model.number="exportConfig.maxRows" 
                type="number" 
                min="1"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 其他功能配置 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-2"><i class="fas fa-sliders-h mr-2 text-indigo-500"></i>其他功能</h3>
        <div class="bg-gray-50 p-4 rounded-md">
          <div class="space-y-3">
            <!-- 行选择 -->
            <div class="flex items-center">
              <input 
                id="row-selection" 
                type="checkbox" 
                v-model="enableRowSelection"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label for="row-selection" class="ml-2 block text-sm text-gray-900">行选择功能</label>
            </div>
            
            <!-- 列排序 -->
            <div class="flex items-center">
              <input 
                id="column-sorting" 
                type="checkbox" 
                v-model="enableSorting"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label for="column-sorting" class="ml-2 block text-sm text-gray-900">列排序功能</label>
            </div>
            
            <!-- 列过滤 -->
            <div class="flex items-center">
              <input 
                id="column-filtering" 
                type="checkbox" 
                v-model="enableFiltering"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label for="column-filtering" class="ml-2 block text-sm text-gray-900">列过滤功能</label>
            </div>
            
            <!-- 列拖拽排序 -->
            <div class="flex items-center">
              <input 
                id="column-drag" 
                type="checkbox" 
                v-model="enableColumnDrag"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label for="column-drag" class="ml-2 block text-sm text-gray-900">列拖拽排序</label>
            </div>
            
            <!-- 列显示控制 -->
            <div class="flex items-center">
              <input 
                id="column-visibility" 
                type="checkbox" 
                v-model="enableColumnVisibility"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label for="column-visibility" class="ml-2 block text-sm text-gray-900">列显示控制</label>
            </div>
            
            <!-- 行展开详情 -->
            <div class="flex items-center">
              <input 
                id="expandable-rows" 
                type="checkbox" 
                v-model="enableExpandableRows"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
              />
              <label for="expandable-rows" class="ml-2 block text-sm text-gray-900">行展开详情</label>
            </div>
            
            <!-- 行操作列固定位置 -->
            <div class="flex items-center justify-between">
              <label for="row-actions-fixed" class="block text-sm text-gray-900">行操作列固定位置</label>
              <select
                id="row-actions-fixed"
                v-model="rowActionsFixed"
                class="ml-2 block w-40 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              >
                <option :value="false">不固定</option>
                <option value="left">固定在左侧</option>
                <option value="right">固定在右侧</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { PaginationConfig, ExportConfig } from '@/types/integration';
import { message } from '@/services/message';

// 接收props
const props = defineProps<{
  pagination: PaginationConfig;
  exportConfig: ExportConfig;
  rowActionsFixed?: 'left' | 'right' | false;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update:pagination', pagination: PaginationConfig): void;
  (e: 'update:exportConfig', exportConfig: ExportConfig): void;
  (e: 'update:rowActionsFixed', value: 'left' | 'right' | false): void;
}>();

// 内部状态
const enableRowSelection = ref(false);
const enableSorting = ref(false);
const enableFiltering = ref(false);
const enableColumnDrag = ref(false);
const enableColumnVisibility = ref(false);
const enableExpandableRows = ref(false);

// 行操作固定位置
const rowActionsFixed = computed({
  get: () => props.rowActionsFixed,
  set: (value) => emit('update:rowActionsFixed', value)
});

// 切换分页开关
const togglePagination = () => {
  const updatedConfig = { ...props.pagination };
  updatedConfig.enabled = !updatedConfig.enabled;
  emit('update:pagination', updatedConfig);
  message.success(`分页功能已${updatedConfig.enabled ? '启用' : '禁用'}`, 3000);
};

// 添加分页选项
const addPaginationOption = () => {
  const updatedConfig = { ...props.pagination };
  const lastOption = updatedConfig.pageSizeOptions[updatedConfig.pageSizeOptions.length - 1] || 10;
  updatedConfig.pageSizeOptions.push(lastOption * 2);
  emit('update:pagination', updatedConfig);
};

// 移除分页选项
const removePaginationOption = (index: number) => {
  const updatedConfig = { ...props.pagination };
  updatedConfig.pageSizeOptions.splice(index, 1);
  emit('update:pagination', updatedConfig);
};

// 切换导出开关
const toggleExport = () => {
  const updatedConfig = { ...props.exportConfig };
  updatedConfig.enabled = !updatedConfig.enabled;
  emit('update:exportConfig', updatedConfig);
  message.success(`导出功能已${updatedConfig.enabled ? '启用' : '禁用'}`, 3000);
};

// 切换导出格式
const toggleExportFormat = (format: string) => {
  const updatedConfig = { ...props.exportConfig };
  const index = updatedConfig.formats.indexOf(format);
  
  if (index === -1) {
    updatedConfig.formats.push(format);
  } else {
    updatedConfig.formats.splice(index, 1);
  }
  
  emit('update:exportConfig', updatedConfig);
};
</script>

<style scoped>
select, input {
  min-height: 38px;
  line-height: 1.5;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e0;
  transition: .4s;
  border-radius: 22px;
}
.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}
input:checked + .slider {
  background-color: #4f46e5;
}
input:checked + .slider:before {
  transform: translateX(20px);
}
</style>