<template>
  <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <!-- 标签页切换 -->
    <div class="border-b border-gray-200">
      <nav class="flex" aria-label="Tabs">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          class="px-4 py-3 font-medium text-sm border-b-2 focus:outline-none flex items-center"
          :class="[
            activeTab === tab.key
              ? 'border-indigo-500 text-indigo-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          <i :class="getTabIcon(tab.key)" class="mr-2"></i>
          {{ tab.label }}
        </button>
      </nav>
    </div>

    <!-- 基本配置 -->
    <div v-if="activeTab === 'basic'" class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 图表类型 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">图表类型</label>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="type in chartTypes"
              :key="type.value"
              @click="selectChartType(type.value)"
              class="inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium focus:outline-none"
              :class="[
                chartConfig.type === type.value
                  ? 'border-indigo-500 text-indigo-600 bg-indigo-50'
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              ]"
            >
              <i :class="`fas ${type.icon} mr-2`"></i>
              {{ type.label }}
            </button>
          </div>
        </div>

        <!-- 图表标题 -->
        <div>
          <label for="chart-title" class="block text-sm font-medium text-gray-700 mb-2">图表标题</label>
          <input
            type="text"
            id="chart-title"
            v-model="chartConfig.title"
            class="ds-input px-3 py-2 text-sm"
            placeholder="输入图表标题"
          />
        </div>

        <!-- 图表描述 -->
        <div>
          <label for="chart-description" class="block text-sm font-medium text-gray-700 mb-2">图表描述</label>
          <input
            type="text"
            id="chart-description"
            v-model="chartConfig.description"
            class="ds-input px-3 py-2 text-sm"
            placeholder="输入图表描述（可选）"
          />
        </div>

        <!-- 图表高度 -->
        <div>
          <label for="chart-height" class="block text-sm font-medium text-gray-700 mb-2">图表高度（像素）</label>
          <input
            type="number"
            id="chart-height"
            v-model.number="chartConfig.height"
            class="ds-input px-3 py-2 text-sm"
            min="200"
            max="1000"
            step="50"
          />
        </div>

        <!-- 显示图例 -->
        <div class="flex items-center">
          <input
            type="checkbox"
            id="show-legend"
            v-model="chartConfig.showLegend"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="show-legend" class="ml-2 block text-sm text-gray-700">显示图例</label>
        </div>

        <!-- 启用动画 -->
        <div class="flex items-center">
          <input
            type="checkbox"
            id="enable-animation"
            v-model="chartConfig.animation"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="enable-animation" class="ml-2 block text-sm text-gray-700">启用动画</label>
        </div>
      </div>
    </div>

    <!-- 数据映射 -->
    <div v-if="activeTab === 'mapping'" class="p-4">
      <div class="mb-4">
        <h3 class="text-lg font-medium text-gray-900">字段映射</h3>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 字段映射 -->
        <template v-if="chartConfig.type === 'bar' || chartConfig.type === 'line' || chartConfig.type === 'scatter' || chartConfig.type === 'area'">
          <!-- X轴字段 -->
          <div>
            <label for="x-field" class="block text-sm font-medium text-gray-700 mb-2">X轴字段</label>
            <select
              id="x-field"
              v-model="chartConfig.dataMapping.xField"
              class="ds-select px-3 py-2 text-sm"
            >
              <option value="">请选择字段</option>
              <option v-for="field in availableFields" :key="field" :value="field">{{ field }}</option>
            </select>
          </div>

          <!-- Y轴字段 -->
          <div>
            <label for="y-field" class="block text-sm font-medium text-gray-700 mb-2">Y轴字段</label>
            <select
              id="y-field"
              v-model="chartConfig.dataMapping.yField"
              class="ds-select px-3 py-2 text-sm"
            >
              <option value="">请选择字段</option>
              <option v-for="field in availableFields" :key="field" :value="field">{{ field }}</option>
            </select>
          </div>

          <!-- 系列字段（可选） -->
          <div>
            <label for="series-field" class="block text-sm font-medium text-gray-700 mb-2">系列字段（可选）</label>
            <select
              id="series-field"
              v-model="chartConfig.dataMapping.seriesField"
              class="ds-select px-3 py-2 text-sm"
            >
              <option value="">不使用系列</option>
              <option v-for="field in availableFields" :key="field" :value="field">{{ field }}</option>
            </select>
            <p class="mt-1 text-xs text-gray-500">系列字段用于将数据分组，每个组显示为不同颜色</p>
          </div>

          <!-- 大小字段（仅散点图） -->
          <div v-if="chartConfig.type === 'scatter'">
            <label for="size-field" class="block text-sm font-medium text-gray-700 mb-2">大小字段（可选）</label>
            <select
              id="size-field"
              v-model="chartConfig.dataMapping.sizeField"
              class="ds-select px-3 py-2 text-sm"
            >
              <option value="">不使用大小映射</option>
              <option v-for="field in availableFields" :key="field" :value="field">{{ field }}</option>
            </select>
            <p class="mt-1 text-xs text-gray-500">大小字段用于确定散点的大小，通常为数值类型</p>
          </div>
        </template>

        <!-- 饼图特定字段 -->
        <template v-if="chartConfig.type === 'pie'">
          <!-- 名称/类别字段 -->
          <div>
            <label for="category-field" class="block text-sm font-medium text-gray-700 mb-2">类别字段</label>
            <select
              id="category-field"
              v-model="chartConfig.dataMapping.categoryField"
              class="ds-select px-3 py-2 text-sm"
            >
              <option value="">请选择字段</option>
              <option v-for="field in availableFields" :key="field" :value="field">{{ field }}</option>
            </select>
            <p class="mt-1 text-xs text-gray-500">类别字段用于饼图的分片名称</p>
          </div>

          <!-- 值字段 -->
          <div>
            <label for="value-field" class="block text-sm font-medium text-gray-700 mb-2">值字段</label>
            <select
              id="value-field"
              v-model="chartConfig.dataMapping.valueField"
              class="ds-select px-3 py-2 text-sm"
            >
              <option value="">请选择字段</option>
              <option v-for="field in availableFields" :key="field" :value="field">{{ field }}</option>
            </select>
            <p class="mt-1 text-xs text-gray-500">值字段用于确定饼图每个分片的大小</p>
          </div>
        </template>
      </div>
    </div>

    <!-- 样式配置 -->
    <div v-if="activeTab === 'style'" class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 主题选择 -->
        <div>
          <label for="chart-theme" class="block text-sm font-medium text-gray-700 mb-2">主题</label>
          <select
            id="chart-theme"
            v-model="chartConfig.theme"
            class="ds-select px-3 py-2 text-sm"
          >
            <option value="default">默认</option>
            <option value="light">亮色</option>
            <option value="dark">暗色</option>
          </select>
        </div>

        <!-- 背景色 -->
        <div>
          <label for="background-color" class="block text-sm font-medium text-gray-700 mb-2">背景色</label>
          <input
            type="text"
            id="background-color"
            v-model="chartConfig.styleOptions.backgroundColor"
            class="ds-input px-3 py-2 text-sm"
            placeholder="#ffffff"
          />
        </div>
      </div>
    </div>

    <!-- 交互配置 -->
    <div v-if="activeTab === 'interaction'" class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 启用缩放 -->
        <div class="flex items-center">
          <input
            type="checkbox"
            id="enable-zoom"
            v-model="chartConfig.interactions.enableZoom"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="enable-zoom" class="ml-2 block text-sm text-gray-700">启用缩放</label>
        </div>

        <!-- 启用平移 -->
        <div class="flex items-center">
          <input
            type="checkbox"
            id="enable-pan"
            v-model="chartConfig.interactions.enablePan"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="enable-pan" class="ml-2 block text-sm text-gray-700">启用平移</label>
        </div>

        <!-- 启用选择 -->
        <div class="flex items-center">
          <input
            type="checkbox"
            id="enable-select"
            v-model="chartConfig.interactions.enableSelect"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="enable-select" class="ml-2 block text-sm text-gray-700">启用数据点选择</label>
        </div>

        <!-- 提示框模式 -->
        <div>
          <label for="tooltip-mode" class="block text-sm font-medium text-gray-700 mb-2">提示框模式</label>
          <select
            id="tooltip-mode"
            v-model="chartConfig.interactions.tooltipMode"
            class="ds-select px-3 py-2 text-sm"
          >
            <option value="single">单点</option>
            <option value="multiple">多点</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 预览 -->
    <div v-if="activeTab === 'preview'" class="p-4">
      <div class="relative">
        <div class="bg-gray-50 rounded-lg p-4">
          <div v-if="!hasRequiredFields" class="text-center py-8 text-gray-500">
            <i class="fas fa-chart-line text-3xl mb-2"></i>
            <p>请完成必要的数据映射配置后查看预览</p>
          </div>
          <div v-else class="chart-preview-container">
            <!-- 这里将集成ChartPreview组件 -->
            <ChartPreview
              :chart-type="chartConfig.type"
              :title="chartConfig.title"
              :description="chartConfig.description"
              :height="chartConfig.height"
              :show-legend="chartConfig.showLegend"
              :animation="chartConfig.animation"
              :x-field="chartConfig.dataMapping.xField"
              :y-field="chartConfig.dataMapping.yField"
              :value-field="chartConfig.dataMapping.valueField"
              :category-field="chartConfig.dataMapping.categoryField"
              :series-field="chartConfig.dataMapping.seriesField"
              :size-field="chartConfig.dataMapping.sizeField"
              :name-field="chartConfig.dataMapping.categoryField"
              :theme="chartConfig.theme"
              :query-id="queryId"
              :version-id="versionId"
              :params="queryParams"
            />
          </div>
        </div>
        <div class="mt-4 flex justify-end">
          <button
            @click="refreshPreview"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-sync-alt mr-2"></i>刷新预览
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, computed, onMounted } from 'vue';
import { useQueryStore } from '@/stores/query';
import { ChartType, ChartTheme } from '@/types/integration';
import ChartPreview from './ChartPreview.vue';
import { message } from '@/services/message';
import type { ChartConfig } from '@/types/unified-integration';
import {getApiBaseUrl} from "@/services/query";
import instance from '@/utils/axios';
import { globalApiRequestManager } from '@/utils/requestManager';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  queryId: {
    type: String,
    required: true
  },
  queryParams: {
    type: Object,
    default: () => ({})
  },
  availableFields: {
    type: Array as () => string[],
    default: () => []
  },
  versionId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:modelValue', 'refresh-preview', 'fields-loaded']);

// 查询store
const queryStore = useQueryStore();

// 标签页
const tabs = [
  { key: 'basic', label: '基本配置' },
  { key: 'mapping', label: '数据映射' },
  { key: 'style', label: '样式配置' },
  { key: 'interaction', label: '交互配置' },
  { key: 'preview', label: '预览' }
];

// 当前激活的标签页
const activeTab = ref('basic');

// 图表类型选项
const chartTypes = [
  { value: ChartType.BAR, label: '柱状图', icon: 'fa-chart-bar' },
  { value: ChartType.LINE, label: '折线图', icon: 'fa-chart-line' },
  { value: ChartType.PIE, label: '饼图', icon: 'fa-chart-pie' },
  { value: ChartType.SCATTER, label: '散点图', icon: 'fa-braille' },
  { value: ChartType.AREA, label: '面积图', icon: 'fa-chart-area' }
];

// 本地图表配置状态
const chartConfig = ref({
  type: ChartType.BAR,
  title: '',
  description: '',
  height: 400,
  theme: ChartTheme.DEFAULT,
  showLegend: true,
  animation: true,
  dataMapping: {
    xField: '',
    yField: '',
    valueField: '',
    categoryField: '',
    seriesField: '',
    colorField: '',
    sizeField: ''
  },
  styleOptions: {
    backgroundColor: '',
    colors: [],
    fontFamily: '',
    borderRadius: 0,
    padding: 0
  },
  interactions: {
    enableZoom: false,
    enablePan: false,
    enableSelect: false,
    tooltipMode: 'single'
  }
});

// 初始化时从props同步配置
onMounted(() => {
  // 使用深拷贝来避免直接引用props中的对象
  const config = JSON.parse(JSON.stringify(props.modelValue));

  // 合并默认值和传入的配置
  chartConfig.value = {
    ...chartConfig.value,
    ...config,
    dataMapping: {
      ...chartConfig.value.dataMapping,
      ...(config.dataMapping || {})
    },
    styleOptions: {
      ...chartConfig.value.styleOptions,
      ...(config.styleOptions || {})
    },
    interactions: {
      ...chartConfig.value.interactions,
      ...(config.interactions || {})
    }
  };

  // 添加配置变化监听并向上同步
  const stopWatcher = setInterval(() => {
    const newConfig = chartConfig.value;
    emit('update:modelValue', JSON.parse(JSON.stringify(newConfig)));
  }, 500); // 每500ms检查一次变化

  // 清理函数
  window.addEventListener('beforeunload', () => {
    clearInterval(stopWatcher);
  });
});

// 判断是否具备必要的字段映射
const hasRequiredFields = computed(() => {
  const { type, dataMapping } = chartConfig.value;

  if (type === ChartType.PIE) {
    return !!dataMapping.valueField && !!dataMapping.categoryField;
  } else if (type === ChartType.BAR || type === ChartType.LINE || type === ChartType.SCATTER || type === ChartType.AREA) {
    return !!dataMapping.xField && !!dataMapping.yField;
  }

  return false;
});

// 根据标签页获取对应的图标
const getTabIcon = (tabKey: string) => {
  const iconMap: Record<string, string> = {
    'basic': 'fas fa-cog',
    'mapping': 'fas fa-project-diagram',
    'style': 'fas fa-paint-brush',
    'interaction': 'fas fa-hand-pointer',
    'preview': 'fas fa-eye'
  };

  return iconMap[tabKey] || 'fas fa-question-circle';
};

// 选择图表类型
const selectChartType = (type: ChartType) => {
  chartConfig.value.type = type;
};

// 刷新预览
const refreshPreview = () => {
  emit('refresh-preview');
};

// 从数据配置导入字段
const importFieldsFromData = async () => {
  if (!props.queryId) {
    return;
  }

  try {
    // 显示加载提示
    message.info('正在从查询配置中获取可用字段，请稍候...', 3);

    // 使用parameters接口但只提取fields字段
    const apiUrl = `/api/queries/${props.queryId}/parameters`;
    const response = await globalApiRequestManager.request(
      () => instance.get(apiUrl),
      apiUrl,
      { queryId: props.queryId },
      { cacheTime: 30000 }
    );
    const data = response.data;
      console.log('[ChartConfig] 导入字段API响应数据:', data);

      // 检查响应数据结构 - 只使用fields数组，不使用parameters数组
      const columnsData = (data.data && data.data.fields) || data.fields || data.parameters;

      if (!columnsData || !Array.isArray(columnsData)) {
        message.error('导入字段失败: 响应数据中不包含fields字段', 3);
        console.error('[ChartConfig] 导入字段失败: 响应数据结构不正确', data);
        return;
      }

      console.log('[ChartConfig] 解析到的字段数据:', columnsData);

      // 导出所有可用字段，更新本地字段列表
      const allFields = columnsData.map((col: any) => col.name || col.field || col.column_name || '').filter(Boolean);
      console.log('[ChartConfig] 提取的字段列表:', allFields);

      // 更新组件自身的字段列表 - 这里调用props不会直接更新父组件的字段列表
      // 需要通过事件通知父组件更新
      if (allFields.length > 0) {
        // 发出事件通知父组件更新字段列表
        emit('fields-loaded', allFields);

        // 更新本地字段列表（如果availableFields是reactive变量）
        if (props.availableFields) {
          // 这里只是记录日志，实际更新由父组件完成
          console.log('[ChartConfig] 当前可用字段:', props.availableFields);
        }
      }

      // 自动映射字段
      const fieldNames = columnsData.map((col: any) => col.name || col.field || col.column_name || '').filter(Boolean);
      const numericFields = columnsData
        .filter((col: any) => {
          const type = (col.type || '').toLowerCase();
          return type.includes('int') || type.includes('float') || type.includes('double') ||
                type.includes('decimal') || type.includes('number');
        })
        .map((col: any) => col.name || col.field || col.column_name || '')
        .filter(Boolean);

      const dateFields = columnsData
        .filter((col: any) => {
          const type = (col.type || '').toLowerCase();
          return type.includes('date') || type.includes('time');
        })
        .map((col: any) => col.name || col.field || col.column_name || '')
        .filter(Boolean);

      const stringFields = columnsData
        .filter((col: any) => {
          const type = (col.type || '').toLowerCase();
          return type.includes('char') || type.includes('text') || type.includes('string');
        })
        .map((col: any) => col.name || col.field || col.column_name || '')
        .filter(Boolean);

      // 根据图表类型自动分配字段
      const { type } = chartConfig.value;

      if (type === ChartType.PIE) {
        // 饼图：类别字段(字符串) + 数值字段(数字)
        if (stringFields.length > 0) {
          chartConfig.value.dataMapping.categoryField = stringFields[0];
        }
        if (numericFields.length > 0) {
          chartConfig.value.dataMapping.valueField = numericFields[0];
        }
      } else if (type === ChartType.BAR || type === ChartType.LINE || type === ChartType.AREA) {
        // 柱状图/折线图/面积图：x轴(类别/日期) + y轴(数值)
        if (dateFields.length > 0) {
          // 优先使用日期字段作为X轴
          chartConfig.value.dataMapping.xField = dateFields[0];
        } else if (stringFields.length > 0) {
          // 其次使用字符串字段
          chartConfig.value.dataMapping.xField = stringFields[0];
        } else if (fieldNames.length > 0) {
          // 最后用第一个字段
          chartConfig.value.dataMapping.xField = fieldNames[0];
        }

        if (numericFields.length > 0) {
          // 用第一个数值字段作为Y轴
          chartConfig.value.dataMapping.yField = numericFields[0];

          // 如果有更多数值字段，用第二个作为系列字段
          if (numericFields.length > 1) {
            chartConfig.value.dataMapping.seriesField = numericFields[1];
          }
        }
      } else if (type === ChartType.SCATTER) {
        // 散点图：x轴(数值) + y轴(数值) + 可选大小字段(数值)
        if (numericFields.length >= 2) {
          chartConfig.value.dataMapping.xField = numericFields[0];
          chartConfig.value.dataMapping.yField = numericFields[1];

          // 如果有第三个数值字段，用作大小字段
          if (numericFields.length >= 3) {
            chartConfig.value.dataMapping.sizeField = numericFields[2];
          }

          // 如果有字符串字段，用作系列字段
          if (stringFields.length > 0) {
            chartConfig.value.dataMapping.seriesField = stringFields[0];
          }
        }
      }

      message.success('已自动映射字段', 3);

      // 更新预览
      refreshPreview();
    // 移除了else块，因为instance.get在失败时会抛出异常
  } catch (error) {
    console.error('导入字段出错:', error);
    message.error('导入字段失败: ' + (error instanceof Error ? error.message : String(error)), 3);
  }
};

// 重新加载数据的方法（可在父组件中调用）
defineExpose({
  importFieldsFromData
});
</script>

<style scoped>
.chart-preview-container {
  min-height: 400px;
}
</style>
