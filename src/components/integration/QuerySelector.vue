<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQueryStore } from '@/stores/query';
import { message } from '@/services/message';
import { queryService } from '@/services/query';
import type { QueryHistoryParams, QueryType } from '@/types/query';

// 路由
const router = useRouter();

// 组件属性
const props = defineProps<{
  modelValue: string;
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  dataSourceId?: string;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'selected', id: string, data: any): void;
}>();

// Store
const queryStore = useQueryStore();

// 状态
const queries = ref<Array<{ 
  id: string; 
  name: string; 
  description?: string; 
  queryType: string; 
  dataSourceId?: string; 
}>>([]);
const loading = ref(false);
const searchText = ref('');
const selectedQueryId = ref(props.modelValue || '');

// 添加QueryItem类型定义
interface QueryItem {
  id: string;
  name: string;
  description?: string;
  type?: string;
  dataSourceId?: string;
}

// 计算属性
const filteredQueries = computed<Array<QueryItem>>(() => {
  let result: Array<QueryItem> = queries.value.map(query => ({
    id: query.id,
    name: query.name,
    description: query.description,
    type: query.queryType,
    dataSourceId: query.dataSourceId
  }));
  
  // 先过滤数据源
  if (props.dataSourceId) {
    result = result.filter(query => query.dataSourceId === props.dataSourceId);
  }
  
  return result;
});

// 查询选项
const queryOptions = computed(() => {
  return filteredQueries.value.map(query => ({
    value: query.id,
    label: query.name,
    description: query.description
  }));
});

// 监听selectedQueryId变化
watch(selectedQueryId, (newValue) => {
  emit('update:modelValue', newValue);
  
  // 查找选中的查询数据
  const selectedQuery = queries.value.find(q => q.id === newValue);
  if (selectedQuery) {
    emit('selected', newValue, selectedQuery);
  }
});

// 监听modelValue变化
watch(() => props.modelValue, (newValue, oldValue) => {
  console.log(`[QuerySelector] modelValue变更: ${oldValue} -> ${newValue}`);
  selectedQueryId.value = newValue;
  
  // 如果有新值，尝试从查询列表中找到相应的数据
  if (newValue) {
    const query = queries.value.find(q => q.id === newValue);
    if (query) {
      console.log(`[QuerySelector] 根据modelValue找到查询: ${query.name}, dataSourceId=${query.dataSourceId}`);
    } else {
      console.log(`[QuerySelector] 根据modelValue(${newValue})未找到匹配的查询，可能需要加载查询列表`);
      loadQueries();
    }
  }
}, { immediate: true });

// 监听组件props变更
watch([
  () => props.dataSourceId,
  () => props.disabled
], ([newDataSourceId, newDisabled], [oldDataSourceId, oldDisabled]) => {
  console.log(`[QuerySelector] props变更: 
    dataSourceId: ${oldDataSourceId} -> ${newDataSourceId}
    disabled: ${oldDisabled} -> ${newDisabled}`);
  
  if (newDataSourceId !== oldDataSourceId) {
    console.log(`[QuerySelector] 数据源ID变更，重新加载查询列表`);
    // 数据源ID变更，重新加载查询列表
    loadQueries();
    
    // 检查当前选择的查询是否属于新的数据源
    if (selectedQueryId.value) {
      const currentQuery = queries.value.find(q => q.id === selectedQueryId.value);
      console.log(`[QuerySelector] 检查当前选择的查询: `, currentQuery);
      
      if (currentQuery && currentQuery.dataSourceId !== newDataSourceId) {
        console.log(`[QuerySelector] 当前查询的数据源(${currentQuery.dataSourceId})与新选择的数据源(${newDataSourceId})不匹配，清空选择`);
        selectedQueryId.value = '';
        emit('update:modelValue', '');
      } else if (!currentQuery) {
        console.log(`[QuerySelector] 未找到当前选择的查询(${selectedQueryId.value})，保持选择不变，等待查询列表加载`);
      }
    }
  }
});

// 监听dataSourceId变化，重新加载查询
watch(() => props.dataSourceId, (newValue, oldValue) => {
  console.log(`[QuerySelector] 数据源ID变更(独立watch): ${oldValue} -> ${newValue}`);
  
  if (newValue) {
    // 如果数据源ID变更，重新加载查询列表
    loadQueries();
    
    // 如果当前选择的查询不属于新的数据源，则清空选择
    if (selectedQueryId.value) {
      const currentQuery = queries.value.find(q => q.id === selectedQueryId.value);
      if (currentQuery && currentQuery.dataSourceId !== newValue) {
        console.log(`[QuerySelector] 已选择的查询(${selectedQueryId.value})不属于新数据源(${newValue})，清空选择`);
        selectedQueryId.value = '';
        emit('update:modelValue', '');
      }
    }
  }
}, { immediate: true });

// 生命周期钩子
onMounted(() => {
  // 原有的加载查询
  loadQueries();
});

// 加载查询列表
const loadQueries = async () => {
  loading.value = true;
  
  try {
    console.log('[QuerySelector] 开始获取查询列表数据...');
    
    // 准备请求参数
    const params: QueryHistoryParams = { 
      page: 1, 
      size: 100,
      queryType: 'SQL' as QueryType
    };
    
    // 如果指定了数据源ID，则在请求中包含该参数
    if (props.dataSourceId) {
      params.dataSourceId = props.dataSourceId;
      console.log(`[QuerySelector] 使用数据源ID(${props.dataSourceId})筛选查询`);
    }
    
    console.log('[QuerySelector] 发送查询API请求，参数:', params);
    const result = await queryService.getQueries(params);
    
    console.log('[QuerySelector] 获取到的查询列表数据:', result);
    
    if (result && result.data) {
      // 确保我们正确处理API返回的分页结果，取出items
      const responseData = result.data;
      const queryItems = responseData.items || (Array.isArray(responseData) ? responseData : []);
      
      const oldLength = queries.value.length;
      queries.value = queryItems.map(query => {
        console.log('[QuerySelector] 处理查询数据:', query.id, query.name, query.dataSourceId);
        return {
          id: query.id,
          name: query.name || `查询 ${query.id}`,
          description: query.description,
          queryType: query.queryType,
          dataSourceId: query.dataSourceId // 确保查询数据中包含数据源ID
        };
      });
      
      console.log(`[QuerySelector] 处理后的查询列表: ${queries.value.length}项 (原${oldLength}项)`);
      console.log(`[QuerySelector] 与数据源 ${props.dataSourceId} 关联的查询数量:`, 
        props.dataSourceId ? queries.value.filter(q => q.dataSourceId === props.dataSourceId).length : queries.value.length);
      
      // 如果有已选择的查询ID，检查是否存在
      if (selectedQueryId.value) {
        const selectedExists = queries.value.some(q => q.id === selectedQueryId.value);
        console.log(`[QuerySelector] 检查已选查询(${selectedQueryId.value})是否存在: ${selectedExists}`);
        
        if (!selectedExists) {
          console.log(`[QuerySelector] 已选查询不存在于返回的查询列表中`);
        }
      }
    } else {
      console.warn('[QuerySelector] 查询API返回结果格式不正确:', result);
      queries.value = [];
    }
  } catch (error) {
    console.error('[QuerySelector] 加载查询列表失败', error);
    message.error({
      content: '加载查询列表失败',
      description: '无法从服务器获取查询列表，请检查网络连接或稍后重试',
      duration: 5000
    });
    queries.value = [];
  } finally {
    loading.value = false;
  }
};

// 刷新查询列表
const refreshQueries = async () => {
  await loadQueries();
};

// 处理change事件
const handleChange = (value: string) => {
  selectedQueryId.value = value;
};

// 导航到查询创建页面
const navigateToCreateQuery = (event: Event) => {
  event.preventDefault();
  
  // 如果有数据源ID，作为查询参数传递
  const query = props.dataSourceId ? { dataSourceId: props.dataSourceId } : {};
  
  // 在同一个标签页跳转，而不是打开新标签页
  router.push({
    path: '/query/create',
    query
  });
};

// 导航到查询列表页面
const navigateToQueries = (event: Event) => {
  event.preventDefault();
  
  // 如果有数据源ID，作为查询参数传递
  const query = props.dataSourceId ? { dataSourceId: props.dataSourceId } : {};
  
  // 在同一个标签页跳转，而不是打开新标签页
  router.push({
    path: '/query',
    query
  });
};

// 过滤选项函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<template>
  <div class="query-selector">
    <label v-if="props.label" :for="'query-selector-' + (Math.random().toString(36).substring(2))" class="block text-sm font-medium text-gray-700 mb-1">
      {{ props.label }}
      <span v-if="props.required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <a-select
        v-model:value="selectedQueryId"
        :placeholder="props.placeholder || '请选择数据查询'"
        :disabled="props.disabled || (props.dataSourceId && filteredQueries.length === 0)"
        :loading="loading"
        show-search
        :filter-option="filterOption"
        :status="props.error ? 'error' : undefined"
        style="width: 100%"
        @change="handleChange"
        :options="queryOptions"
        :empty-option-content="null"
      >
        <template #suffixIcon>
          <span class="suffix-icon-container">
            <i v-if="loading" class="fas fa-circle-notch fa-spin text-gray-400"></i>
            <a v-else @click.stop="refreshQueries" title="刷新查询列表">
              <i class="fas fa-sync-alt text-gray-400 hover:text-gray-600"></i>
            </a>
          </span>
        </template>
        <template #option="{ value, label, description }">
          <div>
            <div class="font-medium">{{ label }}</div>
            <div v-if="description" class="text-xs truncate text-gray-500">{{ description }}</div>
          </div>
        </template>
        <template #notFoundContent>
          <div v-if="props.dataSourceId && filteredQueries.length === 0 && !loading" class="p-4 text-center">
            <p class="text-sm text-gray-600 mb-1">当前数据源下没有可用的查询，请先为该数据源创建查询</p>
            <a 
              class="text-sm text-indigo-600 hover:text-indigo-800 inline-flex items-center"
              @click="navigateToCreateQuery"
            >
              <i class="fas fa-external-link-alt mr-1"></i>
              创建新查询
            </a>
          </div>
          <div v-else class="p-4 text-center text-gray-500">
            没有找到匹配的查询
          </div>
        </template>
      </a-select>
      
      <!-- 错误提示 -->
      <p v-if="props.error" class="mt-1 text-sm text-red-600">{{ props.error }}</p>
      
      <!-- 无可用查询的提示 -->
      <div v-if="props.dataSourceId && filteredQueries.length === 0 && !loading" class="mt-2 flex p-4 rounded-md bg-yellow-50">
        <div class="flex-shrink-0">
          <i class="fas fa-info-circle text-yellow-400"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">当前数据源下没有可用的查询</h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>查询选择会自动过滤与当前数据源相关的查询。请先为该数据源创建查询，然后再创建集成。</p>
            <a href="#" @click="navigateToQueries" class="mt-2 text-sm text-indigo-600 hover:text-indigo-800 inline-flex items-center">
              <i class="fas fa-external-link-alt mr-1"></i>
              前往查询列表
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.query-selector {
  margin-bottom: 0;
  position: relative;
  isolation: isolate;
}

.suffix-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 4px;
}

:deep(.ant-select-selection-search-input) {
  height: 100%;
}

:deep(.ant-select-selector) {
  height: 38px !important;
  padding: 4px 11px !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
}

:deep(.ant-select-item-option-content) {
  white-space: normal;
}

:deep(.ant-select-dropdown .ant-select-item) {
  padding: 8px 12px;
}
</style>