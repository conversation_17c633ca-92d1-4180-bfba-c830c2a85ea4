<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { watch, onUnmounted } from '@/plugins/vue-types-fix'
import monaco from '@/plugins/monaco'
import { processSQL } from '@/utils/sql-param-converter'

// 添加全局变量和函数声明
declare global {
  interface Window {
    __SQL_CONTENT__?: string;
    __REAL_SQL_CONTENT__?: string;
    updateSqlEditor?: (value: string) => boolean;
  }
}

// 定义简单版本的watchEffect和onBeforeUnmount
const watchEffect = (callback: () => void) => {
  // 初始运行一次
  callback();
  
  // 监听modelValue变化
  watch(() => props.modelValue, () => callback());
};

// 定义简单版本的onBeforeUnmount，因为Vue导出有问题
const onBeforeUnmount = (callback: () => void) => {
  // 只在组件中用简单的处理代替
  window.addEventListener('beforeunload', callback);
};

// 定义环境变量
const isDev = import.meta.env.DEV

// 定义组件属性
const props = defineProps<{
  modelValue: string
  dataSourceId?: string | null
  placeholder?: string
  height?: string
  minHeight?: string
  maxHeight?: string
  readonly?: boolean
  language?: 'sql' | 'text'
  theme?: string
  autoHeight?: boolean
}>()

// 定义组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'execute': [message?: string, selectedSql?: string];
  'cancel': [];
  'save': [];
  'change': [value: string];
}>();

// 编辑器DOM元素
const editorContainer = ref<HTMLDivElement | null>(null)
let editor: monaco.editor.IStandaloneCodeEditor | null = null

// 编辑器高亮模式
const language = props.language || 'sql'

// 编辑器内容
const content = ref(props.modelValue)

// 简单的 SQL 关键字
const SQL_KEYWORDS = [
  'SELECT', 'FROM', 'WHERE', 'ORDER BY', 'GROUP BY', 'HAVING', 'LIMIT',
  'INSERT INTO', 'VALUES', 'UPDATE', 'SET', 'DELETE FROM',
  'JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'INNER JOIN', 'OUTER JOIN',
  'CREATE TABLE', 'ALTER TABLE', 'DROP TABLE',
  'AND', 'OR', 'NOT', 'IN', 'LIKE', 'BETWEEN', 'IS NULL', 'IS NOT NULL',
  'COUNT', 'SUM', 'AVG', 'MIN', 'MAX',
  'UNION', 'UNION ALL', 'INTERSECT', 'EXCEPT'
]

// 监听 modelValue 变化
watchEffect(() => {
  const newValue = props.modelValue;
  if (editor && editor.getValue() !== newValue) {
    console.log('SqlEditor: 检测到modelValue变化，更新编辑器内容, 长度=', newValue?.length || 0);
    // 加一个延迟，确保编辑器已完全初始化
    setTimeout(() => {
      if (editor) {
        try {
          editor.setValue(newValue || '');
          // 触发布局更新
          editor.layout();
          // 聚焦编辑器
          editor.focus();
          
          console.log('SqlEditor: 编辑器内容已更新，长度=', editor.getValue().length);
        } catch (e) {
          console.error('SqlEditor: 设置内容时出错:', e);
        }
      }
    }, 50);
  }
});

// 监听内容变化，更新 modelValue
watchEffect(() => {
  const newValue = content.value
  console.log('SqlEditor: 内容变化, 发送update事件, 长度=', newValue?.length || 0);
  emit('update:modelValue', newValue);
  
  // 调整高度
  if (editor) {
    setTimeout(() => {
      if (editor) {
        adjustHeight(editor);
      }
    }, 0);
  }
})

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 处理Ctrl+Enter执行查询
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault();
    console.log('SqlEditor: 触发Ctrl+Enter执行, 内容长度=', content.value?.length || 0);
    execute();
  }
  
  // 处理Tab键缩进
  if (event.key === 'Tab') {
    event.preventDefault();
    
    const target = event.target as HTMLTextAreaElement;
    const start = target.selectionStart;
    const end = target.selectionEnd;
    
    // 插入两个空格作为缩进
    const newValue = target.value.substring(0, start) + '  ' + target.value.substring(end);
    target.value = newValue;
    
    // 更新v-model绑定值
    content.value = newValue;
    
    // 设置光标位置
    target.selectionStart = target.selectionEnd = start + 2;
  }
}

// 简单的 SQL 语法高亮
// 注意：这只是一个基本实现，实际项目可以使用 CodeMirror 或 Monaco Editor

// 执行查询
const execute = () => {
  // 验证数据源和查询内容
  if (!props.dataSourceId) {
    // 触发执行事件，但携带错误信息
    emit('execute', '请在左侧面板中选择一个数据源')
    return
  }
  
  // 获取选中的文本
  let selectedText = '';
  if (editor) {
    const selection = editor.getSelection();
    if (selection && !selection.isEmpty()) {
      // 有选中文本，获取选中的内容
      selectedText = editor.getModel()?.getValueInRange(selection) || '';
      console.log('SqlEditor: 检测到选中文本，长度=', selectedText.length);
    }
  }
  
  // 如果没有选中文本，使用全部内容
  if (!selectedText || selectedText.trim() === '') {
    selectedText = content.value || '';
    console.log('SqlEditor: 使用全部内容，长度=', selectedText.length);
  }
  
  if (!selectedText || !selectedText.trim()) {
    // 触发执行事件，但携带错误信息
    emit('execute', '请在SQL编辑器中输入查询语句')
    return
  }
  
  // 确保SQL内容传递正确 - 使用选中的内容
  const rawSql = selectedText.trim();
  
  // 处理SQL参数格式和关键字
  const processedSql = processSQL(rawSql);
  console.log('处理后的SQL:', processedSql);
  
  // 将处理后的SQL保存到全局变量
  window.__SQL_CONTENT__ = processedSql;
  
  // 触发执行事件，传递选中的SQL内容
  emit('execute', undefined, processedSql)
}

// 保存查询
const save = () => {
  emit('save')
}

// 插入 SQL 关键字
const insertKeyword = (keyword: string) => {
  if (!editor) return
  
  const editorInstance = editor
  const position = editorInstance.getPosition()
  if (!position) return
  
  const start = position.lineNumber
  const end = position.column
  
  // 插入关键字和空格
  const newValue = editorInstance.getValue().substring(0, start) + keyword + ' ' + editorInstance.getValue().substring(end)
  
  content.value = newValue
  
  // 更新光标位置
  setTimeout(() => {
    if (editorInstance) {
      editorInstance.setPosition({ lineNumber: start, column: end + keyword.length + 1 })
      editorInstance.focus()
    }
  }, 0)
}

// 在mounted钩子中设置初始高度
onMounted(() => {
  // 确保在组件挂载后始终能拿到最新内容
  const initialContent = window.__SQL_CONTENT__ || window.__REAL_SQL_CONTENT__ || props.modelValue || '';
  content.value = initialContent;
  
  console.log('SqlEditor: 组件挂载, 初始内容长度=', initialContent.length);
  console.log('SqlEditor: 初始内容预览=', initialContent.substring(0, 50) + (initialContent.length > 50 ? '...' : ''));
  
  // 强制先设置一次
  emit('update:modelValue', initialContent);
  
  // 添加自定义事件监听，用于外部强制更新内容
  document.addEventListener('sqlEditorFix', ((e: Event) => {
    const customEvent = e as CustomEvent;
    console.log('SqlEditor: 接收到sqlEditorFix事件');
    if (customEvent.detail && customEvent.detail.content) {
      const fixContent = customEvent.detail.content;
      console.log('SqlEditor: 从事件中接收内容，长度=', fixContent.length);
      setValue(fixContent);
    }
  }) as EventListener);
  
  // 确保DOM更新后初始化编辑器
  setTimeout(() => {
    if (editorContainer.value) {
      console.log('SqlEditor: 初始化Monaco编辑器');
      
      try {
        // 计算初始高度 - 设置为80px
        const initialHeight = props.height ?
          parseInt(props.height.replace('px', '')) :
          props.minHeight ? parseInt(props.minHeight.replace('px', '')) : 80;
        
        console.log('SqlEditor: 使用初始高度:', initialHeight, 'px');
        
        // 使用实际内容，不再添加默认文本
        const effectiveContent = initialContent || '';
        console.log('SqlEditor: 实际初始内容长度:', effectiveContent.length);
        
        editor = monaco.editor.create(editorContainer.value, {
          value: effectiveContent,
          language: props.language || 'sql',
          theme: props.theme || 'vs',
          automaticLayout: true,
          minimap: { enabled: false },
          readOnly: props.readonly,
          scrollBeyondLastLine: false,
          lineNumbers: 'off',
          wordWrap: 'on',
          autoIndent: 'full',
          formatOnPaste: true,
          fontSize: 14,
          lineHeight: 18,
          tabSize: 2,
          folding: false,
          contextmenu: true,
          useTabStops: true,
          selectOnLineNumbers: true,
          lineDecorationsWidth: 0,
          overviewRulerBorder: false,
          hideCursorInOverviewRuler: true,
          renderLineHighlight: 'none',
          scrollbar: {
            vertical: 'hidden',
            horizontal: 'auto',
            useShadows: false,
            verticalScrollbarSize: 0
          },
          suggest: {
            showWords: false
          },
          // 添加placeholder配置
          placeholder: props.placeholder || '请在此输入SQL查询语句'
        });
        
        // 重要：在编辑器初始化后立即获取值检查
        const contentAfterInit = editor.getValue();
        console.log('SqlEditor: 编辑器初始化后内容长度=', contentAfterInit.length);
        
        // 如果初始化后内容为空或不匹配，立即强制设置
        if (contentAfterInit.length === 0 || contentAfterInit !== effectiveContent) {
          console.log('SqlEditor: 内容不匹配，强制更新');
          editor.setValue(effectiveContent);
        }
        
        // 为编辑器添加内容变更监听
        editor.onDidChangeModelContent(() => {
          if (!editor) return;
          const value = editor.getValue();
          content.value = value;
          console.log('SqlEditor: 编辑器内容变化, 长度=', value?.length || 0);
          emit('update:modelValue', value);
          emit('change', value);
        });
        
        // 添加快捷键支持
        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
          if (!editor) return;
          console.log('SqlEditor: 触发执行查询快捷键, 内容长度=', editor.getValue().length);
          
          // 获取选中的文本
          const selection = editor.getSelection();
          let selectedText = '';
          if (selection && !selection.isEmpty()) {
            selectedText = editor.getModel()?.getValueInRange(selection) || '';
            console.log('SqlEditor: 快捷键检测到选中文本，长度=', selectedText.length);
          }
          
          // 如果没有选中文本，使用全部内容
          if (!selectedText || selectedText.trim() === '') {
            selectedText = editor.getValue() || '';
            console.log('SqlEditor: 快捷键使用全部内容，长度=', selectedText.length);
          }
          
          if (!selectedText || !selectedText.trim()) {
            emit('execute', '请在SQL编辑器中输入查询语句');
            return;
          }
          
          // 处理SQL参数格式
          const processedSql = processSQL(selectedText.trim());
          console.log('快捷键处理后的SQL:', processedSql);
          
          // 触发执行事件，传递选中的SQL内容
          emit('execute', undefined, processedSql);
        });
        
        // 设置初始高度
        editor.layout({ 
          width: editor.getLayoutInfo().width, 
          height: initialHeight 
        });
        
        // 强制刷新机制 - 对抗可能的渲染问题
        setTimeout(() => {
          if (editor) {
            try {
              // 触发布局更新
              console.log('SqlEditor: 500ms后强制刷新编辑器');
              
              // 检查内容是否一致
              const currentContent = editor.getValue();
              if (currentContent !== effectiveContent) {
                console.log('SqlEditor: 内容不一致，再次设置，当前=', currentContent.length, '期望=', effectiveContent.length);
                editor.setValue(effectiveContent);
              }
              
              // 触发重新布局
              editor.layout();
              editor.focus();
              
              // 重置编辑器位置
              editor.setPosition({ lineNumber: 1, column: 1 });
              
              // 强制滚动到顶部
              editor.setScrollTop(0);
              editor.setScrollLeft(0);
            } catch (err) {
              console.error('SqlEditor: 刷新编辑器时出错:', err);
            }
          }
        }, 500);
      } catch (err) {
        console.error('SqlEditor: 初始化编辑器时出错:', err);
      }
    }
  }, 0);

  // 组件卸载时清理事件监听
  onBeforeUnmount(() => {
    // 清理编辑器实例
    if (editor) {
      editor.dispose();
      editor = null;
    }
    
    // 清理事件监听
    document.removeEventListener('sqlEditorFix', () => {
      console.log('SqlEditor: 移除sqlEditorFix事件监听');
    });
  });
});

// 监听内容变化，调整高度
watchEffect(() => {
  if (editor) {
    // 使用nextTick确保DOM更新后再调整高度
    setTimeout(() => {
      if (editor) {
        adjustHeight(editor)
      }
    }, 0)
  }
})

// 自动调整高度函数
const adjustHeight = (editor: monaco.editor.IStandaloneCodeEditor) => {
  // 获取编辑器内容的实际高度
  const contentHeight = editor.getContentHeight();
  
  // 获取编辑器当前显示的行数
  const model = editor.getModel();
  if (!model) return;
  const lineCount = model.getLineCount();
  
  // 计算实际需要的高度：行数 * 行高(18px) + 4px边距
  const calculatedHeight = (lineCount * 18) + 4;
  
  // 计算编辑器高度，最小为80px，最大为500px
  const minHeightPx = props.minHeight ? parseInt(props.minHeight.replace('px', '')) : 80;
  const maxHeightPx = props.maxHeight ? parseInt(props.maxHeight.replace('px', '')) : 500;
  
  // 使用计算出的实际高度，并确保在范围内
  const targetHeight = Math.min(maxHeightPx, Math.max(minHeightPx, calculatedHeight));
  
  // 设置编辑器高度
  if (editor.getLayoutInfo().height !== targetHeight) {
    editor.layout({
      width: editor.getLayoutInfo().width,
      height: targetHeight
    });
  }
};

// 组件卸载时销毁编辑器
onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})

// 公开方法：获取编辑器内容
const getValue = () => {
  return editor ? editor.getValue() : ''
}

// 公开方法：设置编辑器内容
const setValue = (value: string) => {
  console.log('SqlEditor: 外部调用setValue, 内容长度=', value?.length || 0);
  
  // 先更新内部状态
  content.value = value || '';
  
  // 如果编辑器已初始化，设置编辑器内容
  if (editor) {
    try {
      const currentValue = editor.getValue();
      if (currentValue !== value) {
        console.log('SqlEditor: 更新编辑器内容，从', currentValue.length, '到', value.length);
        editor.setValue(value || '');
        // 强制更新布局
        editor.layout();
        // 设置光标位置到开头
        editor.setPosition({ lineNumber: 1, column: 1 });
      }
    } catch (err) {
      console.error('SqlEditor: 设置编辑器内容出错:', err);
    }
  } else {
    console.warn('SqlEditor: 编辑器尚未初始化，内容将在初始化后应用');
    // 存储到window全局，确保后续能拿到
    window.__SQL_CONTENT__ = value;
  }
}

// 添加额外的全局可访问函数 - 兜底解决方案
window.updateSqlEditor = (value: string): boolean => {
  const editorInstance = editor;
  if (editorInstance) {
    console.log('SqlEditor: 通过全局函数更新，内容长度=', value?.length || 0);
    editorInstance.setValue(value || '');
    return true;
  }
  return false;
};

// 公开方法：获取编辑器实例
const getEditor = () => {
  return editor
}

// 公开方法：聚焦编辑器
const focus = () => {
  if (editor) {
    editor.focus()
  }
}

// 公开方法：插入文本
const insertText = (text: string) => {
  if (editor) {
    const position = editor.getPosition()
    if (!position) return
    
    editor.executeEdits('', [
      {
        range: {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column
        },
        text: text
      }
    ])
    editor.focus()
  }
}

// 定义组件暴露的方法
defineExpose({
  getValue,
  setValue,
  getEditor,
  focus,
  insertText,
  // 添加获取选中文本的方法
  getSelectedText: () => {
    if (!editor) return '';
    const selection = editor.getSelection();
    if (selection && !selection.isEmpty()) {
      return editor.getModel()?.getValueInRange(selection) || '';
    }
    return '';
  }
})
</script>

<template>
  <div class="relative w-full overflow-hidden" :class="{ 'cursor-not-allowed': readonly }" :style="{ minHeight: minHeight || '80px' }">
    <div
      ref="editorContainer"
      class="w-full h-full"
      data-cy="sql-editor"
      :class="{ 'opacity-60': readonly }"
      :style="{ height: '100%' }"
    ></div>
    
    <!-- 添加调试文本（仅开发环境下显示） -->
    <div v-if="isDev" class="absolute bottom-0 right-0 bg-gray-100 p-1 text-xs text-gray-500">
      内容长度: {{ modelValue?.length || 0 }}字符
    </div>
  </div>
</template>