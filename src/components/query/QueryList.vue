<template>
  <!-- No changes to template section -->
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useQueryStore } from '../../stores/query';

const queryStore = useQueryStore();

const currentPage = ref(1);
const pageSize = ref(10);
const totalPages = ref(1);
const filters = ref({
  searchTerm: '',
  queryType: undefined,
  status: undefined,
  serviceStatus: undefined,
  dataSourceId: undefined
});
const isLoading = ref(false);

// 加载查询列表
const loadQueries = async () => {
  try {
    console.log('[QueryList] 开始加载查询列表', {
      当前页: currentPage.value,
      每页大小: pageSize.value,
      筛选条件: filters.value,
      当前分页状态: queryStore.pagination
    });
    
    isLoading.value = true;
    
    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      searchTerm: filters.value.searchTerm,
      queryType: filters.value.queryType,
      status: filters.value.status,
      serviceStatus: filters.value.serviceStatus,
      dataSourceId: filters.value.dataSourceId
    };
    
    console.log('[QueryList] 调用fetchQueries，参数:', params);
    
    // 调用store方法获取查询列表
    await queryStore.fetchQueries(params);
    
    console.log('[QueryList] fetchQueries完成，当前分页状态:', {
      当前页: queryStore.pagination.page,
      每页大小: queryStore.pagination.pageSize,
      总记录数: queryStore.pagination.total,
      总页数: queryStore.pagination.totalPages,
      是否可以下一页: queryStore.pagination.hasMore
    });
  } catch (error) {
    console.error('[QueryList] 加载查询列表失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 上一页
const prevPage = () => {
  console.log('[QueryList] 点击上一页，当前页:', currentPage.value);
  if (currentPage.value > 1) {
    currentPage.value--;
    loadQueries();
  }
};

// 下一页
const nextPage = () => {
  console.log('[QueryList] 点击下一页，当前页:', currentPage.value);
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    loadQueries();
  }
};

// 跳转到指定页
const goToPage = (page: number) => {
  console.log('[QueryList] 跳转到指定页:', page);
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    loadQueries();
  }
};

// 应用筛选条件
const applyFilters = () => {
  console.log('[QueryList] 应用筛选条件:', filters.value);
  currentPage.value = 1; // 重置到第一页
  loadQueries();
};

// 清除筛选条件
const clearFilters = () => {
  console.log('[QueryList] 清除筛选条件');
  filters.value = {
    searchTerm: '',
    queryType: undefined,
    status: undefined,
    serviceStatus: undefined,
    dataSourceId: undefined
  };
  currentPage.value = 1; // 重置到第一页
  loadQueries();
};

// 组件挂载时加载数据
onMounted(() => {
  console.log('[QueryList] 组件开始挂载');
  loadQueries();
  console.log('[QueryList] 组件挂载完成');
});
</script>

<style>
  /* No changes to style section */
</style> 