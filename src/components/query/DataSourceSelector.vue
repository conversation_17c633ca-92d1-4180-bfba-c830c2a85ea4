<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { NSelect, NSpace, NButton, NIcon, NTooltip } from 'naive-ui'
import { http } from '@/utils/http'
import { getApiUrl } from '@/services/apiUtils'
import { RefreshSharp } from '@vicons/ionicons5'
import type { DataSource } from '@/types/metadata'
import instance from "@/utils/axios";

// 定义组件属性
const props = defineProps<{
  currentDataSourceId: string
  disabled?: boolean
}>()

// 定义事件
const emits = defineEmits<{
  'update:datasource': [dataSourceId: string]
  'error': [error: { type: string; message: string }]
}>()

// 状态变量
const dataSources = ref<DataSource[]>([])
const isLoading = ref(false)
const searchQuery = ref('')

// 计算过滤后的数据源
const filteredDataSources = computed(() => {
  if (!searchQuery.value) return dataSources.value
  const query = searchQuery.value.toLowerCase()
  return dataSources.value.filter(
    ds => ds.name.toLowerCase().includes(query) ||
          ds.id.toLowerCase().includes(query) ||
          ds.type.toLowerCase().includes(query)
  )
})

// 选项列表
const dataSourceOptions = computed(() => {
  return filteredDataSources.value.map(ds => ({
    label: `${ds.name} (${ds.type})`,
    value: ds.id,
    key: ds.id
  }))
})

// 加载数据源方法
const loadDataSources = async () => {
  isLoading.value = true
  try {
    const url = getApiUrl() + '/metadata/datasources'
    const response = await instance.get(url)
    dataSources.value = response.data.dataSources || []
  } catch (err: any) {
    console.error('加载数据源失败:', err)
    emits('error', {
      type: 'error',
      message: `无法加载数据源: ${err.message || '未知错误'}`
    })
    dataSources.value = []
  } finally {
    isLoading.value = false
  }
}

// 处理刷新按钮点击
const handleRefresh = () => {
  loadDataSources()
}

// 处理数据源变更
const handleDataSourceChange = (value: string) => {
  emits('update:datasource', value)
}

// 组件挂载后加载数据
onMounted(() => {
  loadDataSources()
})
</script>

<template>
  <div class="datasource-selector">
    <NSpace justify="space-between" align="center">
      <label class="block text-sm font-medium mb-2">选择数据源</label>
      <NTooltip>
        <template #trigger>
          <NButton
            tertiary
            circle
            size="small"
            @click="handleRefresh"
            :loading="isLoading"
            :disabled="disabled"
          >
            <NIcon>
              <RefreshSharp />
            </NIcon>
          </NButton>
        </template>
        刷新数据源
      </NTooltip>
    </NSpace>

    <NSelect
      v-model:value="currentDataSourceId"
      filterable
      placeholder="选择一个数据源"
      :options="dataSourceOptions"
      :loading="isLoading"
      :disabled="disabled || isLoading"
      @update:value="handleDataSourceChange"
      :filter="(pattern, option) => option.label.toLowerCase().includes(pattern.toLowerCase())"
      clearable
    />
  </div>
</template>

<style scoped>
.datasource-selector {
  width: 100%;
}
</style>
