<template>
  <div v-if="open" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景蒙层 -->
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- 对话框内容 -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
              <i class="fas fa-list-alt text-blue-600"></i>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">填写查询参数</h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500 mb-4">
                  请为查询设置以下参数值
                </p>

                <!-- 无参数提示 -->
                <div v-if="!parameters || parameters.length === 0" class="py-4 text-center text-gray-500">
                  未发现查询参数
                </div>

                <!-- 参数输入表单 -->
                <div v-else class="space-y-4 mt-4">
                  <div v-for="param in parameters" :key="param.id || param.name" class="mb-4">
                    <label :for="`param-${param.name}`" class="block text-sm font-medium text-gray-700 mb-1">
                      {{ param.label || param.name }}
                      <span v-if="param.required" class="text-red-500">*</span>
                    </label>

                    <!-- 字符串类型 -->
                    <input 
                      v-if="param.type === 'string'"
                      :id="`param-${param.name}`"
                      v-model="paramValues[param.name]"
                      type="text"
                      class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border"
                      :placeholder="`输入${param.label || param.name}`"
                      :required="param.required"
                    />

                    <!-- 数字类型 -->
                    <input 
                      v-else-if="param.type === 'number'"
                      :id="`param-${param.name}`"
                      v-model.number="paramValues[param.name]"
                      type="number"
                      class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border"
                      :placeholder="`输入${param.label || param.name}`"
                      :required="param.required"
                    />

                    <!-- 布尔类型 -->
                    <div v-else-if="param.type === 'boolean'" class="mt-1">
                      <div class="flex items-center">
                        <input 
                          :id="`param-${param.name}-true`"
                          type="radio"
                          :name="`param-${param.name}`"
                          :value="true"
                          v-model="paramValues[param.name]"
                          class="h-4 w-4 text-indigo-600 border-gray-300"
                        />
                        <label :for="`param-${param.name}-true`" class="ml-2 block text-sm text-gray-700">是</label>
                      </div>
                      <div class="flex items-center mt-1">
                        <input 
                          :id="`param-${param.name}-false`"
                          type="radio"
                          :name="`param-${param.name}`"
                          :value="false"
                          v-model="paramValues[param.name]"
                          class="h-4 w-4 text-indigo-600 border-gray-300"
                        />
                        <label :for="`param-${param.name}-false`" class="ml-2 block text-sm text-gray-700">否</label>
                      </div>
                    </div>

                    <!-- 日期类型 -->
                    <input 
                      v-else-if="param.type === 'date'"
                      :id="`param-${param.name}`"
                      v-model="paramValues[param.name]"
                      type="date"
                      class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border"
                      :required="param.required"
                    />

                    <!-- 默认文本输入 -->
                    <input 
                      v-else
                      :id="`param-${param.name}`"
                      v-model="paramValues[param.name]"
                      type="text"
                      class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border"
                      :placeholder="`输入${param.label || param.name}`"
                      :required="param.required"
                    />

                    <!-- 参数描述 -->
                    <p v-if="param.description" class="mt-1 text-xs text-gray-500">
                      {{ param.description }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            @click="handleConfirm"
            type="button"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            确认
          </button>
          <button 
            @click="handleCancel"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import type { PropType } from 'vue';
import type { QueryParameter } from '@/types/query';

// 定义组件属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  parameters: {
    type: Array as PropType<QueryParameter[]>,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
  (e: 'confirm', values: Record<string, any>): void
  (e: 'cancel'): void
}>();

// 参数值集合
const paramValues = ref<Record<string, any>>({});

// 初始化参数值
onMounted(() => {
  console.log('参数对话框已挂载，参数:', props.parameters);
  initParamValues();
});

// 监听参数变化，重新初始化值
watch(() => props.parameters, (newParams) => {
  console.log('参数列表已更新:', newParams);
  initParamValues();
}, { deep: true });

// 监听对话框状态
watch(() => props.open, (isOpen) => {
  console.log('参数对话框显示状态:', isOpen);
  if (isOpen) {
    initParamValues();
  }
});

// 初始化参数值方法
const initParamValues = () => {
  const values: Record<string, any> = {};
  
  // 初始化每个参数的默认值
  (props.parameters || []).forEach(param => {
    if (param.name) {
      // 如果已有值，保留现有值
      if (paramValues.value[param.name] !== undefined) {
        values[param.name] = paramValues.value[param.name];
      }
      // 否则使用参数的默认值，如果有的话
      else if (param.defaultValue !== undefined) {
        values[param.name] = param.defaultValue;
      }
      // 否则根据类型设置默认值
      else {
        switch (param.type) {
          case 'string':
            values[param.name] = '';
            break;
          case 'number':
            values[param.name] = null;
            break;
          case 'boolean':
            values[param.name] = false;
            break;
          case 'date':
            values[param.name] = new Date().toISOString().split('T')[0];
            break;
          case 'datetime':
            values[param.name] = new Date().toISOString().substring(0, 16);
            break;
          case 'time':
            values[param.name] = new Date().toISOString().substring(11, 16);
            break;
          case 'array':
            values[param.name] = [];
            break;
          case 'object':
            values[param.name] = {};
            break;
          default:
            values[param.name] = '';
        }
      }
    }
  });
  
  paramValues.value = values;
  console.log('初始化参数值:', values);
};

// 确认按钮
const handleConfirm = () => {
  // 检查必填参数是否已填写
  const missingRequired = (props.parameters || []).find(param => 
    param.required && 
    (paramValues.value[param.name] === undefined || 
     paramValues.value[param.name] === null || 
     (typeof paramValues.value[param.name] === 'string' && paramValues.value[param.name].trim() === ''))
  );
  
  // 如果有必填参数未填写，提示用户
  if (missingRequired) {
    alert(`请填写必填参数: ${missingRequired.label || missingRequired.name}`);
    return;
  }
  
  // 处理参数值类型转换
  const processedValues = { ...paramValues.value };
  
  // 根据参数类型转换值
  props.parameters.forEach(param => {
    if (param.name && processedValues[param.name] !== undefined && processedValues[param.name] !== null) {
      // 针对特定参数名做特殊处理
      if (param.name === 'startDate' || param.name === 'endDate') {
        // 确保日期参数格式正确
        if (param.type === 'date' && typeof processedValues[param.name] === 'string') {
          // 如果是空字符串，设置为null以触发SQL中的IS NULL条件
          if (processedValues[param.name].trim() === '') {
            processedValues[param.name] = null;
          }
          // 否则保持日期字符串格式
        } 
      } else if (param.name === 'name') {
        // 确保name参数正确处理
        if (typeof processedValues[param.name] === 'string') {
          // 如果是空字符串，设置为null以触发SQL中的IS NULL条件
          if (processedValues[param.name].trim() === '') {
            processedValues[param.name] = null;
          }
          // 否则保持字符串格式
        }
      } else if (param.type === 'number' && typeof processedValues[param.name] === 'string') {
        // 通用数字类型转换
        const num = parseFloat(processedValues[param.name]);
        if (!isNaN(num)) {
          processedValues[param.name] = num;
        }
      } else if (param.type === 'boolean' && typeof processedValues[param.name] === 'string') {
        // 通用布尔类型转换
        const value = processedValues[param.name].toLowerCase();
        if (value === 'true') processedValues[param.name] = true;
        else if (value === 'false') processedValues[param.name] = false;
      }
    }
  });
  
  console.log('处理后的参数值:', processedValues);
  
  // 发送确认事件并带上参数值
  emit('confirm', processedValues);
  
  // 关闭对话框
  emit('update:open', false);
};

// 取消按钮
const handleCancel = () => {
  emit('cancel');
  emit('update:open', false);
};
</script>

<style scoped>
/* 确保对话框始终显示在最上层 */
.z-50 {
  z-index: 9999 !important;
}

/* 为表单控件添加统一样式 */
input, select {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  width: 100%;
}

input:focus, select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 1px #4f46e5;
}

/* 必填字段标记样式 */
.text-red-500 {
  color: #ef4444;
}
</style> 