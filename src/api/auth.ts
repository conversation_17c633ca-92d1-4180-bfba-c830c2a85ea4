import http from '@/utils/http';
import { authConfig } from '@/utils/config';
import instance from '@/utils/axios';
import type { BaseResponse } from '@/types/common';
import type {
  GetUserProfileResponse,
  AuthRequest,
  AuthRequestResponse,
  UserPermissionResponse,
  AuthRequestType
} from '@/types/auth';
import type { GetInterfacePermissionResponse } from '@/types/permission';

/**
 * 获取用户信息
 */
export async function getUserProfile(): Promise<GetUserProfileResponse> {
  try {
    const response = await http.post(authConfig.getUserProfileApiPath);
    return response.data;
  } catch (error) {
    throw error;
  }
}
/**
 * 获取接口权限列表
 */
export async function getInterfacePermission(): Promise<GetInterfacePermissionResponse> {
  try {
    const response = await http.post(authConfig.getInterfacePermissionApiPath, {
      headers: {
        'systemcode': 'boss-data-scope2',
      },
    });
    return {
      ...response.data,
      data: response.data.data.reduce((_: any, next: any) => {
        if (next.functionStatus === 'ENABLED') {
          _.push(next);
        }
        return _;
      }, [])
    };
  } catch (error) {
    throw error;
  }
}
/**
 * 退出登录
 */
export async function logout(): Promise<BaseResponse> {
  try {
    const response = await http.get(authConfig.logoutApiPath);
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 查询用户对特定资源的权限状态
 */
export async function getUserPermissionStatus(
  resourceType: AuthRequestType,
  resourceId: string
): Promise<UserPermissionResponse> {
  try {
    const response = await instance.get(`/api/auth/permissions/status`, {
      params: {
        resourceType,
        resourceId
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 申请权限
 */
export async function requestPermission(request: AuthRequest): Promise<AuthRequestResponse> {
  try {
    const response = await instance.post('/api/auth/permissions/request', request);
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取用户的权限申请列表
 */
export async function getUserAuthRequests(params?: {
  page?: number;
  size?: number;
  status?: string;
  resourceType?: AuthRequestType;
}): Promise<BaseResponse> {
  try {
    const response = await instance.get('/api/auth/permissions/requests', { params });
    return response.data;
  } catch (error) {
    throw error;
  }
}
