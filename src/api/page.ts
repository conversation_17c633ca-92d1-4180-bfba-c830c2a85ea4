import instance from '@/utils/axios';
import type { Page, PageQueryParams } from '@/stores/page';

// API路径前缀
const API_PREFIX = '/api/pages';

/**
 * 获取页面列表
 */
export const getPageList = (params: PageQueryParams) => {
  return instance.get(API_PREFIX, { params });
};

/**
 * 获取单个页面详情
 */
export const getPageById = (id: string) => {
  return instance.get(`${API_PREFIX}/${id}`);
};

/**
 * 获取页面配置
 */
export const getPageConfig = (id: string) => {
  return instance.get(`${API_PREFIX}/${id}/config`);
};

/**
 * 创建页面
 */
export const createPage = (data: Partial<Page>) => {
  return instance.post(API_PREFIX, data);
};

/**
 * 更新页面
 */
export const updatePage = (id: string, data: Partial<Page>) => {
  return instance.put(`${API_PREFIX}/${id}`, data);
};

/**
 * 删除页面
 */
export const deletePage = (id: string) => {
  return instance.delete(`${API_PREFIX}/${id}`);
};

/**
 * 获取页面标签列表
 */
export const getPageTags = () => {
  return instance.get(`${API_PREFIX}/tags`);
};

/**
 * 导出页面配置
 */
export const exportPageConfig = (id: string) => {
  return instance.get(`${API_PREFIX}/${id}/export`);
};

/**
 * 导入页面配置
 */
export const importPageConfig = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  return instance.post(`${API_PREFIX}/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export default {
  getPageList,
  getPageById,
  getPageConfig,
  createPage,
  updatePage,
  deletePage,
  getPageTags,
  exportPageConfig,
  importPageConfig
};
