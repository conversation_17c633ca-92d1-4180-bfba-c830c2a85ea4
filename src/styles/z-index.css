/**
 * 全局z-index管理
 * 这个文件集中管理所有组件的z-index值，避免层级冲突
 */

:root {
  /* 基础层级 */
  --z-negative: -1;       /* 用于需要放置在内容下方的元素 */
  --z-zero: 0;            /* 默认层级 */
  --z-low: 10;            /* 低层级元素，如背景装饰 */
  --z-mid: 100;           /* 中层元素，如固定页头、页脚 */
  --z-high: 1000;         /* 高层元素，如浮动操作按钮 */
  --z-highest: 2000;      /* 最高层元素 */
  
  /* 导航和布局层级 */
  --z-header: 100;        /* 页头 */
  --z-sidebar: 200;       /* 侧边栏 */
  --z-footer: 100;        /* 页脚 */
  --z-sticky: 300;        /* 固定元素 */
  
  /* 下拉和弹出层级 */
  --z-dropdown: 1000;     /* 下拉菜单 */
  --z-dropdown-backdrop: 990; /* 下拉菜单背景 */
  --z-popover: 1100;      /* 弹出提示 */
  --z-tooltip: 1200;      /* 工具提示 */
  
  /* 对话框和遮罩层级 */
  --z-modal-backdrop: 1900;  /* 模态框背景遮罩 */
  --z-modal: 2000;        /* 模态框 */
  --z-modal-content: 2100; /* 模态框内容 */
  --z-modal-dropdown: 2200; /* 模态框内下拉菜单 */
  
  /* 消息和通知层级 */
  --z-notification: 3000; /* 通知提醒 */
  --z-message: 3100;      /* 全局消息 */
  
  /* loading层级，设置在notification下、modal上 */
  --z-loading: 1950;      /* 全局loading遮罩 */
  
  /* 开发调试层级 */
  --z-dev-tools: 9000;    /* 开发工具 */
  --z-max: 9999;          /* 最大z-index值 */
}

/* ==== 应用到具体组件 ==== */

/* 导航和布局 */
.app-header,
.sticky-header,
header.sticky {
  z-index: var(--z-header) !important;
}

.app-sidebar,
.app-drawer {
  z-index: var(--z-sidebar) !important;
}

.app-footer {
  z-index: var(--z-footer) !important;
}

/* 下拉菜单 */
.dropdown {
  z-index: var(--z-dropdown) !important;
}

.dropdown-backdrop {
  z-index: var(--z-dropdown-backdrop) !important;
}

/* 表单元素和选择器 */
select {
  z-index: var(--z-low) !important;
  /* 全局添加箭头图标到所有select元素 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
  background-position: right 0.5rem center !important;
  background-repeat: no-repeat !important;
  background-size: 1.5em 1.5em !important;
  padding-right: 2.5rem !important;
  appearance: none !important;
  position: relative !important;
}

select:focus, 
select:active {
  z-index: var(--z-notification) !important; /* 使用与notification相同的层级，高于loading */
}

/* 弹出提示 */
.tooltip {
  z-index: var(--z-tooltip) !important;
}

.popover {
  z-index: var(--z-popover) !important;
}

/* 模态框 */
.modal-backdrop,
.dialog-backdrop {
  z-index: var(--z-modal-backdrop) !important;
}

.modal,
.dialog {
  z-index: var(--z-modal) !important;
}

.modal-content,
.dialog-content {
  z-index: var(--z-modal-content) !important;
}

/* 模态框内的下拉菜单应该有更高的层级 */
.modal select:focus,
.modal .dropdown,
.dialog select:focus,
.dialog .dropdown {
  z-index: var(--z-modal-dropdown) !important;
}

/* 消息和通知 */
.notification,
.notification-container {
  z-index: var(--z-notification) !important;
}

.message,
.message-container {
  z-index: var(--z-message) !important;
}

/* Loading遮罩 */
div[class*="loading"],
.loading-overlay,
.loading-container {
  z-index: var(--z-loading) !important;
}

/* Ant Design Vue和其他UI库组件覆盖 */
.ant-select-dropdown {
  z-index: var(--z-notification) !important; /* 确保下拉菜单在loading之上 */
}

.ant-message {
  z-index: var(--z-message) !important;
}

.ant-notification {
  z-index: var(--z-notification) !important;
}

/* 在模态框中的Ant Design组件 */
.modal .ant-select-dropdown,
.dialog .ant-select-dropdown {
  z-index: var(--z-modal-dropdown) !important;
}

/* 表格配置中的下拉菜单 */
.field-selector {
  z-index: var(--z-low) !important;
}

.field-selector:focus-within, 
.field-selector:active {
  z-index: var(--z-notification) !important; /* 使用与notification相同的层级 */
}

/* 模态框中的表格配置下拉菜单 */
.modal .field-selector:focus-within,
.dialog .field-selector:focus-within {
  z-index: var(--z-modal-dropdown) !important;
}

/* 修复固定在底部的操作栏 */
.fixed-action-bar {
  z-index: var(--z-high) !important;
}