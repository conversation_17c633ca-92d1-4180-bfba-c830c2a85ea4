@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  /* 数据范围系统的输入框样式规范 */
  .ds-input,
  .ds-select {
    @apply block w-full rounded-md;
    border: 1px solid #d1d5db !important;
    transition:
      border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;
  }

  .ds-input:focus,
  .ds-select:focus {
    @apply outline-none ring-2 ring-indigo-500 border-indigo-500;
  }

  /* 修复禁用输入框背景色问题 */
  .form-input:disabled,
  .form-select:disabled,
  input[type="text"]:disabled,
  input[type="number"]:disabled,
  input:not([type]):disabled,
  select:disabled,
  textarea:disabled {
    background-color: #f3f4f6 !important;
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    border-color: #d1d5db !important;
  }

  /* 下拉框箭头样式 */
  .ds-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    appearance: none;
  }

  /* 全局强制添加箭头图标到所有的select元素 - 已移除重复定义 */
  /* 避免重复定义，移至z-index.css文件中统一管理 */

  select:active {
    /* 提高聚焦时的z-index，确保下拉项不会被loading遮罩覆盖 */
    z-index: var(--z-notification) !important;
  }

  /* 表格配置下拉菜单容器样式 */
  .field-selector {
    position: relative;
    z-index: 5;
  }

  .field-selector:focus-within,
  .field-selector:active {
    z-index: 55;
  }

  /* 修复bg-white中的下拉菜单被截断问题 */
  .bg-white {
    position: relative;
    overflow: visible !important;
  }

  /* 修复容器中的溢出隐藏 */
  .overflow-hidden select,
  .overflow-hidden .ds-select,
  .overflow-hidden .field-selector {
    position: relative;
    overflow: visible !important;
  }

  /* 修复表格中下拉菜单在折叠后无法显示问题 */
  .overflow-x-auto {
    overflow-y: visible !important;
  }

  /* 解决自定义下拉箭头的双箭头问题 */
  select + .absolute .fa-chevron-down,
  .ds-select + .absolute .fa-chevron-down,
  .select-container .fa-chevron-down {
    /* 确保自定义箭头样式正确 */
    pointer-events: none;
    transition: transform 0.2s ease-in-out;
  }

  /* 确保鼠标悬停或点击时箭头正确显示并旋转 */
  select:hover + .absolute .fa-chevron-down,
  .ds-select:hover + .absolute .fa-chevron-down,
  select:focus + .absolute .fa-chevron-down,
  .ds-select:focus + .absolute .fa-chevron-down,
  .select-container:hover .fa-chevron-down,
  .select-container:focus-within .fa-chevron-down {
    opacity: 1;
    visibility: visible;
    transform: rotate(180deg);
    color: #4f46e5;
  }

  /* 让箭头在下拉框打开时旋转 */
  select:focus + .absolute .fa-chevron-down,
  .ds-select:focus + .absolute .fa-chevron-down,
  .select-open + .absolute .fa-chevron-down,
  .active + .absolute .fa-chevron-down,
  .is-open + .absolute .fa-chevron-down {
    transform: rotate(-180deg);
  }

  /* Select下拉状态选择器样式 */
  select.active ~ .absolute .fa-chevron-down,
  .select-wrapper.active .fa-chevron-down,
  [aria-expanded="true"] ~ .absolute .fa-chevron-down {
    transform: rotate(-180deg);
  }

  /* 使用transform类时箭头旋转 */
  .fa-chevron-down.rotate-180,
  .fa-chevron-down.transform.rotate-180 {
    transform: rotate(-180deg);
  }

  /* 当使用FontAwesome箭头时隐藏原生下拉箭头 */
  select.hide-native-arrow,
  .has-custom-arrow select {
    background-image: none !important;
  }

  /* 确保绝对定位的元素在select内部时正确显示 */
  .select-wrapper {
    position: relative;
  }

  .select-wrapper .absolute {
    pointer-events: none;
  }
}

/* 修复下拉菜单被遮挡问题 */
.dropdown-menu-container {
  position: relative;
  z-index: 10;
}

/* 增强下拉菜单容器的可见性 */
.dropdown-menu-container:hover,
.dropdown-menu-container:focus-within {
  z-index: 1000 !important;
}

.dropdown-content {
  position: absolute;
  z-index: 100;
  background-color: white;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

/* 修复select下拉框层级问题 */
select,
.ds-select {
  position: relative;
  z-index: 10;
}

select:focus,
.ds-select:focus,
select:hover,
.ds-select:hover {
  z-index: var(
    --z-notification
  ) !important; /* 增加到notification层级使其高于loading遮罩 */
}

/* 优化下拉菜单父容器 */
div:has(> select),
div:has(> .ds-select) {
  overflow: visible !important;
}

/* 当鼠标悬停或聚焦时改变容器的状态 */
.select-container:hover .fa-chevron-down,
.select-container:focus-within .fa-chevron-down,
.relative:has(select:hover) .fa-chevron-down,
.relative:has(select:focus) .fa-chevron-down {
  color: #4f46e5; /* 改变颜色为紫色(indigo) */
  transform: rotate(180deg); /* 旋转180度 */
}

/* 点击状态 - 添加更明确的交互 */
select:active + .absolute .fa-chevron-down,
.select-container:active .fa-chevron-down {
  transform: rotate(180deg);
  color: #4f46e5;
}

/* 带图标输入框的统一样式规范 */

/* 前置图标容器 */
.input-icon-left,
.absolute.inset-y-0.left-0.flex.items-center.pointer-events-none,
.absolute.inset-y-0.left-0.pl-3.flex.items-center.pointer-events-none {
  position: absolute !important;
  top: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  z-index: 2 !important;
  display: flex !important;
  align-items: center !important;
  padding-left: 0.75rem !important;
  pointer-events: none !important;
}

/* 后置图标容器 */
.input-icon-right,
.absolute.inset-y-0.right-0.flex.items-center {
  position: absolute !important;
  top: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  z-index: 2 !important;
  display: flex !important;
  align-items: center !important;
  padding-right: 0.75rem !important;
}

/* 带前置图标的输入框 */
.input-with-left-icon,
input.pl-10,
input.pl-8 {
  padding-left: 2.5rem !important;
}

/* 带后置图标的输入框 */
.input-with-right-icon,
input.pr-10,
input.pr-8 {
  padding-right: 2.5rem !important;
}

/* 带前后图标的输入框 */
.input-with-both-icons {
  padding-left: 2.5rem !important;
  padding-right: 2.5rem !important;
}

/* 可点击的后置图标（如清除按钮） */
.input-icon-clickable {
  pointer-events: auto !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;
}

.input-icon-clickable:hover {
  color: #374151 !important;
}

/* 隐藏Font Awesome箭头 */
.absolute.inset-y-0.right-0.flex.items-center.px-2.pointer-events-none {
  display: none !important;
}

/* 只隐藏下拉框中的Font Awesome chevron图标 */
select + .absolute .fa-chevron-down,
.ds-select + .absolute .fa-chevron-down,
.select-container .fa-chevron-down {
  display: none !important;
}

/* 强化按钮禁用状态的视觉效果 */
button:disabled,
.btn:disabled,
input[type="button"]:disabled,
input[type="submit"]:disabled,
.a-button-disabled,
.el-button.is-disabled,
.ant-btn-disabled,
.ant-btn[disabled] {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  filter: grayscale(40%) !important;
  pointer-events: none !important;
}

/* 增强主色调按钮禁用状态区分度 */
button.bg-indigo-600:disabled,
button.bg-blue-600:disabled,
button.bg-primary:disabled,
.btn-primary:disabled,
.el-button--primary.is-disabled,
.ant-btn-primary[disabled] {
  background-color: #a5b4fc !important; /* 使用更浅的颜色 */
  border-color: #a5b4fc !important;
  color: white !important;
  opacity: 0.65 !important;
}
