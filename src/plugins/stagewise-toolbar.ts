/**
 * Stagewise Toolbar 集成
 *
 * 该文件负责初始化和配置 @stagewise/toolbar，仅在开发环境中加载
 */

import {initToolbar} from '@stagewise/toolbar';

/**
 * Stagewise Toolbar 配置
 */
const stagewiseConfig = {
    plugins: [
        {
            name: 'example-plugin',
            description: '为组件添加额外上下文信息',
            shortInfoForPrompt: () => {
                return "关于所选元素的上下文信息";
            },
            mcp: null,
            actions: [
                {
                    name: '示例操作',
                    description: '演示自定义操作',
                    execute: () => {
                        window.alert('这是一个自定义操作!');
                    },
                },
            ],
        },
    ],
};

/**
 * 初始化 Stagewise Toolbar
 * 仅在开发环境中加载
 */
export function setupStagewise() {
    // 只在开发环境中初始化
    if (process.env.NODE_ENV === 'development') {
        console.log('[Stagewise] 正在开发环境中初始化 Stagewise Toolbar');
        initToolbar(stagewiseConfig);
    }
}

export default setupStagewise;
