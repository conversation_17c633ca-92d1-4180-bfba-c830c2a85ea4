import { getInterfacePermission } from '@/api/auth';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const usePermission = defineStore('permission', () => {
  const permissionList = ref<{
    functionName: string
    functionStatus: 'ENABLE' | 'DISABLE';
    functionStatusName: string;
    id: number;
    method: '';
    systemCode: string;
    timeRuleValid: boolean;
    url: string;
  }[]>([]);

  const hasBeenFetched = ref(false)

  async function fetchInterfacePermission() {
    try {
      const response = await getInterfacePermission();
      permissionList.value = response.data;
      hasBeenFetched.value = true
    } catch (error) {
      console.error('获取接口权限失败:', error);
    }
  }

  function hasPermission(permissionUrl: string | string[]) {
    if (Array.isArray(permissionUrl)) {
      return permissionUrl.every(p => permissionList.value.some(item => item.url === p));
    }
    return permissionList.value.some(item => item.url === permissionUrl);
  }

  return {
    fetchInterfacePermission,
    hasPermission,
    hasBeenFetched,
  }
});
