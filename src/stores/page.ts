import { defineStore } from 'pinia';
import { ref } from 'vue';
import * as pageApi from '@/api/page';

export interface Page {
  id: string;
  title: string;
  description?: string;
  type: 'data' | 'analysis' | 'management';
  status: 'active' | 'inactive' | 'draft';
  tags?: string[];
  thumbnail?: string | null;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

export interface PageConfig {
  meta: {
    id: string;
    title: string;
    description?: string;
    [key: string]: any;
  };
  filter?: any;
  list?: any;
  chart?: any;
  [key: string]: any;
}

export interface PageQueryParams {
  page?: number;
  size?: number;
  keyword?: string;
  type?: string;
  status?: string;
}

export const usePageStore = defineStore('page', () => {
  // 状态
  const pages = ref<Page[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const total = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);
  
  // 获取页面列表
  const fetchPageList = async (params?: PageQueryParams) => {
    loading.value = true;
    error.value = null;
    
    try {
      const apiParams = {
        page: params?.page || currentPage.value,
        size: params?.size || pageSize.value,
        ...params
      };
      
      const response = await pageApi.getPageList(apiParams);
      
      if (response) {
        pages.value = response.items || [];
        total.value = response.total || 0;
        currentPage.value = apiParams.page;
        pageSize.value = apiParams.size;
      } else {
        pages.value = [];
        total.value = 0;
      }
      
      return pages.value;
    } catch (err) {
      console.error('Failed to fetch page list:', err);
      error.value = err instanceof Error ? err.message : '获取页面列表失败';
      pages.value = [];
      total.value = 0;
      
      // 临时提供模拟数据，以便界面展示
      if (process.env.NODE_ENV === 'development') {
        console.log('使用开发环境下的模拟数据');
        pages.value = [
          {
            id: '1',
            title: '销售数据分析',
            description: '销售部门数据分析与可视化展示',
            type: 'data',
            status: 'active',
            tags: ['销售', '数据', '图表']
          },
          {
            id: '2',
            title: '用户行为分析',
            description: '用户行为数据分析与用户画像',
            type: 'analysis',
            status: 'active',
            tags: ['用户', '行为', '分析']
          },
          {
            id: '3',
            title: '系统配置管理',
            description: '系统参数与配置管理页面',
            type: 'management',
            status: 'inactive',
            tags: ['系统', '配置', '管理']
          }
        ];
        total.value = pages.value.length;
      }
      
      return pages.value;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取单个页面
  const fetchPageById = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await pageApi.getPageById(id);
      return response as Page;
    } catch (err) {
      console.error(`Failed to fetch page ${id}:`, err);
      error.value = err instanceof Error ? err.message : `获取页面${id}失败`;
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取页面配置
  const fetchPageConfig = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await pageApi.getPageConfig(id);
      return response as PageConfig;
    } catch (err) {
      console.error(`Failed to fetch page config ${id}:`, err);
      error.value = err instanceof Error ? err.message : `获取页面配置${id}失败`;
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 创建页面
  const createPage = async (pageData: Partial<Page>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await pageApi.createPage(pageData);
      return response;
    } catch (err) {
      console.error('Failed to create page:', err);
      error.value = err instanceof Error ? err.message : '创建页面失败';
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 更新页面
  const updatePage = async (id: string, pageData: Partial<Page>) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await pageApi.updatePage(id, pageData);
      return response;
    } catch (err) {
      console.error(`Failed to update page ${id}:`, err);
      error.value = err instanceof Error ? err.message : `更新页面${id}失败`;
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 删除页面
  const deletePage = async (id: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await pageApi.deletePage(id);
      return response;
    } catch (err) {
      console.error(`Failed to delete page ${id}:`, err);
      error.value = err instanceof Error ? err.message : `删除页面${id}失败`;
      return null;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取页面标签
  const fetchPageTags = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await pageApi.getPageTags();
      return response;
    } catch (err) {
      console.error('Failed to fetch page tags:', err);
      error.value = err instanceof Error ? err.message : '获取页面标签失败';
      return [];
    } finally {
      loading.value = false;
    }
  };
  
  return {
    // 状态
    pages,
    loading,
    error,
    total,
    currentPage,
    pageSize,
    
    // 方法
    fetchPageList,
    fetchPageById,
    fetchPageConfig,
    createPage,
    updatePage,
    deletePage,
    fetchPageTags
  };
});