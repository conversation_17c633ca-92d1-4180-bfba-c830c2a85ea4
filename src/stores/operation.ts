import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  Operation,
  OperationExecution,
  OperationQueryParams,
  CreateOperationParams,
  UpdateOperationParams,
  Application
} from '@/types/operation';
import { operationService } from '@/services/operationService';
import { useMessageService } from '@/services/message';
import { loading } from '@/services/loading';

export const useOperationStore = defineStore('operation', () => {
  // 状态
  const operations = ref<Operation[]>([]);
  const currentOperation = ref<Operation | null>(null);
  const executionHistory = ref<OperationExecution[]>([]);
  const applications = ref<Application[]>([]);
  const currentApplication = ref<Application | null>(null);
  const isLoading = ref(false);
  const error = ref<Error | null>(null);
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0
  });

  // 获取消息服务
  const messageService = useMessageService();

  // 计算属性
  const operationsByStatus = computed(() => {
    const result = {
      DRAFT: operations.value.filter(op => op.status === 'DRAFT'),
      ACTIVE: operations.value.filter(op => op.status === 'ACTIVE'),
      INACTIVE: operations.value.filter(op => op.status === 'INACTIVE')
    };
    return result;
  });

  const operationsByType = computed(() => {
    const result = {
      CREATE: operations.value.filter(op => op.type === 'CREATE'),
      UPDATE: operations.value.filter(op => op.type === 'UPDATE'),
      DELETE: operations.value.filter(op => op.type === 'DELETE'),
      CUSTOM: operations.value.filter(op => op.type === 'CUSTOM')
    };
    return result;
  });

  // 操作列表
  const fetchOperations = async (params?: OperationQueryParams) => {
    try {
      isLoading.value = true;
      error.value = null;

      // 处理查询参数
      const queryParams = {
        page: params?.page || pagination.value.page,
        size: params?.size || pagination.value.pageSize,
        search: params?.search || '',
        type: params?.type,
        status: params?.status,
        queryId: params?.queryId,
        applicationId: params?.applicationId
      };

      const response = await operationService.getOperations(queryParams);

      operations.value = response.items;
      pagination.value = {
        page: queryParams.page,
        pageSize: queryParams.size,
        total: response.total
      };

      return operations.value;
    } catch (err: any) {
      error.value = err;
      messageService.error(`获取操作列表失败: ${err.message}`);
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  // 获取操作详情
  const fetchOperationById = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.getOperationById(id);
      currentOperation.value = response;

      return currentOperation.value;
    } catch (err: any) {
      error.value = err;
      messageService.error(`获取操作详情失败: ${err.message}`);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 创建操作
  const createOperation = async (data: CreateOperationParams) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.createOperation(data);
      messageService.success('操作创建成功');

      return response;
    } catch (err: any) {
      error.value = err;
      messageService.error(`创建操作失败: ${err.message}`);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 更新操作
  const updateOperation = async (id: string, data: UpdateOperationParams) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.updateOperation(id, data);
      
      if (currentOperation.value && currentOperation.value.id === id) {
        currentOperation.value = response;
      }
      
      // 更新列表中的操作
      const index = operations.value.findIndex(op => op.id === id);
      if (index !== -1) {
        operations.value[index] = response;
      }

      messageService.success('操作更新成功');

      return response;
    } catch (err: any) {
      error.value = err;
      messageService.error(`更新操作失败: ${err.message}`);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 删除操作
  const deleteOperation = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      await operationService.deleteOperation(id);
      
      // 从列表中移除
      operations.value = operations.value.filter(op => op.id !== id);
      
      if (currentOperation.value && currentOperation.value.id === id) {
        currentOperation.value = null;
      }

      messageService.success('操作删除成功');

      return true;
    } catch (err: any) {
      error.value = err;
      messageService.error(`删除操作失败: ${err.message}`);
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // 更新操作状态
  const updateOperationStatus = async (id: string, status: 'ACTIVE' | 'INACTIVE' | 'DRAFT') => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.updateOperationStatus(id, status);
      
      if (currentOperation.value && currentOperation.value.id === id) {
        currentOperation.value = response;
      }
      
      // 更新列表中的操作
      const index = operations.value.findIndex(op => op.id === id);
      if (index !== -1) {
        operations.value[index] = response;
      }

      messageService.success(`操作状态更新为: ${status === 'ACTIVE' ? '激活' : status === 'INACTIVE' ? '停用' : '草稿'}`);

      return response;
    } catch (err: any) {
      error.value = err;
      messageService.error(`更新操作状态失败: ${err.message}`);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 执行操作
  const executeOperation = async (id: string, params?: Record<string, any>) => {
    try {
      loading.show('正在执行操作...');
      error.value = null;

      const result = await operationService.executeOperation(id, params);
      
      if (result.status === 'SUCCESS') {
        messageService.success('操作执行成功');
      } else if (result.status === 'FAILED') {
        messageService.error(`操作执行失败: ${result.errorMessage}`);
      }

      return result;
    } catch (err: any) {
      error.value = err;
      messageService.error(`执行操作失败: ${err.message}`);
      return null;
    } finally {
      loading.hide();
    }
  };

  // 获取操作历史
  const fetchOperationHistory = async (operationId: string, page = 1, size = 10) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.getOperationHistory(operationId, { page, size });
      executionHistory.value = response.items;

      return response;
    } catch (err: any) {
      error.value = err;
      messageService.error(`获取操作历史失败: ${err.message}`);
      return { items: [], total: 0 };
    } finally {
      isLoading.value = false;
    }
  };

  // 获取应用列表
  const fetchApplications = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.getApplications();
      applications.value = response;

      return applications.value;
    } catch (err: any) {
      error.value = err;
      messageService.error(`获取应用列表失败: ${err.message}`);
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  // 获取应用详情
  const fetchApplicationById = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await operationService.getApplicationById(id);
      currentApplication.value = response;

      return currentApplication.value;
    } catch (err: any) {
      error.value = err;
      messageService.error(`获取应用详情失败: ${err.message}`);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // 测试操作执行
  const testOperation = async (data: {
    applicationId: string;
    apiEndpoint: string;
    apiMethod: string;
    apiParameters: any[];
    testValues: Record<string, any>;
  }) => {
    try {
      loading.show('正在测试操作...');
      error.value = null;

      const result = await operationService.testOperation(data);
      messageService.success('操作测试成功');

      return result;
    } catch (err: any) {
      error.value = err;
      messageService.error(`测试操作失败: ${err.message}`);
      return null;
    } finally {
      loading.hide();
    }
  };

  return {
    // 状态
    operations,
    currentOperation,
    executionHistory,
    applications,
    currentApplication,
    isLoading,
    error,
    pagination,
    
    // 计算属性
    operationsByStatus,
    operationsByType,
    
    // 方法
    fetchOperations,
    fetchOperationById,
    createOperation,
    updateOperation,
    deleteOperation,
    updateOperationStatus,
    executeOperation,
    fetchOperationHistory,
    fetchApplications,
    fetchApplicationById,
    testOperation
  };
}); 