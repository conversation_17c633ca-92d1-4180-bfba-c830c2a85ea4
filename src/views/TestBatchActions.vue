<template>
  <div class="test-page">
    <h1>批量操作转换测试</h1>
    
    <div class="card">
      <div v-if="testResults">
        <h2>测试结果</h2>
        
        <div class="results-section">
          <h3>标准配置测试结果</h3>
          <table>
            <tr>
              <td>批量操作启用状态:</td>
              <td>{{ testResults.standardConfig.batchEnable }}</td>
            </tr>
            <tr>
              <td>批量操作按钮数量:</td>
              <td>{{ testResults.standardConfig.batchActionsCount }}</td>
            </tr>
            <tr>
              <td>第一个批量操作按钮名称:</td>
              <td>{{ testResults.standardConfig.firstActionName }}</td>
            </tr>
            <tr>
              <td>第二个批量操作按钮名称:</td>
              <td>{{ testResults.standardConfig.secondActionName }}</td>
            </tr>
            <tr>
              <td>批量删除是否支持确认对话框:</td>
              <td>{{ testResults.standardConfig.hasBatchDeleteConfirm }}</td>
            </tr>
            <tr>
              <td>批量删除是否支持权限控制:</td>
              <td>{{ testResults.standardConfig.hasBatchDeletePermissions }}</td>
            </tr>
          </table>
        </div>
        
        <div class="results-section">
          <h3>还原配置测试结果</h3>
          <table>
            <tr>
              <td>批量操作按钮数量:</td>
              <td>{{ testResults.convertedBack.batchActionsCount }}</td>
            </tr>
            <tr>
              <td>第一个批量操作按钮标签:</td>
              <td>{{ testResults.convertedBack.firstActionLabel }}</td>
            </tr>
            <tr>
              <td>第二个批量操作按钮标签:</td>
              <td>{{ testResults.convertedBack.secondActionLabel }}</td>
            </tr>
            <tr>
              <td>批量删除是否支持确认对话框:</td>
              <td>{{ testResults.convertedBack.hasBatchDeleteConfirm }}</td>
            </tr>
            <tr>
              <td>批量删除是否支持权限控制:</td>
              <td>{{ testResults.convertedBack.hasBatchDeletePermissions }}</td>
            </tr>
          </table>
        </div>
        
        <div class="results-section">
          <h3>完整标准配置</h3>
          <pre>{{ JSON.stringify(testResults.fullStandardConfig, null, 2) }}</pre>
        </div>
        
        <div class="results-section">
          <h3>完整还原配置</h3>
          <pre>{{ JSON.stringify(testResults.fullConvertedBack, null, 2) }}</pre>
        </div>
      </div>
      <div v-else>
        <button @click="runTest">运行测试</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { testBatchActionsConversion } from '../test-batch-actions';

const testResults = ref(null);

function runTest() {
  testResults.value = testBatchActionsConversion();
}
</script>

<style scoped>
.test-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.results-section {
  margin-bottom: 30px;
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
}

h2 {
  font-size: 20px;
  margin-bottom: 16px;
}

h3 {
  font-size: 18px;
  margin-bottom: 12px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

table td {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

table td:first-child {
  font-weight: bold;
  width: 300px;
}

button {
  background: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background: #3e8e41;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
}
</style>