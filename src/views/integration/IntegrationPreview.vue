<script setup lang="ts">
// @ts-ignore - 为了解决Vue API导入问题
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useIntegrationStore } from '@/stores/integration';
import { message } from '@/services/message';
import { storeToRefs } from 'pinia';
import type { Integration, FormConfig, TableConfig, FormCondition, ChartConfig, ChartType, FormComponentType } from '@/types/integration';
import { ColumnAlign, ColumnDisplayType } from '@/types/integration';
import { formatDate } from '@/utils/formatter';
import { 
  ArrowLeftOutlined, 
  EditOutlined, 
  ExclamationCircleOutlined, 
  LoadingOutlined,
  SettingOutlined, 
  FullscreenOutlined, 
  FullscreenExitOutlined 
} from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';

// 导入预览组件
import QueryForm from '@/components/integration/preview/QueryForm.vue';
import TableView from '@/components/integration/preview/TableView.vue';
import ChartView from '@/components/integration/preview/ChartView.vue';

// 路由相关
const route = useRoute();
const router = useRouter();
const integrationId = computed(() => route.params.id as string);

// Store
const integrationStore = useIntegrationStore();
const { loading } = storeToRefs(integrationStore);

// 状态
const integration = ref<Integration | null>(null);
const formValues = ref<Record<string, any>>({});
const tableData = ref<any[]>([]);
const tableLoading = ref(false);
const chartData = ref<any[]>([]);
const chartLoading = ref(false);
const queryError = ref<string | null>(null);
const isLoading = ref(false);

// 页面标题和操作按钮
const pageTitle = computed(() => integration.value ? `预览: ${integration.value.name}` : '集成预览');
const actionItems = computed(() => [
  {
    icon: ArrowLeftOutlined,
    text: '返回列表',
    onClick: goBack
  },
  {
    icon: EditOutlined,
    text: '编辑集成',
    type: 'primary',
    onClick: goToEdit
  }
]);

// 集成类型
const integrationType = computed(() => integration.value?.type || '');

// 表格标题
const tableTitle = computed(() => '数据列表');

// 是否显示查询表单
const showQueryForm = computed(() => {
  return queryConditions.value.length > 0;
});

// 集成数据
const integrationData = computed(() => {
  if (!integration.value) return [];
  
  if (integrationType.value === 'TABLE' || integrationType.value === 'SIMPLE_TABLE') {
    return tableData.value;
  } else if (integrationType.value === 'CHART') {
    return chartData.value;
  }
  return [];
});

// 获取集成类型的显示名称
const getIntegrationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'TABLE': '高级表格',
    'SIMPLE_TABLE': '简单表格',
    'CHART': '图表'
  };
  return typeMap[type] || type;
};

// 需要显示查询条件的集成类型
const shouldShowQuery = computed(() => {
  // 优先使用URL查询参数的类型，其次使用集成对象的类型
  console.log('[DEBUG] 计算shouldShowQuery, URL类型:', route.query.type);
  console.log('[DEBUG] 集成类型:', integration.value?.type);
  
  const typeParam = Array.isArray(route.query.type) 
    ? route.query.type[0] 
    : route.query.type as string;
  
  const integrationType = typeParam || (integration.value?.type ?? 'TABLE');
  
  console.log('[DEBUG] 最终使用的类型:', integrationType);
  
  // 只有高级表格类型(TABLE)需要显示查询条件
  return integrationType === 'TABLE';
});

// 计算查询条件列表
const queryConditions = computed(() => {
  if (!integration.value) return [];
  
  // 如果存在集成配置中的表单条件，优先使用
  if (integration.value.formConfig?.conditions?.length) {
    return integration.value.formConfig.conditions
      .filter(condition => condition.visibility !== 'hidden')
      .map(condition => ({
        name: condition.field,
        type: condition.type,
        format: condition.type === 'SELECT' ? 'enum' : condition.type.toLowerCase(),
        formType: mapFormComponentType(condition.type),
        required: condition.required || false,
        defaultValue: condition.defaultValue,
        description: condition.label,
        displayOrder: condition.displayOrder,
        options: condition.componentProps?.options || [],
        advancedConfig: condition.componentProps?.advancedConfig
      }))
      .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
  }
  
  // 其次使用查询参数
  if (integration.value.queryParams?.length) {
    return integration.value.queryParams
      .filter(param => param.description && param.description.trim() !== '')
      .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
  }
  
  return [];
});

// 将FormComponentType映射到formType
const mapFormComponentType = (componentType: string): string => {
  const typeMap: Record<string, string> = {
    'INPUT': 'input',
    'NUMBER': 'number',
    'DATE': 'date',
    'DATETIME': 'datetime',
    'SELECT': 'select',
    'MULTISELECT': 'multiselect',
    'CHECKBOX': 'checkbox',
    'RADIO': 'radio',
    'TEXTAREA': 'textarea'
  };
  return typeMap[componentType] || 'input';
};

// 计算表格列
const tableColumns = computed(() => {
  if (!integration.value || 
      (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
      !integration.value.tableConfig) {
    return [];
  }
  
  return integration.value.tableConfig.columns
    .filter(column => column.visible)
    .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
});

// 表格操作按钮
const tableActions = computed(() => {
  if (!integration.value || 
      (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
      !integration.value.tableConfig) {
    return [];
  }
  
  return integration.value.tableConfig.actions || [];
});

// 表格分页配置
const tablePagination = computed(() => {
  if (!integration.value || 
      (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
      !integration.value.tableConfig) {
    return {
      enabled: true,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100]
    };
  }
  
  return integration.value.tableConfig.pagination;
});

// 表格导出配置
const tableExport = computed(() => {
  if (!integration.value || 
      (integration.value.type !== 'SIMPLE_TABLE' && integration.value.type !== 'TABLE') || 
      !integration.value.tableConfig) {
    return {
      enabled: true,
      formats: ['xlsx', 'csv', 'pdf'],
      maxRows: 1000
    };
  }
  
  return integration.value.tableConfig.export;
});

// 图表配置
const chartConfig = computed(() => {
  if (!integration.value || integration.value.type !== 'CHART' || !integration.value.chartConfig) {
    return {
      type: 'bar' as ChartType,
      title: '默认图表',
      dataMapping: {
        xField: 'category',
        yField: 'value'
      }
    } as ChartConfig;
  }
  
  return integration.value.chartConfig;
});

// 生命周期钩子
onMounted(async () => {
  console.log('页面刷新后重新计算shouldShowQuery');
  
  if (integrationId.value) {
    await loadIntegration();
  }
  
  // 手动触发数据加载
  await loadTableData();
  
  // 添加全局resize事件监听器，用于处理图表和表格的尺寸变化
  window.addEventListener('resize', handleGlobalResize);
});

// 在组件销毁时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleGlobalResize);
});

// 全局resize事件处理
const handleGlobalResize = () => {
  console.log('窗口尺寸变化，触发容器重新计算');
  // 此处可以添加其他需要处理的resize逻辑
};

// 加载集成
const loadIntegration = async () => {
  loading.value = true;
  try {
    const id = route.params.id as string;
    if (!id) {
      message.error({
      content: '集成ID不能为空',
      description: '请检查URL参数或返回集成列表重新选择',
      duration: 5000
    });
      router.replace('/integration/list');
      return;
    }

    try {
      // 从Store获取集成数据
      const result = await integrationStore.fetchIntegrationById(id);
    
      if (result) {
        // 设置集成数据
        integration.value = { ...result };
        
        // 获取URL中的类型参数
        const typeParam = Array.isArray(route.query.type) 
          ? route.query.type[0] 
          : route.query.type as string;
        
        // 如果URL中有指定类型，则优先使用URL中的类型
        if (typeParam && ['CHART', 'TABLE', 'SIMPLE_TABLE'].includes(typeParam)) {
          integration.value.type = typeParam as 'CHART' | 'TABLE' | 'SIMPLE_TABLE';
        }
        
        console.log('加载的集成类型:', integration.value.type);
        
        // 初始化表单值
        if (integration.value.formConfig?.conditions?.length) {
          // 如果有表单配置，优先使用表单配置中的条件
          initFormValuesFromConfig(integration.value.formConfig.conditions);
        } else if (integration.value.queryParams) {
          // 其次使用查询参数
          initFormValues(integration.value.queryParams);
        }
      }
    } catch (error) {
      console.error('从服务端获取集成数据失败:', error);
      
      // 创建模拟数据
      const mockIntegration = {
        id,
        name: `演示集成 ${id}`,
        description: '这是一个用于演示的集成配置',
        type: ['TABLE', 'SIMPLE_TABLE', 'CHART'][Math.floor(Math.random() * 3)] as 'TABLE' | 'SIMPLE_TABLE' | 'CHART',
        status: 'ACTIVE',
        dataSourceId: 'ds_001',
        queryId: 'query_001',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin',
        updatedBy: 'admin'
      };
      
      // 设置集成数据
      integration.value = mockIntegration as Integration;
    }
    
    // 根据集成类型加载不同数据
    if (['TABLE', 'SIMPLE_TABLE'].includes(integration.value?.type || '')) {
      await loadTableData();
    } else if (integration.value?.type === 'CHART') {
      await loadChartData();
    }
    
    await nextTick();
    loading.value = false;
  } catch (error) {
    console.error('加载集成失败:', error);
    message.error({
      content: '加载集成失败',
      description: '无法从服务器获取集成信息，请稍后重试或联系系统管理员',
      duration: 5000
    });
    loading.value = false;
  }
};

// 从表单配置初始化值
const initFormValuesFromConfig = (conditions: any[]) => {
  formValues.value = {};
  
  if (!conditions) return;
  
  conditions.forEach(condition => {
    if (condition.visibility !== 'hidden') {
      // 根据不同类型设置默认值
      let defaultValue = condition.defaultValue || '';
      
      // 根据组件类型处理默认值
      if (condition.type === 'DATE') {
        // 如果默认值为空，检查是否有设置使用当前日期
        if (!defaultValue && condition.componentProps?.advancedConfig?.useCurrentDate) {
          defaultValue = new Date().toISOString().split('T')[0];
        }
      } else if (condition.type === 'DATETIME') {
        // 处理日期时间类型
        if (!defaultValue && condition.componentProps?.advancedConfig?.useCurrentDate) {
          defaultValue = new Date().toISOString().slice(0, 16);
        }
      } else if (condition.type === 'NUMBER') {
        // 数字类型默认值处理
        defaultValue = defaultValue === '' ? '' : Number(defaultValue);
      } else if (condition.type === 'CHECKBOX' || condition.type === 'BOOLEAN') {
        // 布尔类型转换
        defaultValue = defaultValue === true || defaultValue === 'true';
      }
      
      formValues.value[condition.field] = defaultValue;
    }
  });
  
  console.log('[DEBUG] 从表单配置初始化值:', formValues.value);
};

// 从查询参数初始化表单值
const initFormValues = (params?: any[]) => {
  formValues.value = {};
  
  if (!params) return;
  
  params.forEach(param => {
    // 根据不同类型设置默认值
    let defaultValue = param.defaultValue || '';
    
    // 根据类型处理默认值
    if (param.formType === 'date' || param.type === 'DATE' || param.type === 'date') {
      // 如果默认值为空，且有设置当前日期为默认值的选项
      if (!defaultValue && param.useCurrentDate) {
        defaultValue = new Date().toISOString().split('T')[0];
      }
    } else if (param.formType === 'datetime' || param.type === 'DATETIME' || param.type === 'datetime') {
      // 处理日期时间类型
      if (!defaultValue && param.useCurrentDate) {
        defaultValue = new Date().toISOString().slice(0, 16);
      }
    } else if (param.formType === 'number' || param.type === 'NUMBER' || param.type === 'number') {
      // 数字类型默认值处理
      defaultValue = defaultValue === '' ? '' : Number(defaultValue);
    } else if (param.formType === 'checkbox' || param.formType === 'boolean' || 
               param.type === 'BOOLEAN' || param.type === 'boolean') {
      // 布尔类型转换
      defaultValue = defaultValue === true || defaultValue === 'true';
    }
    
    formValues.value[param.name] = defaultValue;
  });
  
  console.log('[DEBUG] 从查询参数初始化值:', formValues.value);
};

// 加载表格数据
const loadTableData = async () => {
  try {
    tableLoading.value = true;
    console.log('[DEBUG] 开始加载表格数据...');

    // 显示加载状态的延迟
    await sleep(300);
    
    // 定义表格的列配置
    if (integration.value && !integration.value.tableConfig?.columns?.length) {
      integration.value.tableConfig = {
        ...integration.value?.tableConfig || {},
        columns: [
          { field: 'id', label: 'ID', type: 'number', visible: true, sortable: true, displayOrder: 0, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT },
          { field: 'date', label: '日期', type: 'date', visible: true, sortable: true, displayOrder: 1, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.DATE },
          { field: 'product', label: '产品', type: 'string', visible: true, sortable: true, displayOrder: 2, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT },
          { field: 'quantity', label: '数量', type: 'number', visible: true, sortable: true, displayOrder: 3, align: ColumnAlign.RIGHT, displayType: ColumnDisplayType.NUMBER },
          { field: 'amount', label: '金额', type: 'currency', visible: true, sortable: true, displayOrder: 4, align: ColumnAlign.RIGHT, displayType: ColumnDisplayType.TEXT },
          { field: 'status', label: '状态', type: 'enum', visible: true, sortable: true, displayOrder: 5, align: ColumnAlign.CENTER, displayType: ColumnDisplayType.TAG },
          { field: 'customer', label: '客户', type: 'string', visible: true, sortable: true, displayOrder: 6, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT },
          { field: 'department', label: '部门', type: 'string', visible: true, sortable: true, displayOrder: 7, align: ColumnAlign.LEFT, displayType: ColumnDisplayType.TEXT }
        ],
        actions: [],
        pagination: {
          enabled: true,
          pageSize: 10,
          pageSizeOptions: [10, 20, 50, 100]
        },
        export: {
          enabled: true,
          formats: ['xlsx', 'csv', 'pdf'],
          maxRows: 1000
        },
        batchActions: [],
        advancedFilters: {
          enabled: true,
          defaultFilters: [],
          savedFilters: []
        },
        aggregation: {
          enabled: false,
          groupByFields: [],
          aggregationFunctions: []
        }
      };
    }
    
    // 生成50条测试数据
    const mockData = generateMockTableData(50);

    console.log('生成了测试数据:', mockData.length, '条记录');
    console.log('样例数据:', mockData[0]);
    
    // 直接设置数据
    tableData.value = mockData;
    console.log('[DEBUG] 表格数据加载完成:', tableData.value.length, '条记录');
  } catch (error) {
    console.error('加载表格数据失败:', error);
    message.error({
      content: '加载表格数据失败',
      description: '无法从服务器获取数据，请检查查询参数或稍后重试',
      duration: 5000
    });
  } finally {
    tableLoading.value = false;
  }
};

// 生成图表数据
const loadChartData = async () => {
  try {
    chartLoading.value = true;
    console.log('正在加载图表数据...');
    
    // 在实际场景中，这里应该调用API获取实际数据
    await sleep(1000); // 模拟网络延迟
    
    if (!integration.value || !integration.value.chartConfig) {
      console.warn('集成或图表配置为空，无法加载图表数据');
      chartLoading.value = false;
      return;
    }
    
    const chartType = integration.value.chartConfig.type || 'bar';
    const dataSize = 10; // 数据点数量
    console.log('图表类型:', chartType);
    
    // 生成图表测试数据
    chartData.value = generateMockChartData(chartType, dataSize);
    
    console.log('图表数据加载完成, 共生成', chartData.value.length, '条数据');
  } catch (error) {
    console.error('加载图表数据失败:', error);
  } finally {
    chartLoading.value = false;
  }
};

// 处理表格操作
const handleTableAction = (action: any, record: any) => {
  console.log('触发表格操作:', action, record);
  message.info({
      content: `执行操作: ${action.label}`,
      description: `记录ID: ${record.id}`,
      duration: 3000
    });
};

// 处理导出
const handleExport = (format: string) => {
  console.log('导出数据:', format);
  message.info({
      content: `导出数据`,
      description: `正在导出数据至${format}格式`,
      duration: 3000
    });
};

// 处理分页变化
const handlePageChange = (page: number) => {
  console.log('页码变化:', page);
};

// 返回按钮事件
const goBack = () => {
  router.push('/integration');
};

// 编辑按钮事件
const goToEdit = () => {
  router.push(`/integration/edit/${integrationId.value}`);
};

// 加载数据
const loadData = async () => {
  isLoading.value = true;
  queryError.value = null;
  
  try {
    if (integrationType.value === 'TABLE' || integrationType.value === 'SIMPLE_TABLE') {
      await loadTableData();
    } else if (integrationType.value === 'CHART') {
      await loadChartData();
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    queryError.value = '数据加载失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  formValues.value = {};
  loadData();
};

// 用于模拟API延迟的函数
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 用于生成模拟表格数据的函数
const generateMockTableData = (count = 50) => {
  const statusOptions = ['已完成', '处理中', '已取消', '待处理'];
  const productOptions = ['产品A', '产品B', '产品C', '产品D', '产品E'];
  const departmentOptions = ['销售部', '技术部', '客服部', '财务部', '市场部'];
  const customerOptions = ['客户1', '客户2', '客户3', '客户4', '客户5', '客户6', '客户7', '客户8'];
  
  return Array.from({ length: count }).map((_, index) => ({
    id: index + 1,
    date: formatDate(new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), 'YYYY-MM-DD'),
    product: productOptions[Math.floor(Math.random() * productOptions.length)],
    quantity: Math.floor(Math.random() * 100) + 1,
    amount: (Math.random() * 10000).toFixed(2),
    status: statusOptions[Math.floor(Math.random() * statusOptions.length)],
    customer: customerOptions[Math.floor(Math.random() * customerOptions.length)],
    department: departmentOptions[Math.floor(Math.random() * departmentOptions.length)]
  }));
};

// 用于生成模拟图表数据的函数
const generateMockChartData = (chartType: string, count: number) => {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  const categories = ['类别A', '类别B', '类别C', '类别D'];
  
  switch (chartType) {
    case 'bar':
    case 'line':
      return months.slice(0, count).map(month => ({
        name: month,
        value: Math.floor(Math.random() * 1000)
      }));
    case 'pie':
      return categories.slice(0, count).map(category => ({
        name: category,
        value: Math.floor(Math.random() * 100)
      }));
    case 'scatter':
      return Array.from({ length: count }).map((_, i) => ({
        x: Math.random() * 100,
        y: Math.random() * 100,
        name: `数据点 ${i + 1}`
      }));
    default:
      return months.slice(0, count).map(month => ({
        name: month,
        value: Math.floor(Math.random() * 1000)
      }));
  }
};

// 导出配置（包含maxRows）
const exportConfig = computed(() => {
  if (!integration.value?.tableConfig?.export) {
    return {
      enabled: true,
      formats: ['xlsx', 'csv', 'pdf'],
      maxRows: 1000
    };
  }
  return {
    ...integration.value.tableConfig.export,
    maxRows: integration.value.tableConfig.export.maxRows || 1000
  };
});
</script>

<template>
  <div class="preview-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <h1 class="text-xl font-semibold mb-0">{{ pageTitle }}</h1>
          <a-tag v-if="integration?.type" color="blue" class="ml-2">{{ getIntegrationType(integration.type) }}</a-tag>
        </div>
        
        <div class="flex items-center space-x-2">
          <template v-for="(item, index) in actionItems" :key="index">
            <a-button 
              :type="item.type || 'default'" 
              @click="item.onClick"
            >
              <template #icon v-if="item.icon">
                <component :is="item.icon" />
              </template>
              {{ item.text }}
            </a-button>
          </template>
        </div>
      </div>
    </div>
    
    <!-- 预览内容区域 -->
    <div class="preview-content">
      <!-- 查询表单和结果内容 -->
      <a-card class="result-preview-card">
        <!-- 页面顶部消息 -->
        <div v-if="!isLoading && queryError" class="error-message">
          <a-alert type="error" :message="queryError" show-icon />
        </div>
        
        <!-- 查询条件表单区域 -->
        <template v-if="shouldShowQuery">
          <div class="query-info-header mb-4">
            <div class="flex justify-between items-center">
              <h3 class="text-base font-medium mb-0">查询条件</h3>
              <a-tag v-if="integration?.type" color="blue">{{ getIntegrationType(integration.type) }}</a-tag>
            </div>
          </div>

          <QueryForm 
            v-if="queryConditions.length > 0"
            :conditions="queryConditions" 
            :model-value="formValues"
            @submit="loadData"
            @reset="resetForm"
            class="query-form-section"
          />

          <div v-else class="empty-query-message p-4 mb-4 border border-dashed border-gray-300 rounded">
            <a-alert type="info" message="无查询条件可用" description="当前集成未配置查询条件，您可以继续查看数据或返回编辑集成添加查询条件。" show-icon />
          </div>
        </template>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <a-spin size="large" tip="数据加载中..." />
        </div>
        
        <!-- 结果展示区域 -->
        <div v-else-if="!queryError" class="result-container">
          <!-- 图表视图 -->
          <ChartView
            v-if="integrationData && integrationType === 'CHART'"
            :config="chartConfig"
            :data="integrationData"
            @refresh="loadData"
          />
          
          <!-- 表格视图 -->
          <TableView
            v-else-if="integrationData && (integrationType === 'TABLE' || integrationType === 'SIMPLE_TABLE')"
            :columns="tableColumns"
            :data="integrationData" 
            :pagination="tablePagination"
            :title="tableTitle"
            :export-config="exportConfig"
            @refresh="loadData"
          />
          
          <!-- 无数据提示 -->
          <div v-else class="empty-container">
            <a-empty description="暂无数据，请修改查询条件后重试">
              <a-button type="primary" @click="loadData">刷新数据</a-button>
            </a-empty>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preview-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.result-preview-card {
  height: 100%;
}

.error-message {
  margin-bottom: 16px;
}

.query-form-section {
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.result-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.query-info-header {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}
</style>