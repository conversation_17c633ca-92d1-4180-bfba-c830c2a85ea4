<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center">
        <button
          @click="goBack"
          class="mr-4 text-gray-500 hover:text-gray-700"
        >
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-2xl font-bold text-gray-900">操作详情</h1>
      </div>
    </div>
    
    <!-- 加载中状态 -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="inline-flex items-center px-4 py-2">
        <i class="fas fa-spinner fa-spin mr-2"></i>
        加载中...
      </div>
    </div>
    
    <!-- 操作详情 -->
    <div v-else-if="operation" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- 左侧详情区 -->
      <div class="md:col-span-2 space-y-6">
        <!-- 基本信息卡片 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-medium text-gray-900">基本信息</h2>
              <div class="flex space-x-2">
                <button 
                  v-if="operation.status !== 'ACTIVE'"
                  @click="activateOperation"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <i class="fas fa-check-circle mr-1"></i>
                  激活
                </button>
                <button 
                  v-if="operation.status === 'ACTIVE'"
                  @click="deactivateOperation"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  <i class="fas fa-ban mr-1"></i>
                  停用
                </button>
                <button 
                  @click="editOperation"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <i class="fas fa-edit mr-1"></i>
                  编辑
                </button>
              </div>
            </div>
          </div>
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">操作名称</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ operation.name }}</dd>
              </div>
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">操作类型</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <span :class="[
                    'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                    {
                      'bg-green-100 text-green-800': operation.type === 'CREATE',
                      'bg-blue-100 text-blue-800': operation.type === 'UPDATE',
                      'bg-red-100 text-red-800': operation.type === 'DELETE',
                      'bg-purple-100 text-purple-800': operation.type === 'CUSTOM'
                    }
                  ]">
                    {{ getOperationTypeLabel(operation.type) }}
                  </span>
                </dd>
              </div>
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">状态</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <span :class="[
                    'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                    {
                      'bg-green-100 text-green-800': operation.status === 'ACTIVE',
                      'bg-gray-100 text-gray-800': operation.status === 'INACTIVE',
                      'bg-yellow-100 text-yellow-800': operation.status === 'DRAFT'
                    }
                  ]">
                    {{ getStatusLabel(operation.status) }}
                  </span>
                </dd>
              </div>
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">创建时间</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(operation.createdAt) }}</dd>
              </div>
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">最后更新</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(operation.updatedAt) }}</dd>
              </div>
              <div class="col-span-2">
                <dt class="text-sm font-medium text-gray-500">操作描述</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ operation.description || '无描述' }}</dd>
              </div>
            </dl>
          </div>
        </div>
        
        <!-- 查询信息卡片 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">查询关联</h2>
          </div>
          <div class="px-6 py-4">
            <div v-if="relatedQuery" class="flex justify-between items-start">
              <div>
                <h3 class="text-md font-medium text-gray-900">{{ relatedQuery.name }}</h3>
                <p class="mt-1 text-sm text-gray-500">{{ relatedQuery.description || '无描述' }}</p>
              </div>
              <button 
                @click="viewQuery"
                class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                查看查询
              </button>
            </div>
            <div v-else class="text-sm text-gray-500">
              未找到关联查询信息
            </div>
          </div>
        </div>
        
        <!-- API信息卡片 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">API信息</h2>
          </div>
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">应用</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ applicationName }}</dd>
              </div>
              <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">HTTP方法</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <span :class="[
                    'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                    {
                      'bg-blue-100 text-blue-800': operation.apiMethod === 'GET',
                      'bg-green-100 text-green-800': operation.apiMethod === 'POST',
                      'bg-yellow-100 text-yellow-800': operation.apiMethod === 'PUT',
                      'bg-red-100 text-red-800': operation.apiMethod === 'DELETE'
                    }
                  ]">
                    {{ operation.apiMethod }}
                  </span>
                </dd>
              </div>
              <div class="col-span-2">
                <dt class="text-sm font-medium text-gray-500">API端点</dt>
                <dd class="mt-1 text-sm text-gray-900 break-all">{{ operation.apiEndpoint }}</dd>
              </div>
            </dl>
          </div>
        </div>
        
        <!-- 参数卡片 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">参数列表</h2>
          </div>
          <div class="px-6 py-4">
            <div v-if="operation.apiParameters && operation.apiParameters.length > 0">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参数名称</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">必填</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">映射字段</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(param, index) in operation.apiParameters" :key="index">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ param.name }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ param.type }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span v-if="param.required" class="text-green-500"><i class="fas fa-check"></i></span>
                        <span v-else class="text-gray-300"><i class="fas fa-times"></i></span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ param.mappingField || '-' }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ param.description || '-' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else class="text-sm text-gray-500 text-center py-4">
              无参数
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧执行区 -->
      <div class="space-y-6">
        <!-- 执行卡片 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">执行操作</h2>
          </div>
          <div class="px-6 py-4">
            <div v-if="operation.status === 'ACTIVE'">
              <p class="text-sm text-gray-500 mb-4">
                可以通过执行按钮直接执行此操作。
                {{ operation.requireConfirmation ? '操作执行前需要确认。' : '' }}
              </p>
              
              <button
                @click="executeOperation"
                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                :disabled="isExecuting"
              >
                <i v-if="isExecuting" class="fas fa-spinner fa-spin mr-2"></i>
                <i v-else class="fas fa-play mr-2"></i>
                执行操作
              </button>
            </div>
            <div v-else class="text-center py-4">
              <p class="text-sm text-gray-500 mb-2">操作当前处于 {{ getStatusLabel(operation.status) }} 状态</p>
              <button
                v-if="operation.status === 'DRAFT' || operation.status === 'INACTIVE'"
                @click="activateOperation"
                class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <i class="fas fa-check-circle mr-1"></i>
                激活操作
              </button>
            </div>
          </div>
        </div>
        
        <!-- 最近执行历史 -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-medium text-gray-900">最近执行历史</h2>
            <button
              @click="fetchExecutionHistory"
              class="text-sm text-indigo-600 hover:text-indigo-800"
              :disabled="isLoadingHistory"
            >
              <i v-if="isLoadingHistory" class="fas fa-spinner fa-spin mr-1"></i>
              <i v-else class="fas fa-sync-alt mr-1"></i>
              刷新
            </button>
          </div>
          <div class="px-6 py-4">
            <div v-if="isLoadingHistory" class="text-center py-4">
              <div class="inline-flex items-center px-4 py-2">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                加载历史记录中...
              </div>
            </div>
            <div v-else-if="executionHistory.length === 0" class="text-center py-4">
              <p class="text-sm text-gray-500">暂无执行历史</p>
            </div>
            <div v-else class="space-y-4">
              <div v-for="(record, index) in executionHistory" :key="index" class="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                <div class="flex justify-between items-start">
                  <div>
                    <span :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      {
                        'bg-green-100 text-green-800': record.status === 'SUCCESS',
                        'bg-red-100 text-red-800': record.status === 'FAILED',
                        'bg-yellow-100 text-yellow-800': record.status === 'PENDING',
                        'bg-blue-100 text-blue-800': record.status === 'PROCESSING'
                      }
                    ]">
                      {{ getExecutionStatusLabel(record.status) }}
                    </span>
                    <p class="mt-1 text-sm text-gray-500">
                      {{ formatDate(record.executedAt) }}
                    </p>
                  </div>
                  <p class="text-sm text-gray-500">
                    耗时: {{ record.executionTime }}ms
                  </p>
                </div>
                <div v-if="record.status === 'FAILED' && record.errorMessage" class="mt-2">
                  <p class="text-sm text-red-600">{{ record.errorMessage }}</p>
                </div>
              </div>
              <div class="text-center pt-2">
                <button
                  @click="viewAllHistory"
                  class="text-sm text-indigo-600 hover:text-indigo-800"
                >
                  查看全部历史
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 确认弹窗 -->
    <div v-if="showConfirmDialog" class="fixed z-10 inset-0 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
                <i class="fas fa-question-circle text-indigo-600"></i>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  确认执行
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    {{ operation.confirmationMessage || '确认执行此操作？' }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button 
              @click="confirmExecution"
              type="button" 
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
              :disabled="isExecuting"
            >
              <i v-if="isExecuting" class="fas fa-spinner fa-spin mr-2"></i>
              确认
            </button>
            <button 
              @click="cancelExecution"
              type="button" 
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useOperationStore } from '@/stores/operation';
import { useQueryStore } from '@/stores/query';
import type { Operation, OperationExecution, OperationType, OperationStatus, ExecutionStatus } from '@/types/operation';
import type { Query } from '@/types/query';

// 路由相关
const route = useRoute();
const router = useRouter();
const operationId = computed(() => route.params.id as string);
const autoExecute = computed(() => route.query.execute === 'true');

// Store
const operationStore = useOperationStore();
const queryStore = useQueryStore();

// 状态
const isLoading = ref(false);
const isExecuting = ref(false);
const isLoadingHistory = ref(false);
const operation = computed(() => operationStore.currentOperation);
const executionHistory = ref<OperationExecution[]>([]);
const showConfirmDialog = ref(false);
const relatedQuery = ref<Query | null>(null);
const applicationName = ref('');

// 生命周期钩子
onMounted(async () => {
  try {
    isLoading.value = true;
    
    // 加载操作详情
    await loadOperation();
    
    // 加载执行历史
    await fetchExecutionHistory();
    
    // 如果URL中有execute参数，自动显示执行确认框
    if (autoExecute.value && operation.value?.status === 'ACTIVE') {
      showConfirmDialog.value = true;
    }
  } catch (error) {
    console.error('初始化失败:', error);
  } finally {
    isLoading.value = false;
  }
});

// 加载操作详情
const loadOperation = async () => {
  try {
    await operationStore.fetchOperationById(operationId.value);
    
    if (operation.value) {
      // 加载关联查询
      if (operation.value.queryId) {
        await loadRelatedQuery();
      }
      
      // 加载应用信息
      if (operation.value.applicationId) {
        await loadApplicationInfo();
      }
    }
  } catch (error) {
    console.error('加载操作详情失败:', error);
  }
};

// 加载关联查询信息
const loadRelatedQuery = async () => {
  try {
    if (operation.value?.queryId) {
      const query = await queryStore.getQueryById(operation.value.queryId);
      relatedQuery.value = query;
    }
  } catch (error) {
    console.error('加载关联查询失败:', error);
  }
};

// 加载应用信息
const loadApplicationInfo = async () => {
  try {
    if (operation.value?.applicationId) {
      const app = await operationStore.fetchApplicationById(operation.value.applicationId);
      if (app) {
        applicationName.value = app.name;
      }
    }
  } catch (error) {
    console.error('加载应用信息失败:', error);
  }
};

// 获取执行历史
const fetchExecutionHistory = async () => {
  try {
    isLoadingHistory.value = true;
    const response = await operationStore.fetchOperationHistory(operationId.value, 1, 5);
    executionHistory.value = response.items;
  } catch (error) {
    console.error('加载执行历史失败:', error);
  } finally {
    isLoadingHistory.value = false;
  }
};

// 执行操作
const executeOperation = () => {
  if (operation.value?.requireConfirmation) {
    showConfirmDialog.value = true;
  } else {
    doExecuteOperation();
  }
};

// 确认执行
const confirmExecution = () => {
  doExecuteOperation();
};

// 取消执行
const cancelExecution = () => {
  showConfirmDialog.value = false;
};

// 实际执行操作
const doExecuteOperation = async () => {
  try {
    isExecuting.value = true;
    
    const result = await operationStore.executeOperation(operationId.value);
    
    if (result) {
      // 刷新执行历史
      await fetchExecutionHistory();
    }
    
    showConfirmDialog.value = false;
  } catch (error) {
    console.error('执行操作失败:', error);
  } finally {
    isExecuting.value = false;
  }
};

// 激活操作
const activateOperation = async () => {
  try {
    if (!operation.value) return;
    
    await operationStore.updateOperationStatus(operationId.value, 'ACTIVE');
    
    // 重新加载操作详情
    await loadOperation();
  } catch (error) {
    console.error('激活操作失败:', error);
  }
};

// 停用操作
const deactivateOperation = async () => {
  try {
    if (!operation.value) return;
    
    await operationStore.updateOperationStatus(operationId.value, 'INACTIVE');
    
    // 重新加载操作详情
    await loadOperation();
  } catch (error) {
    console.error('停用操作失败:', error);
  }
};

// 编辑操作
const editOperation = () => {
  router.push({ name: 'OperationEdit', params: { id: operationId.value } });
};

// 查看查询
const viewQuery = () => {
  if (relatedQuery.value) {
    router.push({ name: 'QueryDetail', params: { id: relatedQuery.value.id } });
  }
};

// 查看所有历史
const viewAllHistory = () => {
  router.push({ name: 'OperationHistory', params: { id: operationId.value } });
};

// 返回
const goBack = () => {
  router.back();
};

// 工具方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const getOperationTypeLabel = (type: OperationType) => {
  const typeMap: Record<OperationType, string> = {
    'CREATE': '创建',
    'UPDATE': '更新',
    'DELETE': '删除',
    'CUSTOM': '自定义'
  };
  
  return typeMap[type] || type;
};

const getStatusLabel = (status: OperationStatus) => {
  const statusMap: Record<OperationStatus, string> = {
    'ACTIVE': '已激活',
    'INACTIVE': '已停用',
    'DRAFT': '草稿'
  };
  
  return statusMap[status] || status;
};

const getExecutionStatusLabel = (status: ExecutionStatus) => {
  const statusMap: Record<ExecutionStatus, string> = {
    'SUCCESS': '成功',
    'FAILED': '失败',
    'PENDING': '等待中',
    'PROCESSING': '处理中'
  };
  
  return statusMap[status] || status;
};
</script> 