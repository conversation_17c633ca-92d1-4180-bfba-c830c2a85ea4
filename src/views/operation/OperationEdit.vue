<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center">
        <button
          @click="goBack"
          class="mr-4 text-gray-500 hover:text-gray-700"
        >
          <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-2xl font-bold text-gray-900">{{ isEditing ? '编辑操作' : '创建操作' }}</h1>
      </div>
    </div>
    
    <!-- 加载中状态 -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="inline-flex items-center px-4 py-2">
        <i class="fas fa-spinner fa-spin mr-2"></i>
        加载中...
      </div>
    </div>
    
    <!-- 表单区域 -->
    <div v-else class="bg-white shadow rounded-lg">
      <div class="p-6">
        <form @submit.prevent="submitForm">
          <!-- 表单分组：基础信息 -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">基础信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 操作名称 -->
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                  操作名称 <span class="text-red-500">*</span>
                </label>
                <input
                  id="name"
                  v-model="operation.name"
                  type="text"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="请输入操作名称"
                  required
                />
              </div>
              
              <!-- 操作类型 -->
              <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">
                  操作类型 <span class="text-red-500">*</span>
                </label>
                <select
                  id="type"
                  v-model="operation.type"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                >
                  <option value="">请选择操作类型</option>
                  <option value="CREATE">创建</option>
                  <option value="UPDATE">更新</option>
                  <option value="DELETE">删除</option>
                  <option value="CUSTOM">自定义</option>
                </select>
              </div>
              
              <!-- 操作描述 -->
              <div class="md:col-span-2">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                  操作描述
                </label>
                <textarea
                  id="description"
                  v-model="operation.description"
                  rows="3"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="请输入操作描述"
                ></textarea>
              </div>
            </div>
          </div>
          
          <!-- 表单分组：查询关联 -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">查询关联</h2>
            <div>
              <label for="queryId" class="block text-sm font-medium text-gray-700 mb-1">
                关联查询 <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <select
                  id="queryId"
                  v-model="operation.queryId"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                  @change="fetchQueryFields"
                >
                  <option value="">请选择关联查询</option>
                  <option v-for="query in queries" :key="query.id" :value="query.id">
                    {{ query.name }}
                  </option>
                </select>
                <div v-if="isLoadingQueries" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <i class="fas fa-spinner fa-spin text-gray-400"></i>
                </div>
              </div>
              <p v-if="operation.queryId" class="mt-2 text-sm text-gray-500">
                已选择查询: {{ getQueryNameById(operation.queryId) }}
              </p>
            </div>
          </div>
          
          <!-- 表单分组：应用接口 -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">应用接口</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 协议类型 -->
              <div>
                <label for="protocolType" class="block text-sm font-medium text-gray-700 mb-1">
                  协议类型 <span class="text-red-500">*</span>
                </label>
                <select
                  id="protocolType"
                  v-model="operation.protocolType"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                >
                  <option value="">请选择协议类型</option>
                  <option value="HTTP">HTTP</option>
                  <option value="GENERIC">泛化调用</option>
                </select>
              </div>
              
              <!-- HTTP协议特有字段 -->
              <template v-if="operation.protocolType === 'HTTP'">
                <!-- 内容类型 -->
                <div>
                  <label for="contentType" class="block text-sm font-medium text-gray-700 mb-1">
                    内容类型 <span class="text-red-500">*</span>
                  </label>
                  <select
                    id="contentType"
                    v-model="operation.contentType"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    required
                  >
                    <option value="">请选择内容类型</option>
                    <option value="APPLICATION/JSON">APPLICATION/JSON</option>
                    <option value="APPLICATION/FORM-DATA">APPLICATION/FORM-DATA</option>
                  </select>
                </div>
                
                <!-- API端点（HTTP模式直接输入） -->
                <div>
                  <label for="apiEndpoint" class="block text-sm font-medium text-gray-700 mb-1">
                    API端点 <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="apiEndpoint"
                    v-model="operation.apiEndpoint"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="请输入API端点"
                    required
                  />
                </div>
                
                <!-- HTTP方法（HTTP模式固定为POST） -->
                <div>
                  <label for="apiMethod" class="block text-sm font-medium text-gray-700 mb-1">
                    HTTP方法
                  </label>
                  <input
                    id="apiMethod"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-500 sm:text-sm"
                    value="POST"
                    disabled
                  />
                  <input type="hidden" v-model="operation.apiMethod" value="POST" />
                </div>
              </template>
              
              <!-- 泛化调用特有字段 -->
              <template v-else-if="operation.protocolType === 'GENERIC'">
                <!-- 应用选择 -->
                <div>
                  <label for="applicationId" class="block text-sm font-medium text-gray-700 mb-1">
                    应用标识 <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <input
                      id="applicationId"
                      v-model="operation.applicationId"
                      type="text"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="请输入应用标识"
                      required
                      @blur="fetchBackendClasses"
                    />
                    <div v-if="isLoadingApplications" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <i class="fas fa-spinner fa-spin text-gray-400"></i>
                    </div>
                  </div>
                </div>
                
                <!-- 后端类名 -->
                <div>
                  <label for="backendClass" class="block text-sm font-medium text-gray-700 mb-1">
                    后端类名 <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      id="backendClass"
                      v-model="backendClass"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      required
                      @change="fetchBackendMethods"
                    >
                      <option value="">请选择后端类名</option>
                      <option v-for="cls in backendClasses" :key="cls.name" :value="cls.name">
                        {{ cls.name }}
                      </option>
                    </select>
                  </div>
                </div>
                
                <!-- 后端方法名 -->
                <div>
                  <label for="backendMethod" class="block text-sm font-medium text-gray-700 mb-1">
                    后端方法名 <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      id="backendMethod"
                      v-model="backendMethod"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      required
                      @change="selectMethod"
                    >
                      <option value="">请选择后端方法名</option>
                      <option v-for="method in backendMethods" :key="method.name" :value="method.name">
                        {{ method.name }}
                      </option>
                    </select>
                  </div>
                </div>
              </template>
            </div>
          </div>
          
          <!-- 表单分组：参数映射 -->
          <div class="mb-8">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-medium text-gray-900">参数映射</h2>
              <button
                type="button"
                @click="addParameter"
                class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <i class="fas fa-plus mr-1"></i>
                添加参数
              </button>
            </div>
            
            <div v-if="operation.apiParameters.length === 0" class="bg-gray-50 p-4 rounded-md text-center text-gray-500">
              <p>请添加API参数</p>
            </div>
            
            <div v-else class="space-y-4">
              <div v-for="(param, index) in operation.apiParameters" :key="index" class="bg-gray-50 p-4 rounded-md">
                <div class="flex justify-between items-center mb-3">
                  <h3 class="font-medium text-gray-900">参数 #{{ index + 1 }}</h3>
                  <button
                    type="button"
                    @click="removeParameter(index)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- 参数名称 -->
                  <div>
                    <label :for="`param-name-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                      参数名称 <span class="text-red-500">*</span>
                    </label>
                    <input
                      :id="`param-name-${index}`"
                      v-model="param.name"
                      type="text"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="参数名称"
                      required
                    />
                  </div>
                  
                  <!-- 参数类型 -->
                  <div>
                    <label :for="`param-type-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                      参数类型 <span class="text-red-500">*</span>
                    </label>
                    <select
                      :id="`param-type-${index}`"
                      v-model="param.type"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      required
                    >
                      <option value="">请选择参数类型</option>
                      <option value="string">字符串</option>
                      <option value="number">数字</option>
                      <option value="boolean">布尔值</option>
                      <option value="object">对象</option>
                      <option value="array">数组</option>
                    </select>
                  </div>
                  
                  <!-- 是否必填 -->
                  <div>
                    <div class="flex items-center">
                      <input
                        :id="`param-required-${index}`"
                        v-model="param.required"
                        type="checkbox"
                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label :for="`param-required-${index}`" class="ml-2 block text-sm text-gray-900">
                        必填参数
                      </label>
                    </div>
                  </div>
                  
                  <!-- 默认值 -->
                  <div>
                    <label :for="`param-default-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                      默认值
                    </label>
                    <input
                      :id="`param-default-${index}`"
                      v-model="param.defaultValue"
                      type="text"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="默认值"
                    />
                  </div>
                  
                  <!-- 映射字段 -->
                  <div class="md:col-span-2">
                    <label :for="`param-mapping-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                      映射查询字段
                    </label>
                    <select
                      :id="`param-mapping-${index}`"
                      v-model="param.mappingField"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="">不映射</option>
                      <option v-for="field in queryFields" :key="field" :value="field">
                        {{ field }}
                      </option>
                    </select>
                  </div>
                  
                  <!-- 描述 -->
                  <div class="md:col-span-2">
                    <label :for="`param-desc-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                      参数描述
                    </label>
                    <input
                      :id="`param-desc-${index}`"
                      v-model="param.description"
                      type="text"
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="参数描述"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 表单分组：交互设置 -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">交互设置</h2>
            
            <div class="space-y-4">
              <!-- 是否需要确认 -->
              <div>
                <div class="flex items-center">
                  <input
                    id="requireConfirmation"
                    v-model="operation.requireConfirmation"
                    type="checkbox"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="requireConfirmation" class="ml-2 block text-sm text-gray-900">
                    执行前需要确认
                  </label>
                </div>
              </div>
              
              <!-- 确认消息 -->
              <div v-if="operation.requireConfirmation">
                <label for="confirmationMessage" class="block text-sm font-medium text-gray-700 mb-1">
                  确认消息
                </label>
                <input
                  id="confirmationMessage"
                  v-model="operation.confirmationMessage"
                  type="text"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="确认操作执行？"
                />
              </div>
              
              <!-- 成功消息 -->
              <div>
                <label for="successMessage" class="block text-sm font-medium text-gray-700 mb-1">
                  成功消息
                </label>
                <input
                  id="successMessage"
                  v-model="operation.successMessage"
                  type="text"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="操作执行成功"
                />
              </div>
              
              <!-- 错误消息 -->
              <div>
                <label for="errorMessage" class="block text-sm font-medium text-gray-700 mb-1">
                  错误消息
                </label>
                <input
                  id="errorMessage"
                  v-model="operation.errorMessage"
                  type="text"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="操作执行失败"
                />
              </div>
            </div>
          </div>
          
          <!-- 表单操作 -->
          <div class="flex justify-end pt-5 border-t border-gray-200">
            <button
              type="button"
              @click="goBack"
              class="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              取消
            </button>
            <button
              type="submit"
              class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              :disabled="isSubmitting"
            >
              <i v-if="isSubmitting" class="fas fa-spinner fa-spin mr-2"></i>
              {{ isEditing ? '保存修改' : '创建操作' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useOperationStore } from '@/stores/operation';
import { useQueryStore } from '@/stores/query';
import type { CreateOperationParams, UpdateOperationParams, ApiParameter, Application } from '@/types/operation';
import type { Query } from '@/types/query';
import { v4 as uuidv4 } from 'uuid';

// 路由相关
const route = useRoute();
const router = useRouter();
const operationId = computed(() => route.params.id as string);
const isEditing = computed(() => !!operationId.value);

// Store
const operationStore = useOperationStore();
const queryStore = useQueryStore();

// 状态
const isLoading = ref(false);
const isSubmitting = ref(false);
const isLoadingQueries = ref(false);
const isLoadingApplications = ref(false);
const queries = ref<Query[]>([]);
const applications = ref<Application[]>([]);
const applicationApis = ref<any[]>([]);
const queryFields = ref<string[]>([]);
const backendClasses = ref<any[]>([]);
const backendMethods = ref<any[]>([]);
const backendClass = ref('');
const backendMethod = ref('');

// 操作对象
const operation = reactive<CreateOperationParams & { id?: string }>({
  id: '',
  name: '',
  description: '',
  type: 'CREATE',
  queryId: '',
  applicationId: '',
  protocolType: 'HTTP',
  contentType: 'APPLICATION/JSON',
  apiEndpoint: '',
  apiMethod: '' as 'GET' | 'POST' | 'PUT' | 'DELETE',
  apiParameters: [],
  requireConfirmation: true,
  confirmationMessage: '确认执行此操作？',
  successMessage: '操作执行成功',
  errorMessage: '操作执行失败'
});

// 生命周期钩子
onMounted(async () => {
  try {
    isLoading.value = true;
    
    // 加载查询列表
    await loadQueries();
    
    // 加载应用列表
    await loadApplications();
    
    // 如果是编辑模式，加载操作详情
    if (isEditing.value) {
      await loadOperation();
    }
  } catch (error) {
    console.error('初始化失败:', error);
  } finally {
    isLoading.value = false;
  }
});

// 监听协议类型变化，重置相关字段
watchEffect(() => {
  if (operation.protocolType === 'HTTP') {
    // HTTP模式下默认使用POST方法
    operation.apiMethod = 'POST';
    // 如果是切换过来的，清空应用相关信息
    if (operation.applicationId) {
      operation.applicationId = '';
      backendClasses.value = [];
      backendMethods.value = [];
      backendClass.value = '';
      backendMethod.value = '';
    }
  } else if (operation.protocolType === 'GENERIC') {
    // 泛化模式下
    // 清空API端点，保存到隐藏字段
    operation.apiEndpoint = '';
    // 清空API方法，由后端方法决定
    operation.apiMethod = '';
    // 清空后端类名和方法名
    backendClass.value = '';
    backendMethod.value = '';
    backendMethods.value = [];
  } else {
    // 未选择协议类型，清空相关字段
    operation.apiEndpoint = '';
    operation.applicationId = '';
    backendClasses.value = [];
    backendMethods.value = [];
    backendClass.value = '';
    backendMethod.value = '';
  }
});

// 加载查询列表
const loadQueries = async () => {
  try {
    isLoadingQueries.value = true;
    const result = await queryStore.fetchQueries();
    queries.value = result;
  } catch (error) {
    console.error('加载查询列表失败:', error);
  } finally {
    isLoadingQueries.value = false;
  }
};

// 加载应用列表
const loadApplications = async () => {
  try {
    isLoadingApplications.value = true;
    const result = await operationStore.fetchApplications();
    applications.value = result;
  } catch (error) {
    console.error('加载应用列表失败:', error);
  } finally {
    isLoadingApplications.value = false;
  }
};

// 加载操作详情
const loadOperation = async () => {
  try {
    const result = await operationStore.fetchOperationById(operationId.value);
    
    if (result) {
      // 更新操作对象
      operation.id = result.id;
      operation.name = result.name;
      operation.description = result.description || '';
      operation.type = result.type;
      operation.queryId = result.queryId;
      operation.protocolType = result.protocolType;
      operation.contentType = result.contentType;
      operation.applicationId = result.applicationId;
      operation.apiEndpoint = result.apiEndpoint;
      operation.apiMethod = result.apiMethod;
      operation.apiParameters = [...result.apiParameters];
      operation.requireConfirmation = result.requireConfirmation;
      operation.confirmationMessage = result.confirmationMessage || '确认执行此操作？';
      operation.successMessage = result.successMessage || '操作执行成功';
      operation.errorMessage = result.errorMessage || '操作执行失败';
      
      // 加载API和字段
      if (operation.queryId) {
        await fetchQueryFields();
      }
      
      // 泛化调用模式下，需要加载应用API
      if (operation.applicationId && operation.protocolType === 'GENERIC') {
        await fetchBackendClasses();
        
        // 从apiEndpoint解析类名和方法名（格式为"类名:方法名"）
        if (operation.apiEndpoint && operation.apiEndpoint.includes(':')) {
          const [className, methodName] = operation.apiEndpoint.split(':');
          backendClass.value = className;
          
          // 加载后端方法
          await fetchBackendMethods();
          
          // 设置方法名
          backendMethod.value = methodName;
        }
      }
    }
  } catch (error) {
    console.error('加载操作详情失败:', error);
  }
};

// 获取查询字段
const fetchQueryFields = async () => {
  if (!operation.queryId) return;
  
  try {
    const fields = await queryStore.getQueryFields(operation.queryId);
    queryFields.value = fields || [];
  } catch (error) {
    console.error('获取查询字段失败:', error);
    queryFields.value = [];
  }
};

// 获取后端类名
const fetchBackendClasses = async () => {
  if (!operation.applicationId || operation.protocolType !== 'GENERIC') {
    backendClasses.value = [];
    return;
  }
  
  try {
    isLoadingApplications.value = true;
    // 注意：这里直接使用用户输入的applicationId获取应用信息
    const app = await operationStore.fetchApplicationById(operation.applicationId.trim());
    if (app) {
      backendClasses.value = app.classes || [];
    } else {
      // 如果没有找到应用，清空API列表并提示用户
      backendClasses.value = [];
      operation.apiEndpoint = '';
      alert('未找到应用，请检查应用标识是否正确');
    }
  } catch (error) {
    console.error('获取后端类名失败:', error);
    backendClasses.value = [];
    operation.apiEndpoint = '';
    alert('获取后端类名失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    isLoadingApplications.value = false;
  }
};

// 获取后端方法名
const fetchBackendMethods = async () => {
  if (!backendClass.value || operation.protocolType !== 'GENERIC') {
    backendMethods.value = [];
    return;
  }
  
  try {
    isLoadingApplications.value = true;
    // 注意：这里直接使用用户输入的backendClass获取方法信息
    const methods = await operationStore.fetchMethodsByClass(backendClass.value);
    if (methods) {
      backendMethods.value = methods;
    } else {
      // 如果没有找到方法，清空方法列表并提示用户
      backendMethods.value = [];
      operation.apiEndpoint = '';
      alert('未找到方法，请检查后端类名是否正确');
    }
  } catch (error) {
    console.error('获取后端方法名失败:', error);
    backendMethods.value = [];
    operation.apiEndpoint = '';
    alert('获取后端方法名失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    isLoadingApplications.value = false;
  }
};

// 选择方法
const selectMethod = () => {
  if (!backendMethod.value || operation.protocolType !== 'GENERIC') return;
  
  const selectedMethod = backendMethods.value.find(method => method.name === backendMethod.value);
  
  if (selectedMethod) {
    // 设置API方法
    operation.apiMethod = selectedMethod.method;
    
    // 如果已有参数，询问是否替换
    if (operation.apiParameters.length > 0) {
      if (confirm('是否用API定义的参数替换当前参数？')) {
        operation.apiParameters = selectedMethod.parameters.map((param: any) => ({
          id: uuidv4(),
          name: param.name,
          type: param.type,
          required: param.required,
          defaultValue: param.defaultValue,
          description: param.description,
          mappingField: ''
        }));
      }
    } else {
      // 没有参数，直接使用API定义的参数
      operation.apiParameters = selectedMethod.parameters.map((param: any) => ({
        id: uuidv4(),
        name: param.name,
        type: param.type,
        required: param.required,
        defaultValue: param.defaultValue,
        description: param.description,
        mappingField: ''
      }));
    }
  }
};

// 添加参数
const addParameter = () => {
  operation.apiParameters.push({
    id: uuidv4(),
    name: '',
    type: 'string',
    required: false,
    mappingField: ''
  });
};

// 移除参数
const removeParameter = (index: number) => {
  operation.apiParameters.splice(index, 1);
};

// 表单提交
const submitForm = async () => {
  try {
    isSubmitting.value = true;
    
    // 根据协议类型检查必填字段
    if (operation.protocolType === 'HTTP') {
      // HTTP模式检查内容类型和API端点
      if (!operation.contentType) {
        alert('请选择内容类型');
        isSubmitting.value = false;
        return;
      }
      if (!operation.apiEndpoint) {
        alert('请输入API端点');
        isSubmitting.value = false;
        return;
      }
      // 确保HTTP模式下方法为POST
      operation.apiMethod = 'POST';
    } else if (operation.protocolType === 'GENERIC') {
      // 泛化模式检查应用ID、类名和方法名
      if (!operation.applicationId) {
        alert('请输入应用标识');
        isSubmitting.value = false;
        return;
      }
      if (!backendClass.value) {
        alert('请选择后端类名');
        isSubmitting.value = false;
        return;
      }
      if (!backendMethod.value) {
        alert('请选择后端方法名');
        isSubmitting.value = false;
        return;
      }
      
      // 确保应用标识没有前后空格
      operation.applicationId = operation.applicationId.trim();
      
      // 设置apiEndpoint字段为"类名:方法名"格式
      operation.apiEndpoint = `${backendClass.value}:${backendMethod.value}`;
      
      // 设置默认的apiMethod
      if (!operation.apiMethod) {
        operation.apiMethod = 'POST';  // 默认使用POST，或者从后端方法信息中获取
      }
    } else {
      alert('请选择协议类型');
      isSubmitting.value = false;
      return;
    }
    
    if (isEditing.value) {
      // 更新操作
      const updateData: UpdateOperationParams = {
        name: operation.name,
        description: operation.description,
        type: operation.type,
        queryId: operation.queryId,
        applicationId: operation.applicationId,
        protocolType: operation.protocolType,
        contentType: operation.contentType,
        apiEndpoint: operation.apiEndpoint,
        apiMethod: operation.apiMethod,
        apiParameters: operation.apiParameters,
        requireConfirmation: operation.requireConfirmation,
        confirmationMessage: operation.confirmationMessage,
        successMessage: operation.successMessage,
        errorMessage: operation.errorMessage
      };
      
      const result = await operationStore.updateOperation(operationId.value, updateData);
      
      if (result) {
        router.push({ name: 'OperationDetail', params: { id: operationId.value } });
      }
    } else {
      // 创建操作
      const result = await operationStore.createOperation(operation);
      
      if (result) {
        router.push({ name: 'OperationDetail', params: { id: result.id } });
      }
    }
  } catch (error) {
    console.error('保存操作失败:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// 返回
const goBack = () => {
  router.back();
};

// 获取查询名称
const getQueryNameById = (id: string) => {
  const query = queries.value.find(q => q.id === id);
  return query ? query.name : id;
};
</script> 