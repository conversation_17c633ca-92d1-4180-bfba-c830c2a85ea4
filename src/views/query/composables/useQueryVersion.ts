import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import type { VersionInfo } from '@/types/version';
import { fetchQueryVersions, createQueryVersion } from '@/api/version';

/**
 * 查询版本管理的可组合函数
 * 提供查询版本的加载、创建和状态管理功能
 */
export function useQueryVersion(queryId: string | null) {
  // 版本相关状态
  const versions = ref<VersionInfo[]>([]);
  const loadingVersions = ref(false);
  const creatingVersion = ref(false);
  const versionError = ref<string | null>(null);
  const lastDraftSaveAt = ref<string | null>(null);
  
  // 计算属性：最新版本
  const latestVersion = computed(() => {
    if (!versions.value || versions.value.length === 0) return null;
    return versions.value[0]; // 假设版本已按时间排序，最新的在第一位
  });
  
  // 计算属性：当前版本状态
  const versionStatus = computed(() => {
    if (loadingVersions.value) return 'loading';
    if (versionError.value) return 'error';
    if (versions.value && versions.value.length > 0) return 'loaded';
    return 'empty';
  });
  
  // 计算属性：格式化的最后保存时间
  const lastDraftSaveTime = computed(() => {
    if (!lastDraftSaveAt.value) return '';
    
    try {
      const date = new Date(lastDraftSaveAt.value);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      console.error('日期格式化错误:', e);
      return lastDraftSaveAt.value;
    }
  });
  
  /**
   * 加载查询版本列表
   */
  const loadVersions = async () => {
    if (!queryId) return;
    
    loadingVersions.value = true;
    versionError.value = null;
    
    try {
      const response = await fetchQueryVersions(queryId);
      versions.value = response.versions || [];
      
      if (versions.value.length > 0) {
        // 获取最新的草稿保存时间
        const latestDraft = versions.value.find(v => v.isDraft);
        if (latestDraft) {
          lastDraftSaveAt.value = latestDraft.createdAt;
        }
      }
    } catch (error) {
      console.error('加载查询版本失败:', error);
      versionError.value = '加载版本信息失败';
    } finally {
      loadingVersions.value = false;
    }
  };
  
  /**
   * 创建新的查询版本
   * @param isDraft 是否为草稿版本
   * @param queryData 查询数据，包含SQL或自然语言查询内容
   */
  const createVersion = async (isDraft: boolean, queryData: any) => {
    if (!queryId) {
      console.error('创建版本失败: 没有查询ID');
      return;
    }
    
    creatingVersion.value = true;
    
    try {
      const response = await createQueryVersion(queryId, {
        ...queryData,
        isDraft
      });
      
      // 更新版本列表
      if (response.version) {
        // 将新版本添加到版本列表前面
        versions.value = [response.version, ...versions.value];
        
        // 如果是草稿，更新最后草稿保存时间
        if (isDraft) {
          lastDraftSaveAt.value = response.version.createdAt;
          message.success('草稿已保存');
        } else {
          message.success('版本已创建');
        }
      }
      
      return response.version;
    } catch (error) {
      console.error('创建查询版本失败:', error);
      message.error('创建版本失败');
      return null;
    } finally {
      creatingVersion.value = false;
    }
  };
  
  /**
   * 保存草稿版本
   * @param queryData 查询数据
   */
  const saveDraft = async (queryData: any) => {
    return createVersion(true, queryData);
  };
  
  /**
   * 发布新版本
   * @param queryData 查询数据
   */
  const publishVersion = async (queryData: any) => {
    if (!queryData.queryId) {
      console.error('发布版本失败: 没有查询ID');
      message.error('无法发布未保存的查询');
      return null;
    }
    
    console.log('发布版本，使用查询ID:', queryData.queryId);
    return createVersion(false, queryData);
  };
  
  return {
    // 状态
    versions,
    loadingVersions,
    creatingVersion,
    versionError,
    lastDraftSaveAt,
    
    // 计算属性
    latestVersion,
    versionStatus,
    lastDraftSaveTime,
    
    // 方法
    loadVersions,
    createVersion,
    saveDraft,
    publishVersion
  };
}