const loadQueryById = async (id) => {
  try {
    loading.value = true;
    const res = await api.getQueryById(id);
    if (res.code === 200) {
      form.dataSourceId = res.data.dataSourceId;
      form.name = res.data.name;
      form.description = res.data.description;
      form.sql = res.data.sql;
      form.id = res.data.id;
      
      // 将SQL内容设置到全局变量中，确保在组件间传递
      console.log('LoadQueryView: 加载查询成功，SQL长度=', form.sql?.length || 0);
      window.__SQL_CONTENT__ = form.sql || '';
      
      // 等待数据源加载完成
      await nextTick();
      activeNode.value = {
        ...activeNode.value,
        queryId: id,
      };
      
      // 确保通知组件更新
      sqlContent.value = form.sql;
      
      // 延迟触发一次强制更新
      setTimeout(() => {
        const tempContent = form.sql;
        console.log('LoadQueryView: 延迟500ms后再次设置SQL内容，长度=', tempContent?.length || 0);
        sqlContent.value = '';
        nextTick(() => {
          sqlContent.value = tempContent;
          window.__SQL_CONTENT__ = tempContent;
        });
      }, 500);
      
      return true;
    }
    return false;
  } catch (error) {
    console.error('LoadQueryView: 加载查询出错:', error);
    return false;
  } finally {
    loading.value = false;
  }
}; 