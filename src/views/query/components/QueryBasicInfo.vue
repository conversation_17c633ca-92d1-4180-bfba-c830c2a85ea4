<template>
  <div class="query-basic-info">
    <a-form-item
      :validateStatus="queryNameError ? 'error' : ''"
      :help="queryNameError"
      class="mb-0"
    >
      <a-input
        v-model:value="queryName"
        placeholder="请输入查询名称"
        :disabled="loading"
        class="w-96"
        @blur="validateQueryName"
      />
    </a-form-item>

    <a-form-item
      :validateStatus="dataSourceError ? 'error' : ''"
      :help="dataSourceError"
      class="mb-0 mt-4"
    >
      <a-select
        v-model:value="selectedDataSourceId"
        :loading="loadingDataSources"
        placeholder="选择数据源"
        class="w-96"
        :disabled="loading || !dataSources.length"
        @change="handleDataSourceChange"
      >
        <a-select-option
          v-for="dataSource in dataSources"
          :key="dataSource.id"
          :value="dataSource.id"
          :disabled="dataSource.status !== 'ACTIVE'"
        >
          <span class="flex items-center">
            <span
              class="w-2 h-2 rounded-full mr-2"
              :class="{
                'bg-green-500': dataSource.status === 'ACTIVE',
                'bg-blue-500': dataSource.status === 'SYNCING',
                'bg-red-500': dataSource.status === 'ERROR',
                'bg-gray-500': dataSource.status === 'INACTIVE',
              }"
            ></span>
            {{ dataSource.name }}
          </span>
        </a-select-option>
      </a-select>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { DataSource } from '@/types/datasource';
import type { QueryBasicInfoProps } from '@/views/query/types/queryEditor';
import { fetchDataSources } from '@/api/dataSource';

const props = defineProps<QueryBasicInfoProps>();

const emit = defineEmits<{
  'update:queryName': [value: string];
  'update:selectedDataSourceId': [value: string];
  'validateQueryName': [];
}>();

// 状态管理
const queryName = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:queryName', value)
});

const selectedDataSourceId = computed({
  get: () => props.dataSourceId,
  set: (value) => emit('update:selectedDataSourceId', value)
});

const dataSources = ref<DataSource[]>([]);
const loadingDataSources = ref(false);
const queryNameError = ref('');
const dataSourceError = ref('');

// 验证查询名称
const validateQueryName = () => {
  if (!queryName.value) {
    queryNameError.value = '查询名称不能为空';
    return false;
  }

  queryNameError.value = '';
  return true;
};

// 验证数据源
const validateDataSource = () => {
  if (!selectedDataSourceId.value) {
    dataSourceError.value = '请选择数据源';
    return false;
  }

  dataSourceError.value = '';
  return true;
};

// 获取数据源列表
const getDataSources = async () => {
  loadingDataSources.value = true;
  try {
    const response = await fetchDataSources();
    dataSources.value = response.dataSources || [];
  } catch (error) {
    message.error('获取数据源列表失败');
    console.error('获取数据源列表失败:', error);
  } finally {
    loadingDataSources.value = false;
  }
};

// 数据源变更处理
const handleDataSourceChange = (value: string) => {
  if (value) {
    dataSourceError.value = '';
  }
};

// 监听加载状态，重置错误信息
watch(() => props.loading, (isLoading) => {
  if (isLoading) {
    queryNameError.value = '';
    dataSourceError.value = '';
  }
});

// 组件挂载时加载数据源
onMounted(() => {
  getDataSources();
});

// 对外暴露验证方法
defineExpose({
  validateQueryName,
  validateDataSource
});
</script>

<style scoped>
.query-basic-info {
  padding: 16px 0;
}
</style>