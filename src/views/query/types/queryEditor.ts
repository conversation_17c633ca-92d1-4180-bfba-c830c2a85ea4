import type { FormItemProps } from 'ant-design-vue';

// 查询编辑器标签页组件的属性接口
export interface QueryEditorTabsProps {
  // 内容相关
  sqlContent?: string;
  nlContent?: string;
  
  // 表单验证状态
  sqlValidateStatus?: FormItemProps['validateStatus'];
  sqlValidateMessage?: string;
  nlValidateStatus?: FormItemProps['validateStatus'];
  nlValidateMessage?: string;
  submitFailed?: boolean;
  
  // 交互状态
  disabled?: boolean;
  loading?: boolean;
  readOnly?: boolean;
  defaultActiveKey?: string;
}

// 查询编辑器头部组件的属性接口
export interface QueryEditorHeaderProps {
  // 页面标题相关
  isEditMode: boolean;
  queryName?: string;
  
  // 状态控制
  saveLoading: boolean;
  publishLoading: boolean;
  
  // 错误信息显示
  errorMessage?: string;
  showError: boolean;
}

// 数据源的基本结构
export interface DataSource {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'syncing' | 'error';
  type: string;
}

// 查询执行状态
export type QueryExecutionStatus = 'idle' | 'executing' | 'completed' | 'error' | 'cancelled';

// 查询数据结构
export interface QueryData {
  id?: string;
  name: string;
  dataSourceId: string;
  sql?: string;
  naturalLanguageQuery?: string;
  description?: string;
  queryType: 'sql' | 'natural-language';
  createdAt?: string;
  updatedAt?: string;
  tags?: string[];
  isPublished?: boolean;
}

// 保存查询时的数据结构
export interface SaveQueryData {
  name: string;
  description?: string;
  tags?: string[];
  isPublished?: boolean;
}

// 查询执行结果
export interface QueryResult {
  columns: Array<{ 
    name: string;
    type: string;
  }>;
  rows: any[];
  error?: string;
  executionTime?: number;
  totalRows?: number;
}

// 查询编辑器状态
export interface QueryEditorState {
  queryId: string | null;
  queryName: string;
  selectedDataSourceId: string;
  sqlContent: string;
  nlContent: string;
  description: string;
  tags: string[];
  isPublished: boolean;
  isEditMode: boolean;
  activeTab: 'sql' | 'natural-language';
  executionStatus: QueryExecutionStatus;
  submitFailed: boolean;
}

// 查询基础信息组件的属性接口
export interface QueryBasicInfoProps {
  modelValue: string;
  dataSourceId: string;
  loading?: boolean;
}