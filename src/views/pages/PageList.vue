<template>
  <div class="container mx-auto px-4 py-6">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">系统页面</h1>
        <button
          @click="createPage"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i class="fas fa-plus mr-2"></i>
          创建页面
        </button>
      </div>
    </div>

    <!-- 过滤器 -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索 -->
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
            搜索
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input
              id="search"
              v-model="searchText"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="搜索页面名称或描述"
              @keyup.enter="executeQuery"
            />
          </div>
        </div>

        <!-- 类型过滤 -->
        <div>
          <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">
            页面类型
          </label>
          <div class="relative select-container">
            <select
              id="type-filter"
              v-model="filterType"
              class="block w-full py-2 pl-3 pr-10 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option v-for="option in typeOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>

        <!-- 状态过滤 -->
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">
            状态
          </label>
          <div class="relative select-container">
            <select
              id="status-filter"
              v-model="filterStatus"
              class="block w-full py-2 pl-3 pr-10 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-end space-x-2">
          <button
            @click="clearFilters"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-sync-alt mr-2"></i>
            重置筛选
          </button>
          <button
            @click="executeQuery"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            :disabled="isLoading"
          >
            <i class="fas fa-search mr-2"></i>
            查询
          </button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white shadow rounded-lg">
      <!-- 加载中 -->
      <div v-if="loading" class="p-10 text-center">
        <i class="fas fa-circle-notch fa-spin text-indigo-500 text-3xl mb-4"></i>
        <p class="text-gray-500">正在加载页面列表...</p>
      </div>

      <!-- 无数据时 -->
      <div v-else-if="pages.length === 0" class="p-10 text-center">
        <div class="rounded-full bg-gray-100 h-16 w-16 flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
        </div>
        <h3 class="text-sm font-medium text-gray-900">暂无页面数据</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ searchText || filterType || filterStatus ? '没有符合筛选条件的页面' : '暂无数据' }}
        </p>
        <div class="mt-6">
          <button
            @click="createPage"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fas fa-plus mr-2"></i>
            创建页面
          </button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                标题
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                描述
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                标签
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="page in pages" :key="page.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-indigo-100 text-indigo-600">
                    <i class="fas" :class="page.type === 'data' ? 'fa-table' : page.type === 'analysis' ? 'fa-chart-bar' : 'fa-cogs'"></i>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-indigo-600 hover:text-indigo-800 cursor-pointer" @click="viewPage(page.id)">
                      {{ page.title }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-500 max-w-md truncate">
                  {{ page.description || '-' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                  {{ getTypeName(page.type) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getStatusClass(page.status)"
                >
                  {{ getStatusName(page.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="(tag, index) in (page.tags || []).slice(0, 2)"
                    :key="index"
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {{ tag }}
                  </span>
                  <span
                    v-if="(page.tags || []).length > 2"
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    +{{ page.tags.length - 2 }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end items-center space-x-1">
                  <button
                    @click="previewPage(page.id)"
                    class="text-blue-600 hover:text-blue-900 p-1.5 rounded hover:bg-blue-50"
                    title="预览"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                  <button
                    @click="goToEmbedPage(page.id)"
                    class="text-purple-600 hover:text-purple-900 p-1.5 rounded hover:bg-purple-50"
                    title="嵌入"
                  >
                    <i class="fas fa-code"></i>
                  </button>
                  <button
                    @click="editPage(page.id)"
                    class="text-indigo-600 hover:text-indigo-900 p-1.5 rounded hover:bg-indigo-50"
                    title="编辑"
                  >
                    <i class="fas fa-edit"></i>
                  </button>

                  <!-- 更多操作下拉菜单 -->
                  <div class="relative inline-block text-left">
                    <button
                      @click="toggleDropdown(page.id)"
                      class="text-gray-600 hover:text-gray-900 p-1.5 rounded hover:bg-gray-50"
                      title="更多操作"
                    >
                      <i class="fas fa-ellipsis-v"></i>
                    </button>

                    <!-- 下拉菜单 -->
                    <div
                      v-if="activeDropdown === page.id"
                      class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5" style="z-index: var(--z-dropdown) !important;"
                    >
                      <div class="py-1" role="menu" aria-orientation="vertical">
                        <button
                          @click="copyEmbedCode(page.id)"
                          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          role="menuitem"
                        >
                          <i class="fas fa-copy mr-2"></i> 复制嵌入代码
                        </button>
                        <button
                          @click="exportPageConfig(page.id)"
                          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          role="menuitem"
                        >
                          <i class="fas fa-download mr-2"></i> 导出配置
                        </button>
                        <div class="border-t border-gray-100 my-1"></div>
                        <button
                          @click="confirmDelete(page)"
                          class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                          role="menuitem"
                        >
                          <i class="fas fa-trash-alt mr-2"></i> 删除
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 底部分页 -->
      <Pagination
        v-if="totalPages > 0"
        :current-page="currentPage"
        :total-items="total"
        :page-size="pageSize"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  删除页面
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    确定要删除"{{ pageToDelete?.title }}"吗？此操作无法撤销。
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="deletePage"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              删除
            </button>
            <button
              @click="cancelDelete"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 嵌入代码模态框 -->
    <div v-if="embedModalVisible" class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                  <i class="fas fa-code text-indigo-500 mr-2"></i>
                  嵌入代码
                  <button @click="closeEmbedModal" class="ml-auto text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                  </button>
                </h3>

                <div class="mt-4 bg-blue-50 p-3 rounded border border-blue-200 text-blue-700 text-sm mb-4">
                  <i class="fas fa-info-circle mr-2"></i>
                  将以下代码复制到您的网页中，即可嵌入此页面
                </div>

                <div class="bg-gray-100 p-3 rounded relative mb-4">
                  <pre class="text-xs text-gray-800 whitespace-pre-wrap break-all">{{ embedCode }}</pre>
                  <button
                    @click="copyToClipboard(embedCode)"
                    class="absolute top-2 right-2 text-gray-500 hover:text-indigo-600 bg-white rounded-md p-1 shadow-sm"
                    title="复制代码"
                  >
                    <i class="fas fa-copy"></i>
                  </button>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">页面ID</label>
                    <input
                      type="text"
                      v-model="embedPageId"
                      disabled
                      class="bg-gray-100 border border-gray-300 rounded-md py-2 px-3 w-full text-sm"
                    />
                  </div>

                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">宽度</label>
                      <div class="flex items-center">
                        <input
                          type="number"
                          v-model="embedWidth"
                          min="300"
                          max="1920"
                          @change="updateEmbedCode"
                          class="border border-gray-300 rounded-md py-2 px-3 text-sm flex-1"
                        />
                        <select
                          v-model="embedWidthUnit"
                          @change="updateEmbedCode"
                          class="ml-2 border border-gray-300 rounded-md py-2 px-2 text-sm"
                        >
                          <option value="px">px</option>
                          <option value="%">%</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">高度</label>
                      <div class="flex items-center">
                        <input
                          type="number"
                          v-model="embedHeight"
                          min="200"
                          max="1200"
                          @change="updateEmbedCode"
                          class="border border-gray-300 rounded-md py-2 px-3 text-sm flex-1"
                        />
                        <select
                          v-model="embedHeightUnit"
                          @change="updateEmbedCode"
                          class="ml-2 border border-gray-300 rounded-md py-2 px-2 text-sm"
                        >
                          <option value="px">px</option>
                          <option value="%">%</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1">附加选项</label>
                  <div class="flex items-center space-x-4">
                    <label class="inline-flex items-center">
                      <input
                        type="checkbox"
                        v-model="embedShowHeader"
                        @change="updateEmbedCode"
                        class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span class="ml-2 text-sm text-gray-700">显示头部</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input
                        type="checkbox"
                        v-model="embedShowFooter"
                        @change="updateEmbedCode"
                        class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span class="ml-2 text-sm text-gray-700">显示页脚</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="closeEmbedModal"
              class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:w-auto sm:text-sm"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { usePageStore } from '@/stores/page';
import { message } from '@/services/message';
import { storeToRefs } from 'pinia';
import Pagination from '@/components/common/Pagination.vue';

// 定义页面类型接口
interface PageItem {
  id: string;
  title: string;
  description?: string;
  type: 'data' | 'analysis' | 'management';
  status: 'active' | 'inactive' | 'draft';
  tags?: string[];
  createdAt?: string;
}

// 路由相关
const router = useRouter();

// Store
const pageStore = usePageStore();
// 使用统一的消息服务
const { pages, loading, total } = storeToRefs(pageStore);

// 状态
const searchText = ref('');
const filterType = ref('');
const filterStatus = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const showDeleteConfirm = ref(false);
const pageToDelete = ref<PageItem | null>(null);
const embedModalVisible = ref(false);
const embedCode = ref('');
const embedPageId = ref('');
const embedWidth = ref(800);
const embedWidthUnit = ref('px');
const embedHeight = ref(600);
const embedHeightUnit = ref('px');
const embedShowHeader = ref(true);
const embedShowFooter = ref(true);

// 选项定义
const typeOptions = [
  { value: '', label: '全部类型' },
  { value: 'data', label: '数据页面' },
  { value: 'analysis', label: '分析页面' },
  { value: 'management', label: '管理页面' }
];

const statusOptions = [
  { value: '', label: '全部状态' },
  { value: 'active', label: '活跃' },
  { value: 'inactive', label: '禁用' },
  { value: 'draft', label: '草稿' }
];

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(total.value / pageSize.value) || 1;
});

// 生命周期钩子
onMounted(async () => {
  document.addEventListener('click', closeDropdowns);
  await fetchPages();

  // 在window上添加事件，组件卸载时不会自动移除，但这里影响很小
  window.addEventListener('beforeunload', () => {
    document.removeEventListener('click', closeDropdowns);
  });
});

// 获取页面列表
const fetchPages = async () => {
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: searchText.value,
      type: filterType.value || undefined,
      status: filterStatus.value || undefined
    };

    await pageStore.fetchPageList(params);

    if (pageStore.error) {
      message.error({
        content: '获取页面列表失败',
        description: pageStore.error,
        duration: 5000
      });
    } else if (pages.value.length === 0) {
      if (searchText.value || filterType.value || filterStatus.value) {
        message.info({
          content: '没有符合条件的页面',
          description: '当前筛选条件下未找到符合的页面，请尝试修改筛选条件',
          duration: 3000
        });
      }
    }
  } catch (error) {
    console.error('获取页面列表失败:', error);
    message.error({
      content: '获取页面列表失败',
      description: error instanceof Error ? error.message : '未知错误，请检查网络连接或稍后重试',
      duration: 5000
    });
  }
};

// 创建页面
const createPage = () => {
  router.push('/pages/create');
};

// 查看页面
const viewPage = (id: string) => {
  router.push(`/pages/view/${id}`);
};

// 编辑页面
const editPage = (id: string) => {
  router.push(`/pages/edit/${id}`);
};

// 预览页面
const previewPage = (id: string) => {
  try {
    // 确保ID存在且有效
    if (!id) {
      message.error({
        content: '无效的页面ID',
        description: '无法预览页面，查询参数不正确或页面ID不存在',
        duration: 5000
      });
      return;
    }

    // 获取当前域名和协议
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;

    // 定义子系统端口 - 固定使用3002端口，确保与环境变量一致
    const pageServicePort = '3002';
    const pageServicePath = (import.meta.env.VITE_PAGE_SERVICE_PATH as string) || '';

    // 构建完整的子系统URL，根据环境使用不同路径策略
    let targetUrl;
    if (import.meta.env.PROD) {
      // 生产环境可能使用路径前缀而非不同端口
      targetUrl = `${protocol}//${hostname}${pageServicePath}/viewer/${id}`;
    } else {
      // 开发环境固定使用3002端口
      targetUrl = `${protocol}//${hostname}:3002/viewer/${id}`;
    }

    console.log('预览URL:', targetUrl, '页面ID:', id);

    // 在新标签页中打开预览
    window.open(targetUrl, '_blank');
    message.info({
      content: '正在打开页面预览',
      description: '页面将在新窗口中打开，请确保浏览器未阻止弹出窗口',
      duration: 3000
    });
  } catch (error) {
    console.error('打开预览失败:', error);
    message.error({
      content: '打开预览页面失败',
      description: '无法打开预览页面，请确保页面存在并且服务端正常运行',
      duration: 5000
    });
  }
};

// 导出页面配置
const exportPageConfig = (id: string) => {
  // 关闭下拉菜单
  activeDropdown.value = '';

  // 请求页面配置
  pageStore.fetchPageConfig(id)
    .then((config: any) => {
      if (!config) {
        throw new Error('获取页面配置失败');
      }

      // 创建下载链接
      const dataStr = JSON.stringify(config, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `${config.title || 'page-config'}-${id}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();

      message.success({
        content: '页面配置已导出',
        description: `配置文件已保存为${exportFileDefaultName}`,
        duration: 3000
      });
    })
    .catch((error: unknown) => {
      console.error('导出页面配置失败:', error);
      message.error({
        content: '导出失败',
        description: error instanceof Error ? error.message : '未知错误，请确保有文件下载权限或稍后重试',
        duration: 5000
      });
    });
};

// 确认删除
const confirmDelete = (page: PageItem) => {
  pageToDelete.value = page;
  showDeleteConfirm.value = true;
};

// 执行删除
const deletePage = async () => {
  if (!pageToDelete.value) return;

  try {
    await pageStore.deletePage(pageToDelete.value.id);
    message.success({
      content: '页面已成功删除',
      description: '页面及相关配置已从系统中移除',
      duration: 3000
    });
    showDeleteConfirm.value = false;
    pageToDelete.value = null;
    fetchPages(); // 刷新列表
  } catch (error) {
    console.error('删除页面失败:', error);
    message.error({
      content: '删除页面失败',
      description: error instanceof Error ? error.message : '未知错误，请检查网络连接或稍后重试',
      duration: 5000
    });
  }
};

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  pageToDelete.value = null;
};

// 清除筛选
const clearFilters = () => {
  searchText.value = '';
  filterType.value = '';
  filterStatus.value = '';
  currentPage.value = 1;
};

const isLoading = ref(false);

// 执行查询
const executeQuery = async () => {
  currentPage.value = 1; // 重置为第一页
  isLoading.value = true
  await fetchPages();
  isLoading.value = false
};

// 页码变更处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchPages();
};

// 获取状态标签样式
const getStatusClass = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'inactive':
      return 'bg-gray-100 text-gray-800';
    case 'draft':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// 获取状态名称
const getStatusName = (status: string) => {
  switch (status) {
    case 'active': return '活跃';
    case 'inactive': return '禁用';
    case 'draft': return '草稿';
    default: return status;
  }
};

// 获取类型名称
const getTypeName = (type: string) => {
  switch (type) {
    case 'data': return '数据页面';
    case 'analysis': return '分析页面';
    case 'management': return '管理页面';
    default: return type;
  }
};

// 格式化日期
const formatDate = (dateInput: string | undefined) => {
  if (!dateInput) return '-';

  let date;
  if (typeof dateInput === 'string') {
    date = new Date(dateInput);
  } else {
    return '-';
  }

  // 验证日期是否有效
  if (isNaN(date.getTime())) {
    return '-';
  }

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 嵌入页面
const goToEmbedPage = (id: string) => {
  embedPageId.value = id;
  embedModalVisible.value = true;
  updateEmbedCode();
};

// 复制嵌入代码
const copyEmbedCode = (id: string) => {
  embedPageId.value = id;
  updateEmbedCode();
  embedModalVisible.value = true;

  // 关闭下拉菜单
  activeDropdown.value = '';
};

// 更新嵌入代码
const updateEmbedCode = () => {
  const baseUrl = window.location.origin;
  const width = `${embedWidth.value}${embedWidthUnit.value}`;
  const height = `${embedHeight.value}${embedHeightUnit.value}`;
  const showHeader = embedShowHeader.value ? 'true' : 'false';
  const showFooter = embedShowFooter.value ? 'true' : 'false';

  embedCode.value = `<iframe
  src="${baseUrl}/embed/pages/${embedPageId.value}?header=${showHeader}&footer=${showFooter}"
  width="${width}"
  height="${height}"
  frameborder="0"
  allowfullscreen
></iframe>`;
};

// 关闭嵌入代码模态框
const closeEmbedModal = () => {
  embedModalVisible.value = false;
};

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      message.success({
        content: '已复制到剪贴板',
        description: '内容已成功复制，可以直接粘贴使用',
        duration: 3000
      });
    })
    .catch((err) => {
      console.error('无法复制到剪贴板:', err);
      message.error({
        content: '复制失败',
        description: '无法使用剪贴板功能，请手动选择文本并复制',
        duration: 5000
      });
    });
};

// 切换下拉菜单
const activeDropdown = ref('');
const toggleDropdown = (id: string) => {
  if (activeDropdown.value === id) {
    activeDropdown.value = '';
  } else {
    activeDropdown.value = id;
  }
};

// 点击其他区域关闭下拉菜单
const closeDropdowns = (event: MouseEvent) => {
  if (activeDropdown.value && !(event.target as HTMLElement).closest('.relative')) {
    activeDropdown.value = '';
  }
};
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 500;
}

.search-filter-section {
  margin-bottom: 24px;
}

.search-filter-form {
  padding: 0;
}

.filter-buttons {
  display: flex;
  justify-content: flex-end;
}

.data-list-section {
  margin-bottom: 24px;
}

.data-loading {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-icon {
  color: #409EFF;
}

.tag-item {
  margin-right: 5px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.embed-code-container {
  margin-top: 20px;
  position: relative;
}

.embed-code-container .ant-btn {
  margin-top: 10px;
}
</style>
