<script setup lang="ts">
import { ref, onMounted, watch, computed, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DataSourceList from '@/components/datasource/DataSourceList.vue'
import DataSourceForm from '@/components/datasource/DataSourceForm.vue'
import DataSourceDetail from '@/components/datasource/DataSourceDetail.vue'
import AdvancedSearch from '@/components/datasource/AdvancedSearch.vue'
import SearchResultsView from '@/components/datasource/SearchResultsView.vue'
import type { DataSource, CreateDataSourceParams, ConnectionTestResult, DataSourceQueryParams, TestConnectionParams } from '@/types/datasource'
import { useDataSourceStore } from '@/stores/datasource'
import { message } from '@/services/message'
import { DataSourceTypeEnum, DataSourceStatusEnum, type DataSourceType, type DataSourceStatus } from '@/types/datasource'

// 路由相关
const route = useRoute()
const router = useRouter()

// 数据源状态管理 - 将这行移到上面，确保在其他地方使用前已初始化
const dataSourceStore = useDataSourceStore()

const isLoading = ref(false)

// 视图状态管理
interface ViewState {
  isLoading: boolean
  isSearchOpen: boolean
  searchKeyword: string
  searchResults: {
    tables: Array<{
      dataSourceId: string
      dataSourceName: string
      tables: Array<{
        name: string
        type: string
        schema: string
      }>
    }>
    columns: Array<{
      dataSourceId: string
      dataSourceName: string
      columns: Array<{
        table: string
        column: string
        type: string
      }>
    }>
    views: Array<{
      dataSourceId: string
      dataSourceName: string
      views: Array<{
        name: string
        type: string
        schema: string
      }>
    }>
    total: number
  } | null
  searchParams: {
    keyword: string
    dataSourceIds: string[]
    entityTypes: Array<'table' | 'column' | 'view'>
    useRegex?: boolean
    caseSensitive?: boolean
  }
  selectedDataSource: DataSource | null
  shouldRefresh: boolean // 添加标记，用于表示是否需要刷新数据
}

const viewState = reactive<ViewState>({
  isLoading: false,
  isSearchOpen: false,
  searchKeyword: '',
  searchResults: null,
  searchParams: {
    keyword: '',
    dataSourceIds: [],
    entityTypes: ['table', 'column', 'view']
  },
  selectedDataSource: null,
  shouldRefresh: false // 初始化为false
})

// 用于过滤表单的状态
const currentFilters = ref({
  name: '',
  type: '' as DataSourceType | '',
  status: '' as DataSourceStatus | ''
})

// 视图状态
const currentView = ref<'list' | 'detail' | 'form' | 'search' | 'searchResults'>('list')

// 处理过滤器变化
const handleFilterChange = async () => {
  isLoading.value = true
  await refreshDataSources({
    page: 1,
    size: 10,
    name: currentFilters.value.name || undefined,
    type: currentFilters.value.type || undefined,
    status: currentFilters.value.status || undefined
  })
  isLoading.value = false
}

// 重置过滤器
const resetFilters = () => {
  currentFilters.value = {
    name: '',
    type: '',
    status: ''
  }
  debouncedRefreshDataSources({
    page: 1,
    size: 10
  })
}

// 刷新数据源列表
const refreshDataSources = async (params?: any) => {
  // 如果视图正在加载中，不要重复刷新
  if (viewState.isLoading) {
    console.log('[DataSourceView] 当前正在加载中，忽略刷新请求');
    return;
  }

  console.log('[DataSourceView] 刷新数据源列表，接收到的参数:', params);

  // 构建标准查询参数对象
  const queryParams: DataSourceQueryParams = {
    page: params?.page || 1,
    size: parseInt(params?.size) || 10 // 确保将size转为数字
  };

  // 只有当参数有值时才添加过滤条件
  if (params?.name) queryParams.name = params.name;
  if (params?.type) queryParams.type = params.type;
  if (params?.status) queryParams.status = params.status;

  console.log('[DataSourceView] 传递给store的查询参数:', queryParams);

  // 设置loading状态
  viewState.isLoading = true;
  try {
    await dataSourceStore.fetchDataSources(queryParams);
  } catch (error) {
    console.error('加载数据源列表失败:', error);
    message.error('加载数据源列表失败: ' + (error instanceof Error ? error.message : '未知错误'));
  } finally {
    viewState.isLoading = false;
  }
};

// 创建防抖版本的刷新函数，避免短时间内多次刷新
const debouncedRefreshDataSources = (() => {
  let timer: number | null = null;
  return (params?: any) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      timer = null;
      refreshDataSources(params);
    }, 100) as unknown as number;
  };
})();

// 监听路由和搜索状态变化 - 重写这部分以避免直接调用dataSourceStore
watch([() => route.name, () => viewState.isSearchOpen, () => viewState.searchResults], () => {
  if (viewState.isSearchOpen) {
    currentView.value = viewState.searchResults ? 'searchResults' : 'search'
    return
  }

  switch (route.name) {
    case 'datasource-create':
    case 'datasource-edit':
      currentView.value = 'form'
      break
    case 'datasource-detail':
      currentView.value = 'detail'
      break
    default:
      currentView.value = 'list'
      // 标记需要刷新，但不直接调用刷新方法
      if (dataSourceStore && !dataSourceStore.dataSources.length && !viewState.isLoading) {
        console.log('[DataSourceView] 列表页初始化 - 标记需要刷新');
        viewState.shouldRefresh = true; // 设置标记，后续在安全的时机刷新
      }
  }
}, { immediate: true })

// 计算编辑模式
const isEditMode = computed(() => route.name === 'datasource-edit')

// 导航方法
const showListView = () => router.push({ name: 'datasource-list' })
const showDetailView = (dataSource: DataSource) => router.push({ name: 'datasource-detail', params: { id: dataSource.id } })
const showAddForm = () => {
  viewState.selectedDataSource = null
  router.push({ name: 'datasource-create' })
}
const showEditForm = (dataSource: DataSource) => router.push({ name: 'datasource-edit', params: { id: dataSource.id } })

// 根据路由参数初始化视图
const initializeView = async () => {
  const { name, params } = route

  switch (name) {
    case 'datasource-create':
      showAddForm()
      break
    case 'datasource-edit':
      if (params.id) {
        // 总是从API获取最新的数据源数据，确保编辑页面显示最新信息
        console.log('[DataSourceView] 从API获取最新数据源数据', params.id);
        try {
          const dataSource = await dataSourceStore.getDataSourceById(params.id as string, true) // 强制刷新
          if (dataSource) {
            viewState.selectedDataSource = dataSource
            showEditForm(dataSource)
          } else {
            message.error('未找到数据源')
            router.push({ name: 'datasource-list' })
          }
        } catch (error) {
          console.error('获取数据源详情失败:', error)
          message.error('获取数据源详情失败')
          router.push({ name: 'datasource-list' })
        }
      }
      break
    case 'datasource-detail':
      if (params.id) {
        // 总是从API获取最新的数据源数据，确保详情页面显示最新信息
        console.log('[DataSourceView] 从API获取最新数据源数据', params.id);
        try {
          const dataSource = await dataSourceStore.getDataSourceById(params.id as string, true) // 强制刷新
          if (dataSource) {
            viewState.selectedDataSource = dataSource
            showDetailView(dataSource)
          } else {
            message.error('未找到数据源')
            router.push({ name: 'datasource-list' })
          }
        } catch (error) {
          console.error('获取数据源详情失败:', error)
          message.error('获取数据源详情失败')
          router.push({ name: 'datasource-list' })
        }
      }
      break
    case 'datasource-list':
      // 列表页不需要在这里加载数据，因为已经在路由监听中处理了
      break
    default:
      // 其他情况不做额外处理
      break
  }
}

// 监听路由变化 - 只对路由名称和参数变化时触发初始化视图，但不自动加载数据
// 使用debounce优化，避免短时间内多次触发
const debouncedInitView = (() => {
  let timer: number | null = null;
  return (...args: any[]) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      timer = null;
      initializeView();
    }, 50) as unknown as number;
  };
})();

watch([() => route.name, () => route.params], debouncedInitView, { immediate: true })

// 显示高级搜索面板
const showAdvancedSearch = () => {
  viewState.isSearchOpen = true
  router.push({ name: 'datasource-search', query: { search: 'advanced' } })
}

// 关闭高级搜索面板
const closeAdvancedSearch = () => {
  viewState.isSearchOpen = false
  router.push({ name: 'datasource-list' })
}

// 执行高级搜索
const performAdvancedSearch = async (params: {
  keyword: string
  dataSourceIds: string[]
  entityTypes: Array<'table' | 'column' | 'view'>
  useRegex?: boolean
  caseSensitive?: boolean
}) => {
  viewState.isLoading = true

  try {
    const results = await dataSourceStore.advancedSearch(params)
    viewState.searchResults = results
    viewState.searchParams = {
      keyword: params.keyword,
      dataSourceIds: params.dataSourceIds,
      entityTypes: params.entityTypes,
      useRegex: params.useRegex,
      caseSensitive: params.caseSensitive
    }
  } catch (error) {
    console.error('搜索失败:', error)
    message.error('搜索失败: ' + (error instanceof Error ? error.message : '未知错误'))
    viewState.searchResults = null
    viewState.searchParams = {
      keyword: '',
      dataSourceIds: [],
      entityTypes: ['table', 'column', 'view']
    }
  } finally {
    viewState.isLoading = false
  }
}

// 查看搜索结果中的表
const handleViewSearchResultTable = (dataSourceId: string, tableName: string) => {
  // 首先获取数据源信息
  const dataSource = dataSourceStore.dataSources.find(ds => ds.id === dataSourceId)

  if (dataSource) {
    viewState.selectedDataSource = dataSource
    currentView.value = 'detail'

    // 这里可以添加逻辑，让详情页面直接定位到指定表并展开
    setTimeout(() => {
      const event = new CustomEvent('view-table', {
        detail: { tableName }
      })
      window.dispatchEvent(event)
    }, 100)
  } else {
    message.error('未找到数据源信息')
  }
}

// 同步元数据
const syncDataSourceMetadata = async (dataSource: DataSource) => {
  try {
    await dataSourceStore.syncDataSourceMetadata(dataSource.id)
  } catch (error) {
    console.error('同步元数据失败:', error)
  }
}

// 测试数据源连接
const testDataSourceConnection = async (params: TestConnectionParams, callback: (success: boolean, result?: ConnectionTestResult) => void): Promise<void> => {
  try {
    // 构造符合 TestConnectionParams 的参数
    const testParams: TestConnectionParams = {
      id: params.id, // 确保保留id参数
      type: params.type,
      host: params.host,
      port: params.port,
      database: params.database || params.databaseName, // 兼容两种参数名
      username: params.username,
      password: params.password || '',
      connectionParams: params.connectionParams || {}
    };

    console.log('测试连接参数:', testParams);

    // 调用store的测试连接方法
    const result = await dataSourceStore.testDataSourceConnection(testParams);
    console.log('视图层接收到测试连接结果:', result);

    // 回调函数返回结果
    callback(result.success, result);
  } catch (error) {
    console.error('测试连接发生错误:', error);

    // 检查错误是否与HTML响应有关
    const errorMsg = error instanceof Error ? error.message : String(error);
    if (errorMsg.includes('HTML而非JSON') || errorMsg.includes('<!DOCTYPE') || errorMsg.includes('<html')) {
      message.error({
        content: '测试连接失败: 服务器返回了非法响应，可能需要重新登录',
        duration: 5
      });
    } else {
      message.error('测试连接失败: ' + errorMsg);
    }

    callback(false, {
      success: false,
      message: errorMsg
    });
  }
}

// 保存数据源
const saveDataSource = async (dataSource: CreateDataSourceParams) => {
  viewState.isLoading = true

  try {
    if (isEditMode.value && viewState.selectedDataSource) {
      // 编辑现有数据源
      await dataSourceStore.updateDataSource({
        ...dataSource,
        id: viewState.selectedDataSource.id
      })
    } else {
      // 创建新数据源
      await dataSourceStore.createDataSource(dataSource)
    }

    // 返回到列表视图
    showListView()

    // 刷新数据源列表，使用防抖版本
    await debouncedRefreshDataSources()

  } catch (error) {
    console.error('保存数据源失败:', error)
    message.error('保存数据源失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    viewState.isLoading = false
  }
}

// 删除数据源
const deleteDataSource = async (dataSource: DataSource) => {
  // 防止重复提交
  if (viewState.isLoading) return;

  viewState.isLoading = true;
  console.log(`开始删除数据源: ${dataSource.id}`);

  try {
    // 调用 store 的删除方法，避免直接发起 HTTP 请求
    await dataSourceStore.deleteDataSource(dataSource.id);

    // 删除成功后，更新视图状态
    console.log('数据源删除成功');
    message.success('数据源删除成功');

    // 返回列表视图 - 但不主动刷新，避免多次请求
    // 如果有必要，可以在跳转前更新本地状态
    router.push({ name: 'datasource-list' });
    currentView.value = 'list';
  } catch (error) {
    console.error('删除数据源失败:', error);

    // 检查错误是否与HTML响应有关
    const errorMsg = error instanceof Error ? error.message : String(error);
    if (errorMsg.includes('HTML而非JSON') || errorMsg.includes('<!DOCTYPE') || errorMsg.includes('<html')) {
      message.error({
        content: '删除数据源失败: 服务器返回了非法响应，可能需要重新登录',
        duration: 5
      });
    } else {
      message.error('删除数据源失败: ' + errorMsg);
    }
  } finally {
    viewState.isLoading = false;
  }
};

// 组件挂载 - 只执行一次，在这里处理刷新逻辑
onMounted(async () => {
  // 检查之前标记的刷新状态，确保安全地执行刷新
  if (viewState.shouldRefresh || !dataSourceStore.dataSources.length) {
    viewState.isLoading = true;
    try {
      await dataSourceStore.fetchDataSources();
      viewState.shouldRefresh = false; // 重置标记
    } catch (error) {
      console.error('加载数据源列表失败:', error);
      message.error('加载数据源列表失败');
    } finally {
      viewState.isLoading = false;
    }
  }
})
</script>

<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 页面标题和操作按钮 -->
    <div class="md:flex md:items-center md:justify-between mb-6">
      <div class="flex-1 min-w-0">
        <h1 class="text-2xl font-bold text-gray-900">
          <template v-if="currentView === 'list'">数据源管理</template>
          <template v-else-if="currentView === 'detail' && viewState.selectedDataSource">{{ viewState.selectedDataSource.name }} - 详情</template>
          <template v-else-if="currentView === 'form'">{{ isEditMode ? '编辑数据源' : '添加数据源' }}</template>
          <template v-else-if="currentView === 'search'">高级搜索</template>
          <template v-else-if="currentView === 'searchResults'">搜索结果</template>
        </h1>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <template v-if="currentView === 'list'">
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            @click="showAddForm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            添加数据源
          </button>
        </template>

        <template v-else>
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            @click="showListView"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回列表
          </button>
        </template>
      </div>
    </div>

    <!-- 过滤区域 -->
    <div v-if="currentView === 'list'" class="bg-white shadow rounded-lg mb-6 p-4">
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4 items-end">
        <div class="md:col-span-3">
          <label for="search-keyword" class="block text-sm font-medium text-gray-700 mb-1">关键词</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input
              id="search-keyword"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              :placeholder="currentFilters.name ? '' : '搜索数据源名称...'"
              v-model="currentFilters.name"
            />
          </div>
        </div>

        <div class="md:col-span-3">
          <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">数据源类型</label>
          <div class="relative select-container">
            <select
              id="type-filter"
              v-model="currentFilters.type"
              class="block w-full pl-3 pr-10 py-2 border border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option value="">所有类型</option>
              <option :value="DataSourceTypeEnum.MYSQL">MySQL</option>
              <option :value="DataSourceTypeEnum.DB2">DB2</option>
              <option :value="DataSourceTypeEnum.POSTGRESQL">PostgreSQL</option>
              <option :value="DataSourceTypeEnum.ORACLE">Oracle</option>
              <option :value="DataSourceTypeEnum.SQLSERVER">SQL Server</option>
              <option :value="DataSourceTypeEnum.MONGO">MongoDB</option>
              <option :value="DataSourceTypeEnum.ELASTICSEARCH">Elasticsearch</option>
              <option :value="DataSourceTypeEnum.OTHER">其他</option>
            </select>
          </div>
        </div>

        <div class="md:col-span-3">
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <div class="relative select-container">
            <select
              id="status-filter"
              v-model="currentFilters.status"
              class="block w-full pl-3 pr-10 py-2 border border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md appearance-none"
            >
              <option value="">不限</option>
              <option :value="DataSourceStatusEnum.ACTIVE">活跃</option>
              <option :value="DataSourceStatusEnum.INACTIVE">不活跃</option>
              <option :value="DataSourceStatusEnum.ERROR">错误</option>
              <option :value="DataSourceStatusEnum.SYNCING">同步中</option>
            </select>
          </div>
        </div>

        <div class="md:col-span-3 flex justify-end space-x-2">
          <button
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            @click="resetFilters"
          >
            <i class="fas fa-sync-alt mr-2"></i>
            重置筛选
          </button>
          <button
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            @click="handleFilterChange"
            :disabled="isLoading"
          >
            <i class="fas fa-search mr-2"></i>
            查询
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="bg-white shadow rounded-lg">
      <!-- 列表视图 -->
      <template v-if="currentView === 'list'">
        <DataSourceList
          :dataSources="dataSourceStore.dataSources"
          :loading="viewState.isLoading"
          :showActions="true"
          :pagination="dataSourceStore.pagination"
          @refresh="debouncedRefreshDataSources"
          @edit="showEditForm"
          @delete="deleteDataSource"
          @view="showDetailView"
          @select="showDetailView"
          @add="showAddForm"
          @test-connection="testDataSourceConnection"
          @sync-metadata="syncDataSourceMetadata"
        />
      </template>

      <!-- 详情视图 -->
      <div v-else-if="currentView === 'detail' && viewState.selectedDataSource">
        <DataSourceDetail
          :data-source-id="viewState.selectedDataSource.id"
          @edit="showEditForm"
          @delete="deleteDataSource"
          @refresh="debouncedRefreshDataSources"
          @test-connection="testDataSourceConnection"
          @sync-metadata="syncDataSourceMetadata"
        />
      </div>

      <!-- 表单视图 -->
      <div v-else-if="currentView === 'form'">
        <DataSourceForm
          :data-source="viewState.selectedDataSource"
          :is-edit="isEditMode"
          @save="saveDataSource"
          @cancel="showListView"
          @test-connection="testDataSourceConnection"
        />
      </div>

      <!-- 高级搜索视图 -->
      <div v-else-if="currentView === 'search'">
        <AdvancedSearch
          :initial-keyword="viewState.searchKeyword"
          :selected-data-source-id="viewState.selectedDataSource?.id"
          @search="performAdvancedSearch"
          @close="closeAdvancedSearch"
        />
      </div>

      <!-- 搜索结果视图 -->
      <div v-else-if="currentView === 'searchResults' && viewState.searchResults">
        <SearchResultsView
          :results="viewState.searchResults"
          :search-params="viewState.searchParams"
          :is-loading="viewState.isLoading"
          @back="closeAdvancedSearch"
          @retry="performAdvancedSearch(viewState.searchParams)"
        />
      </div>
    </div>

    <!-- 加载指示器 -->
    <div
      v-if="viewState.isLoading"
      class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
    >
      <div class="bg-white p-6 rounded-lg shadow-xl flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mr-3"></div>
        <span class="text-gray-700">加载中...</span>
      </div>
    </div>
  </div>
</template>
